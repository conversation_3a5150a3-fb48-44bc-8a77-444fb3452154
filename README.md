# Groweasy Backend

A one-stop AI platform for launching and optimizing Meta and Google ads.

## Local Setup

1. Create a meta app

- Login to facebook
- visit https://developers.facebook.com/apps/
- Select other and press Next
- Select app type as Business and press Next
- Fill in app name and press "Create app"

2. To access the Marketing API, you need to generate a User access token for your app and ask for the ads_management permission

- Make sure that your personal facebook profile has an ad account associated with it
- Visit https://developers.facebook.com/tools/explorer
- Select Meta app (the one which you created)
- Select "User Token" in User of Page dropdown
- Select these permissions: ads_management, ads_read, pages_read_engagement, pages_show_list, pages_manage_metadata, leads_retrieval, pages_manage_ads
- Hit "Generate Access Token" button and authorize
- Come back and copy the access token
- This token will only be valid for few hours, if you want to get long lived token, use below curl

```
curl --location 'https://graph.facebook.com/v18.0/oauth/access_token?grant_type=fb_exchange_token&client_id=<APP_ID>&client_secret=<APP_SECRET>&fb_exchange_token=<ACCESS_TOKEN>'
```

3. Get access token: https://www.npmjs.com/package/facebook-nodejs-business-sdk
4. Create business manager account: https://www.facebook.com/business/help/****************?id=***************
5. To Install node canvas-

```
brew install pkg-config cairo pango libpng jpeg giflib librsvg
```

## Marketing API Resources

1. Check ads running on Meta: https://www.facebook.com/ads/library
2. Leads ads creation: https://developers.facebook.com/docs/marketing-api/guides/lead-ads/create
3. Meta marketing API get started: https://developers.facebook.com/docs/marketing-apis/get-started
4. Ad previews: https://developers.facebook.com/docs/marketing-api/generatepreview/v18.0
5. Ad Creative Texts: https://www.facebook.com/business/help/***************?id=***************
6. Fetch Ad Image: https://developers.facebook.com/docs/marketing-api/reference/ad-image/
7. Create Ad Image: https://developers.facebook.com/docs/marketing-api/reference/ad-account/adimages/
8. Ad Targeting: https://developers.facebook.com/docs/marketing-api/audiences/reference/basic-targeting
9. Placement Assets Customization: https://developers.facebook.com/docs/marketing-api/dynamic-creative/placement-asset-customization
10. Enhancing Creative Images: https://developers.facebook.com/docs/marketing-api/advantage-catalog-ads/standard-enhancements/
11. Retrieve Leads: https://developers.facebook.com/docs/marketing-api/guides/lead-ads/retrieving/
12. Leads CRM integration: https://developers.facebook.com/docs/marketing-api/guides/lead-ads/
13. Campaign Details: https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/
14. Campaign Insights: https://developers.facebook.com/docs/marketing-api/insights
15. Ad Set Budget: https://developers.facebook.com/docs/marketing-api/adset/budget-limits/v18.0
16. Lead Ad Terms: https://www.facebook.com/ads/leadgen/tos
17. Flexible Targeting: https://developers.facebook.com/docs/marketing-api/audiences/reference/flexible-targeting/
18. Lead Ads Testing tool: https://developers.facebook.com/tools/lead-ads-testing?__mref=message_bubble
19. Targeting Search: https://developers.facebook.com/docs/marketing-api/audiences/reference/targeting-search/
20. Get Facebook Page ID: https://www.facebook.com/help/1503421039731588
21. Rate Limit: https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management
22. Request Advanced Access: https://developers.facebook.com/docs/graph-api/overview/access-levels/#advanced-access
23. Meta Business Verification: https://developers.facebook.com/docs/development/release/business-verification
24. Webhooks for Leads: https://developers.facebook.com/docs/graph-api/webhooks/getting-started/webhooks-for-leadgen/
25. Ad Rules API: https://developers.facebook.com/docs/marketing-api/ad-rules/guides/api-calls
26. Ad Rules Overview: https://developers.facebook.com/docs/marketing-api/ad-rules/overview
27. Ad Rules Evaluation Spec: https://developers.facebook.com/docs/marketing-api/ad-rules/overview/evaluation-spec
28. Debug an access token: https://developers.facebook.com/tools/debug/accesstoken/

## OpenAI Resources

1. Count tokens: https://tiktokenizer.vercel.app/
2. Pricing: https://openai.com/pricing

## Deploymemt to ECS

1. docker build -t groweasy-be .
2. docker run -t -i -p 4000:4000 groweasy-be
3. aws ecr get-login-password --region ap-south-1 --profile bannerbot | docker login --username AWS --password-stdin ************.dkr.ecr.ap-south-1.amazonaws.com
4. docker tag groweasy-be:latest ************.dkr.ecr.ap-south-1.amazonaws.com/groweasy-be:latest
5. docker push ************.dkr.ecr.ap-south-1.amazonaws.com/groweasy-be:latest

### Docker Debugging

1. `docker ps`
2. `docker exec -it f2a24bcf78a2 sh`

## AWS Resources

1. SES: https://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/ses-examples-sending-email.html
2. Env variables: https://docs.aws.amazon.com/sdkref/latest/guide/environment-variables.html

## Google Ads

Docs

1. Lead Form: https://support.google.com/google-ads/answer/9423234
2. API: https://developers.google.com/google-ads/api/docs/start
3. SDK: https://github.com/Opteo/google-ads-api
4. Lead form Webhook: https://developers.google.com/google-ads/webhook/docs/implementation

Steps to get API token-

1. Create a manager Account (diffenert from an Ad account): https://ads.google.com/home/<USER>/manager-accounts/
   A manager account lets you easily view and manage multiple Google Ads accounts, including other manager accounts, from a single location. It enables you to create and manage campaigns, compare performance etc

2. Link your Ad account to this manager account
   https://support.google.com/google-ads/answer/7459601

3. Obtain a developer token
   https://developers.google.com/google-ads/api/docs/get-started/dev-token

4. Apply for Basic Access
5. Set up a Google API Console project
   https://developers.google.com/google-ads/api/docs/get-started/oauth-cloud-project

6. Enable the Google Ads API in your project
7. Get a refresh Token: https://developers.google.com/google-ads/api/docs/get-started/make-first-call?authuser=1

Resources-

1. Test manager account: https://developers.google.com/google-ads/api/docs/best-practices/test-accounts?authuser=1#create
2. Download CMD tool oauth21- `brew install oauth2l`
3. Generate refresh token

```
oauth2l fetch --credentials google-ads-credentials.json --scope "adwords, youtube" --output_format refresh_token
```

Reset if you want to reset cache `oauth2l reset`

Append these query params `prompt=consent&access_type=offline` to the generated URL and open in browser.
Extract the code from redirected URL and paste it on terminal.

4. API Overview: https://developers.google.com/google-ads/api/docs/concepts/overview?authuser=1
5. API structure: https://developers.google.com/google-ads/api/docs/concepts/api-structure?authuser=1
6. REST Interface: https://developers.google.com/google-ads/api/rest/overview?authuser=1
7. Asset Requirements: https://developers.google.com/google-ads/api/docs/performance-max/asset-requirements?authuser=1
8. Location Query builder: https://developers.google.com/google-ads/api/fields/v15/geo_target_constant_query_builder

```
SELECT geo_target_constant.country_code, geo_target_constant.id, geo_target_constant.name, geo_target_constant.canonical_name FROM geo_target_constant WHERE geo_target_constant.name LIKE 'garhwa%'
```

9. Campaign Targeting: https://developers.google.com/google-ads/api/samples/add-campaign-targeting-criteria

Form to fill when there is change in tool behaviour-

https://support.google.com/adspolicy/contact/tool_change

10. Campaign Query: https://developers.google.com/google-ads/api/reference/rpc/v17/Campaign
11. Entity relationships diagram: https://developers.google.com/google-ads/api/docs/concepts/entity-relationships
12. Proto: https://github.com/googleapis/googleapis/blob/master/google/ads/googleads/v18/common/asset_types.proto
13. Assets Requirements: https://developers.google.com/google-ads/api/performance-max/asset-requirements
14. Conversion Actions: https://developers.google.com/google-ads/api/docs/conversions/create-conversion-actions
15. Search keywords (themes text) policies: https://support.google.com/adspolicy/answer/143465?hl=en

### Google Support

https://support.google.com/google-ads/gethelp

## Facebook Ads

1. Ads Referral URLs: https://developers.facebook.com/docs/app-ads/install-referrer/
2. Google Play Install Referrer: https://developer.android.com/google/play/installreferrer
3. Install Referrer using Flutter: https://pub.dev/packages/android_play_install_referrer

## Partners Leads submission via Webhook

1. Create a queue `leads-from-meta-webhook`: https://ap-south-1.console.aws.amazon.com/sqs/v3/home
2. Note the Queue URL: https://sqs.ap-south-1.amazonaws.com/************/leads-from-meta-webhook

## Click to Whatsapp Resources

1. https://developers.facebook.com/docs/whatsapp/cloud-api/get-started-for-tech-providers
2. https://business.whatsapp.com/products/create-ads-that-click-to-whatsapp
3. https://developers.facebook.com/docs/whatsapp/embedded-signup
4. https://developers.facebook.com/docs/whatsapp/business-management-api/guides/set-up-webhooks
5. https://developers.facebook.com/docs/whatsapp/embedded-signup/embed-the-flow
6. https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks/payload-examples/
7. https://developers.facebook.com/docs/whatsapp/cloud-api/guides/send-messages/#interactive-messages
8. https://developers.facebook.com/docs/whatsapp/guides/interactive-messages/
9. https://developers.facebook.com/docs/whatsapp/flows/reference/flowjson#routing-rules
10. Link Page to WhatsApp Business App: https://faq.whatsapp.com/***************/?cms_platform=android
11. CTWA via API: https://developers.facebook.com/docs/marketing-api/ad-creative/messaging-ads/click-to-whatsapp/

## Launch campaign using Bannerbot

1. Duplicate a campaign
2. Rename if necessary
3. In code, use BANNERBOT_INSTAGRAM_ID & BANNERBOT_PAGE_ID
4. Launch and assign uid if necessary

## Conversion API Resources

1. https://www.facebook.com/business/help/****************?id=***************
2. https://developers.facebook.com/docs/marketing-api/conversions-api/business-messaging
3. https://developers.facebook.com/docs/marketing-api/conversions-api/get-started#integration-methods
4. https://developers.facebook.com/docs/marketing-api/offline-conversions/#extern-id
5. https://developers.facebook.com/docs/marketing-api/conversions-api/parameters

### Conversion Event setup steps-

1. Go to Meta Events Manager.
2. Click Connect data sources and select Messaging
3. Click Connect
4. Choose Facebook Page option
5. Select Facebook Page Id
6. In "Set up with existing data" Dialog, choose groweasy-dataset
7. Select Instagram Account and choose BCN
8. Click on Start Integration
9. Review and click on Close
10. In "Choose a Path" dialog, Select direct integration
11. Select Events Purchase & LeadSubmitted
12. Select params e.g. Phone

### System User access tokens

1. https://developers.facebook.com/docs/marketing-api/system-users/create-retrieve-update
2. https://developers.facebook.com/docs/marketing-api/system-users/install-apps-and-generate-tokens#TOS
3. For now, we are using Admin user access long lived token having expiry = 60 days
4. Check ## Local Setup, Step 2 in README above
5. Last generated: Sun 2 June, 2:40 AM (renew in 60 days)
6. https://business.facebook.com/settings/system-users/**************?business_id=*************** to generate no expiry admin access token

### Cloudflare

1. To redirect www to non www, add page rule: https://dash.cloudflare.com/1a1a8dd59c278699640f988eba6c28fa/groweasy.ai/rules

### Logging

CloudWatch Log Insights filter

```
filter @logStream = 'ecs/groweasy-be/2a1d8c83d26c4ba1a02c444dea17dd95'
 | filter @message like "/api/meta/leads-webhook"
 | fields @timestamp, @message
```

To show all error logs-

```
{$.level = 50}
```

To search for all webhook attempts, search by Page Id, e.g.

```
***************
```

### Facebook App

1. Android key hashes: https://stackoverflow.com/questions/27448684/android-facebook-sdk-generate-release-key-hash

### Microsoft for Startups

1. Portal login: https://foundershub.startups.microsoft.com/
2. Check Credit balance: https://www.microsoftazuresponsorships.com/Balance

### Microsoft Azure

How to deploy an OpenAI model-

1. Go to https://portal.azure.com/#home
2. Click on plus button "Create a resource"
3. Choose "Azure AI services" - OpenAI
4. Click on Create
5. Decide region: https://learn.microsoft.com/en-us/azure/ai-services/openai/concepts/models#model-summary-table-and-region-availability
6. Check pricing here: https://azure.microsoft.com/en-in/pricing/details/cognitive-services/
7. Azure OpenAI Pricing: https://azure.microsoft.com/en-in/pricing/details/cognitive-services/openai-service/
8. Once deployment is done, Go to https://ai.azure.com/resource?tid=ac41f952-c989-4978-9816-a263626dccd5
9. Select the newly created resource
10. Go to "Model catalog"
11. Select a model, e.g. gpt-4o-mini
12. Click Deploy
13. Choose RPM (requests per minute) and click confirm
14. Not the API endpoint, e.g. https://groweasy-ai-eastus.cognitiveservices.azure.com/openai/deployments/gpt-4o-mini/chat/completions?api-version=2023-03-15-preview
15. Check REST API docs: https://learn.microsoft.com/en-in/azure/ai-services/openai/reference

```
curl --location 'https://groweasy-ai-eastus.cognitiveservices.azure.com/openai/deployments/gpt-4o-mini/chat/completions?api-version=2023-03-15-preview' \
--header 'api-key: AZURE_GPT_4O_MINI_API_KEY' \
--header 'Content-Type: application/json' \
--data '{
 "messages": [
    {
        "content": "Tell me a joke in hindi",
        "role": "system",
        "response_format": {
            "type": "json_object"
        }
    }
 ]
}'
```

### Meta Support

1. https://www.facebook.com/help/contact/***************
2. Raise support ticket: https://business.facebook.com/business-support-home/?landing_page=account_overview

### Meta - Rotate a Page

1. Create a new Facebook Page
2. Replace the default Page ID (BCN) in BE & FE, You can find Page ID here: https://business.facebook.com/latest/settings/pages
3. Todo: while launching a campaign, if page id in campaign config matches with any of the old Page Ids, replace with new one
4. Make sure that System user has access to this page
5. Also accept Leadgen TOS
6. Visit https://developers.facebook.com/tools/explorer and Reconnect, select newly created Page
7. Once GrowEasy app has access to Page, generate a Page token
8. Install App to Page using API `POST https://graph.facebook.com/NEW_PAGE_ID/subscribed_apps`
9. Verify that it has been installed using API `GET https://graph.facebook.com/NEW_PAGE_ID/subscribed_apps`
10. Now the lead Webhook should work properly
11. No need of Instagram as we are not mentioning it explicitly in Meta APIs

### Meta - Rotate an ad account

1. Make it prepaid
2. Add Default beneficiary and payer to target EU countries
3. GST details

### Remotion

https://www.remotion.dev/

1. Lambda Setup: https://www.remotion.dev/docs/lambda/setup
2. Render video using CLI

```
npx remotion lambda render https://designeasy.ai/fp-videos-v2/index.html p3 --region ap-south-1
```

3. Concurrency quota: https://ap-south-1.console.aws.amazon.com/servicequotas/home/<USER>/lambda/quotas/L-B99A9384
4. Render video using Node.js: https://www.remotion.dev/docs/lambda/setup#11-render-a-video

To upload bundle to s3 run this in frontend

```
npx remotion lambda sites create src/remotion/index.ts --site-name=fp-videos-templates --region=ap-south-1 --privacy=no-acl

```

current s3 url of bundle
https://remotionlambda-apsouth1-joi73j7qws.s3.ap-south-1.amazonaws.com/sites/fp-videos-templates/index.html

To render video using the s3 run the command

```
npx remotion lambda render fp-videos-templates p0 --privacy=no-acl --log=verbose // -> 0.007
npx remotion lambda render fp-videos-templates p0 --privacy=no-acl --log=verbose --frames-per-lambda=50 // -> 0.005
npx remotion lambda render fp-videos-templates p0 --privacy=no-acl --log=verbose --frames-per-lambda=100 // -> 0.005 or 0.006
```

the `--log=verbose` is for getting logs

reduced the number of lambda servers opened by setting the number of frames each server handles

```
npx remotion lambda render fp-videos-templates p4 --privacy=no-acl --log=verbose --frames-per-lambda=180 --
concurrency-per-lambda=2
```

this is done since we are getting delayRender() error while loading videos in loop
now the loop problem is solved by passing the video length as a prop

## future cost cutting for remotion:

```
npx remotion lambda render fp-videos-templates p0 --privacy=no-acl --log=verbose // -> 0.007$
npx remotion lambda render fp-videos-templates p0 --privacy=no-acl --log=verbose --frames-per-lambda=50 // -> 0.005$
npx remotion lambda render fp-videos-templates p0 --privacy=no-acl --log=verbose --frames-per-lambda=100 // -> 0.005 or 0.006$
```

- reduce the size of ram of the lambda server as per the need

### Xendit Resources

1. https://docs.xendit.co/payment-link/payment-channels
2. https://developers.xendit.co/api-reference/#create-invoice
3. https://github.com/xendit/xendit-node/blob/master/docs/Invoice.md

### Tiktok APIs

1. Docs Home: https://business-api.tiktok.com/portal/docs
2. JS SDK: https://github.com/tiktok/tiktok-business-api-sdk/tree/main/js_sdk
3. Campaign Structure: https://business-api.tiktok.com/portal/docs?id=1739381193120770
4. WhatsApp campaign: https://business-api.tiktok.com/portal/docs?id=1774482999036930
5. Phone Call: https://business-api.tiktok.com/portal/docs?id=1774482936048641
6. Smart+ Campaign: https://business-api.tiktok.com/portal/docs?id=1768006268820546
7. To get long lived token: https://business-api.tiktok.com/portal/auth?app_id=7532017506723037200&state=your_custom_params&redirect_uri=https%3A%2F%2Fgroweasy.ai (Change app id to prod one, PS: app cannot be shared to other users)
8.
