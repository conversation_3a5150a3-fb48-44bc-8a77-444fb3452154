# WABA Integration

## Tech Provider vs Tech Partners vs Solution Partners

Docs

1. https://business.whatsapp.com/partners/become-a-partner#feature-matrix
2. https://developers.facebook.com/docs/whatsapp/cloud-api/get-started-for-tech-providers

Third party developers must onboard first as a Tech Provider before upgrading to Tech Partner.
Tech Providers who meet specified requirements will be eligible to self-initiate the Tech Partner upgrade process.

Tech Partners are third party developers who build value added solutions on top of the WhatsApp Business Platform. These solutions can be offered independently or as part of a joint solution with a Solution Partner.

Solution Partners offer end-to-end solutions for businesses which includes driving awareness of WhatsApp Business Platform solutions, providing integration services, and offering customer support. Only Solution Partners can extend a line of credit to businesses for payment.

## Resources-

1. Install Fb SDK: https://developers.facebook.com/docs/javascript
2. https://developers.facebook.com/docs/facebook-login/web/accesstokens
3. https://developers.facebook.com/docs/whatsapp/embedded-signup/embed-the-flow/#step-2--create-facebook-login-for-business-configuration
4. https://developers.facebook.com/docs/whatsapp/embedded-signup/pre-filled-data
5. https://developers.facebook.com/docs/whatsapp/embedded-signup/pre-verified-numbers
6. https://developers.facebook.com/docs/whatsapp/business-management-api/get-started#business-integration-system-user-access-tokens
7. https://docs.genesys.com/Documentation/GSE/latest/SMSolution/DepWhatsAppEmb
8. https://www.postman.com/meta/workspace/whatsapp-business-platform/documentation/13382743-6da4508c-c36d-4e4c-bf74-a6fb41755c26
9. https://developers.facebook.com/docs/whatsapp/cloud-api/reference/registration/

## Access Tokens

1. If you are a direct developer, meaning only you or your business will be accessing your own data, use a System User access token.
2. If you are a Tech Provider, use a Business Integration System User access token.
3. If you are a Solution Partner, use System User access tokens to share your line of credit with newly onboarded customers, and Business Integration System User access tokens for everything else.

- System users can be employees or admins.
- The main advantage of these tokens is that they do not expire.

## Webhook Subscription

Assign a system user and use that token for-

```
curl -X POST \
'https://graph.facebook.com/v20.0/102289599326934/subscribed_apps' \
-H 'Authorization: Bearer EAAJi...'
```

## Solution Partner Onboarding

1. Create a Partner Solution (get Partner App Id from Haptik)
2. Create a new WhatsApp Webhook URL to be shared with partner (Haptik)
3. Once patner request is approved, partner will share API key
4. Accept Credit Line API T&C: https://business.facebook.com/billing_hub/credit_lines/details?business_id=***************&placement=standalone&credit_line_id=****************
5. In Embedded signup JS code, add `solutionID` under `extras.setup`
6. Make sure that app (GrowEasy meta app) is subscribed to the account_updates webhooks field
7. We'll get a code post signup, using that code extract waba id
8. Then call below API

```
curl --location 'http://api.interakt.ai/v1/organizations/tp-signup/' \
--header 'Authorization: INTERAKT_API_KEY' \
--header 'Content-Type: application/json' \
--data '{
    "entry": [
      {
        "changes": [
          {
            "value": {
              "event": "PARTNER_ADDED",
              "waba_info": {
                "waba_id": "***************",
                "solution_id": "****************"
              }
            }
          }
        ]
      }
    ],
    "object": "tech_partner"
  }'
```

Note that since we are using solution partner, we do not need to call below APIs manually-

1. GET `${META_BASE_URL}/${wabaId}/phone_numbers`
2. POST `${META_BASE_URL}/${phoneNumberId}/register`
3. POST `https://graph.facebook.com/${objectId}/subscribed_apps`

Above APIs are needed only when GrowEasy does not want to use a solution partner.

Partner API `http://api.interakt.ai/v1/organizations/tp-signup/` is wrapper of above APIs + credit line attachment

API docs: https://documenter.getpostman.com/view/14760594/2sA2r9X4Kb
https://documenter.getpostman.com/view/34061444/2sA3kSp4EG

Co-existence: https://developers.facebook.com/docs/whatsapp/embedded-signup/custom-flows/onboarding-business-app-users/
