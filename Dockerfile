# Check out https://hub.docker.com/_/node to select a new base image
# Use Node.js 18 Alpine as the base image
FROM --platform=linux/amd64 node:24-alpine

# Install system dependencies for Puppeteer and FFmpeg
RUN apk add --update --no-cache \
  # General dependencies
  make \
  g++ \
  # Puppeteer dependencies
  chromium \
  nss \
  freetype \
  harfbuzz \
  ca-certificates \
  ttf-freefont \
  # FFmpeg and other media dependencies
  ffmpeg \
  # Additional dependencies for graphics and fonts
  jpeg-dev \
  cairo-dev \
  giflib-dev \
  pango-dev \
  libtool \
  autoconf \
  automake

# Set environment variables for Puppeteer
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Create and set the app directory
ARG CODE_SOURCE=/home/<USER>/app
RUN mkdir -p $CODE_SOURCE
WORKDIR $CODE_SOURCE

# Bundle app source
COPY . $CODE_SOURCE

# Install app dependencies
RUN yarn install

# Build the app
RUN yarn run build

# Bind to all network interfaces so that it can be mapped to the host OS
ENV HOST=0.0.0.0 PORT=4000

# Expose the app port
EXPOSE ${PORT}

# Start the app
CMD [ "yarn", "run", "start" ]