## GrowEasy Partners (White labeling solution)

### Non Proxy Way

Follow these for non reverse proxy way-

#### Create a new Record (Partner's Side) -

Record Name: leads.bannerbot.xyz
Record type: CNAME
Value: groweasy-public-971273785.ap-south-1.elb.amazonaws.com

#### GrowEasy Side-

Request a new Public SSL Certificate for leads.bannerbot.xyz from AWS Certificate Manager

1. Share the CNAME type record key and value
2. Partner needs to create this entry in their DNS settings
3. leads.bannerbot.xyz \_c8bcf12023cb5f0a8ddcc23c3303ec30.leads.bannerbot.xyz. \_e3ca26b8af6c2931743546a42bbfd32a.mhbtsbpdnt.acm-validations.aws.
4. ai.zendot.in \_3115cbd6f79ed8a288b4c2b21cb9978f.ai.zendot.in. \_3f3786935c841915fb51698e7f0231f5.sdgjtdhdhz.acm-validations.aws.
5. Once the certificate is verified and issued, go to AWS ELB
6. Go to the Listeners tab and edit the listener to add HTTPS
7. Select the new ACM certificate and attach it to ELB
8. Whitelist partner domain in Firebase Auth
9. Whitelist partner domain in Backend for CORS

### Working of White Label Solution:

1. User Login via Mobile:

- Custom claims in Firebase store the user's email and partner details.
- The /users/profile API is used to update user information.
- userProfile is created in this scenario just like GrowEasy, populating email & name from custom claims (auth middleware)
- In case user logins via Email, just partner is set in custom claims

2. Frontend:

- The UI dynamically adjusts based on the host domain.

3. Backend:

- Parameters change (Razorpay credentials, Ad Acc, Page Id) based on the partner details from Firebase custom claims.
- Use Admin API /user-custom-claims/:uid for updating custom claims

### Nginx reverse Proxy Way

1. Create a subdomain leads.bannerbot.xyz on Route53
2. Create a A record pointing to `*************`
3. Update below entry in nginx `ssh -i bannerbot-ec2.pem ubuntu@*************` && `sudo vi /etc/nginx/sites-enabled/default`

```
server {

        server_name leads.bannerbot.xyz;

        location / {
                proxy_pass https://groweasy.ai/;
                proxy_http_version 1.1;
                proxy_ssl_server_name on;
                proxy_ssl_session_reuse on;
                proxy_ssl_name $proxy_host;
		            #proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                #proxy_set_header Upgrade $http_upgrade;
                #proxy_set_header Connection 'upgrade';
                #proxy_set_header Host $host; #Adding this is causing 403 by Cloudflare so usnge x-forwarded-host header in FE
		            proxy_set_header X-Forwarded-Host $host;
	              #proxy_set_header Host $proxy_host;
		            #proxy_set_header X-Real-IP $remote_addr;
                #proxy_cache_bypass $http_upgrade;
        }

        listen 80;
        listen [::]:80;

}
```

4. Check if configuration is successful `sudo nginx -t`
5. Obtain new certificate `sudo certbot run`
6. Restart nginx `sudo service nginx restart`
7. Check status `sudo service nginx status`
8. Change CORS config in the Backend
9. Whitelist domains on Google o-auth & firebase auth
10. Create partner config in Frontend

### Full nginx file for reference-

```
server {

	server_name bannerbot.xyz www.bannerbot.xyz;

	#root /var/www/example.com;
	#index index.html;

	location / {
		proxy_pass http://localhost:4000;
		proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_cache_bypass $http_upgrade;
	}




    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/bannerbot.xyz/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/bannerbot.xyz/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}

server {

        server_name leads.bannerbot.xyz ai.nividasoftware.com ai.zendot.in;

        location / {
                proxy_pass https://groweasy.ai/;
                proxy_http_version 1.1;
		proxy_ssl_server_name on;
		proxy_ssl_session_reuse on;
		proxy_ssl_name $proxy_host;
		#proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                #proxy_set_header Upgrade $http_upgrade;
                #proxy_set_header Connection 'upgrade';
                #proxy_set_header Host $host; #Adding this is causing 403 by Cloudflare so usnge x-forwarded-host header in FE
		proxy_set_header X-Forwarded-Host $host;
	        #proxy_set_header Host $proxy_host;
		#proxy_set_header X-Real-IP $remote_addr;
                #proxy_cache_bypass $http_upgrade;
        }


    listen [::]:443 ssl ipv6only=on; # managed by Certbot
    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/bannerbot.xyz/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/bannerbot.xyz/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot






}

server {
    if ($host = www.bannerbot.xyz) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    if ($host = bannerbot.xyz) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


	listen 80;
	listen [::]:80;

	server_name bannerbot.xyz www.bannerbot.xyz;
    return 404; # managed by Certbot

}



server {
    if ($host = leads.bannerbot.xyz) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    if ($host = ai.nividasoftware.com) {
        return 301 https://$host$request_uri;
    }


        server_name leads.bannerbot.xyz ai.nividasoftware.com;

        listen 80;
        listen [::]:80;
    return 404; # managed by Certbot




}
```
