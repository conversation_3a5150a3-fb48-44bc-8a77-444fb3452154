import { type DocumentData, Timestamp } from 'firebase-admin/firestore';
import crypto from 'crypto';
import fs, { unlink } from 'fs';
import axios, { type AxiosError } from 'axios';
import sharp from 'sharp';
import { type Express } from 'express';

import { db } from '../modules/firebase_admin_sdk';
import {
  AD_GLOBAL_AI_ADMINS,
  BANNERBOT_S3_PUBLIC_BUCKET_NAME,
  FIRESTORE_COLLECTIONS,
  GOOGLE_AD_ACCOUNT_ID,
  GOOGLE_ADS_BASE_URL,
  GROWEASY_ADMINS,
  GROWEASY_MAIN_AD_ACC_ID,
  META_BASE_URL,
  S3_UPLOADED_ASSETS_DIR,
  TIKTOK_API_BASE_URL,
  TIKTOK_AD_ACCOUNT_ID,
  type INCREMENTAL_COUNTER_FIELDS,
} from '../constants';
import {
  type ILeadgenFormQuestion,
  type ICampaign,
  type IGeoLocations,
  type ICampaignInsightDetails,
  type IGoogleCampaignInsights,
  GROWEASY_CAMPAIGN_TYPE,
  type IBudgetAndScheduling,
  Currency,
  type ICurrencyBudget,
  type IFlexibleTargetingItem,
  type ITiktokCampaignInsights,
} from '../types/campaign_details';
import { type IMetaLead, type IParsedLead } from '../types/leads';
import {
  type IIncrementalCounter,
  type IAuthUser,
  type IUserProfile,
  type AdPlatforms,
} from '../types';
import { type IOrderDetails } from '../types/payments_invoices';
import EU_COUNTRIES from '../constants/eu_countries';
import logger from '../modules/logger';
import parsePhoneNumberFromString from 'libphonenumber-js';
import { type IBannerTemplate } from '../types/banner_template';
import templatesCollection1 from '../constants/templates';
import templatesCollection2 from '../constants/templates_set2';
import { uploadFileToS3 } from '../modules/aws/s3/index';
import {
  getCpcoNodeForMetaSales,
  getCurrencySymbol,
  getUpdatedCloudflareImageUrl,
  getUpdatedUnsplashImageUrl,
} from '../utils';

const accessToken =
  process.env.GROWEASY_LONG_LIVED_ACESS_TOKEN ??
  process.env.SANDBOX_AD_ACCOUNT_ACCESS_TOKEN ??
  '';
const tiktokAccessToken = process.env.TIKTOK_LONG_LIVED_ACCESS_TOKEN ?? '';

const googleAdsTokenCache: {
  accessToken: string | null;
  expiry: number | null;
} = {
  accessToken: null,
  expiry: null,
};

export const getMetaUrl = (endpoint: string): URL => {
  const url = new URL(endpoint);
  url.searchParams.set('access_token', accessToken);
  // disabling because of error: You can't run lead ads until your Facebook Page accepts Facebook's Lead Generation ToS
  // url.searchParams.set('appsecret_proof', appSecretProof);
  return url;
};

export const getGoogleAdsUrl = (
  endpoint: string,
  adAccountId?: string,
): URL => {
  const url = new URL(
    `${GOOGLE_ADS_BASE_URL}/${adAccountId ?? GOOGLE_AD_ACCOUNT_ID}/${endpoint}`,
  );
  return url;
};

export const getTiktokApiUrl = (
  endpoint: string,
  adAccountId?: string,
): URL => {
  const url = new URL(`${TIKTOK_API_BASE_URL}/${endpoint}`);
  url.searchParams.set('advertiser_id', adAccountId ?? TIKTOK_AD_ACCOUNT_ID);
  return url;
};

export const getGoogleOauthUserAccessToken = async (): Promise<
  string | null
> => {
  // Check if token is cached and still valid (not expired)
  if (
    googleAdsTokenCache.accessToken &&
    googleAdsTokenCache.expiry &&
    googleAdsTokenCache.expiry > Date.now()
  ) {
    return googleAdsTokenCache.accessToken;
  }

  // Otherwise, request a new token
  const response = await axios.post(
    'https://www.googleapis.com/oauth2/v3/token',
    new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: process.env.GOOGLE_ADS_REFRESH_TOKEN ?? '',
      client_id: process.env.GOOGLE_ADS_OAUTH_CLIENT_ID ?? '',
      client_secret: process.env.GOOGLE_ADS_OAUTH_CLIENT_SECRET ?? '',
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } },
  );

  // Set token and expiry (1 hour in milliseconds)
  const tokenExpiryTime = response.data.expires_in * 1000;
  googleAdsTokenCache.accessToken = response.data.access_token;
  googleAdsTokenCache.expiry = Date.now() + tokenExpiryTime;

  return googleAdsTokenCache.accessToken;
};

export const getGoogleAdsHeaders = async (): Promise<
  Record<string, string>
> => {
  const accessToken = await getGoogleOauthUserAccessToken();
  return {
    'Content-Type': 'application/json',
    'developer-token': process.env.GOOGLE_ADS_DEVELOPER_TOKEN ?? '',
    'login-customer-id': process.env.GOOGLE_ADS_MANAGER_ACCOUNT_ID ?? '',
    Authorization: `Bearer ${accessToken}`,
  };
};

export const getTiktokApiHeaders = (): Record<string, string> => {
  return {
    'Content-Type': 'application/json',
    'Access-Token': tiktokAccessToken,
  };
};

export const getSingleCampaign = async (
  uid: string,
  id: string,
): Promise<DocumentData | null> => {
  const snapshot = await db
    .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
    .where('uid', '==', `${uid}`)
    .where('id', '==', id)
    .get();
  return snapshot.docs[0] ?? null;
};

export const updateFirestoreCampaign = async (
  firestoreDocId: string,
  campaign: Partial<ICampaign>,
): Promise<void> => {
  await db
    .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
    .doc(firestoreDocId)
    .set(campaign, {
      // caution: shallow merge only
      merge: true,
    });
};

export const chunkArray = (arr: any[], batchSize: number): any[][] => {
  const result = [];
  for (let i = 0; i < arr.length; i += batchSize) {
    result.push(arr.slice(i, i + batchSize));
  }
  return result;
};

export const compareSignatures = (
  signatureA: string,
  signatureB: string,
): boolean => {
  const bufferA = Buffer.from(signatureA, 'hex');
  const bufferB = Buffer.from(signatureB, 'hex');

  // Use crypto.timingSafeEqual to compare buffers in a timing-safe manner
  return crypto.timingSafeEqual(bufferA, bufferB);
};

export const getParseLead = (
  metaLead: IMetaLead,
  formQuestions: ILeadgenFormQuestion[],
): IParsedLead => {
  const parsedLead: IParsedLead = {
    created_time: metaLead.created_time,
    field_data: [],
  };
  metaLead.field_data.forEach((data) => {
    const matchingFormQuestion = formQuestions.find(
      (question) => question.key === data.name,
    );
    if (matchingFormQuestion?.options?.length) {
      // extract value from MCQs
      let value = data.values?.[0];
      // todo investigate: item.values sometimes contain value in lowercase, and some time key
      value =
        matchingFormQuestion.options.find(
          (optionItem) =>
            optionItem.key === value ||
            optionItem.value?.toLowerCase() === value.toLowerCase(),
        )?.value ?? '';
      data.values = [value];
    }
    parsedLead.field_data.push({
      ...data,
      ...matchingFormQuestion,
    });
  });
  return parsedLead;
};

export const isGrowEasyAdmin = (user?: IAuthUser): boolean => {
  if (user?.email && GROWEASY_ADMINS.includes(user.email)) {
    return true;
  }
  return false;
};

export const isAdGlobalAiAdmin = (user?: IAuthUser): boolean => {
  if (user?.email && AD_GLOBAL_AI_ADMINS.includes(user.email)) {
    return true;
  }
  return false;
};

export const getOrderDetails = async (
  razorpayOrderId: string,
): Promise<IOrderDetails | null> => {
  const snapshot = await db
    .collection(FIRESTORE_COLLECTIONS.ORDERS)
    .where('razorpay_order_id', '==', razorpayOrderId)
    .get();
  return (snapshot.docs[0]?.data() as IOrderDetails) ?? null;
};

export const getIncrementalCounter = async (
  field: INCREMENTAL_COUNTER_FIELDS,
): Promise<number> => {
  const docRef = db
    .collection(FIRESTORE_COLLECTIONS.INCREMENTAL_COUNTERS)
    .doc(field);
  // retireve value, update it and then return updated one
  const documentSnapshot = await docRef.get();
  const counter = documentSnapshot.data() as IIncrementalCounter;
  let updatedCount = 1;
  if (counter) {
    updatedCount = counter.count + 1;
  }
  // update back and then return
  await docRef.set(
    {
      count: updatedCount,
      updated_at: Timestamp.now(),
    },
    {
      // caution: shallow merge only
      merge: true,
    },
  );
  return updatedCount;
};

export const getSanitizedValueForWhatsappTemplate = (
  value?: string,
): string => {
  if (!value) {
    return '';
  }
  /// Remove special characters such as #, $, or %
  let sanitizedValue = value.replace(/[#$%]/g, '');

  // Replace \n with space, \t with space, and merge multiple spaces into one
  sanitizedValue = sanitizedValue
    .replace(/\n/g, ' ')
    .replace(/\t/g, ' ')
    .replace(/\s+/g, ' ');

  // Trim to 100 characters and add ellipsis if necessary
  if (sanitizedValue.length > 100) {
    return sanitizedValue.substring(0, 100) + '...';
  }

  return sanitizedValue;
};

export const isTargetingContainsEuCountries = (
  geoLocations: IGeoLocations,
): boolean => {
  const euCountryCodes = EU_COUNTRIES.map((item) => item.code);
  for (const key in geoLocations) {
    const objectKey = key as keyof IGeoLocations;
    const locationDetails = geoLocations[objectKey] ?? [];
    for (const item of locationDetails) {
      if (typeof item === 'object' && item.country) {
        if (euCountryCodes.includes(item.country)) {
          return true;
        }
      } else if (typeof item === 'string') {
        if (euCountryCodes.includes(item)) {
          return true;
        }
      }
    }
  }
  return false;
};

export const fsUnlink = async (filePath: string): Promise<void> => {
  await new Promise<void>((resolve, reject) => {
    unlink(filePath, (error) => {
      if (error) reject(error);
      resolve();
    });
  });
};

export const currencyToInrExchangeRate = async (
  currency: Currency,
): Promise<number> => {
  try {
    const docRef = db
      .collection(FIRESTORE_COLLECTIONS.EXCHANGE_RATE)
      .doc('INR');
    const snapshot = await docRef.get();

    if (!snapshot.exists) {
      throw new Error('Exchange rate document not found');
    }

    const data = snapshot.data() as DocumentData;

    if (!(currency in data)) {
      throw new Error(`Exchange rate for ${currency} not found`);
    }

    const rate = 1 / data[currency];
    return Math.round(rate * 100000) / 100000; // rounds to 5 decimal places
  } catch (error) {
    console.error('Error fetching currency to INR exchange rate:', error);
    throw error;
  }
};

export const sendWhatsappMessage = async (
  whatsappId: string,
  data: any,
): Promise<string> => {
  try {
    const url = getMetaUrl(`${META_BASE_URL}/${whatsappId}/messages`);
    const response = await axios({
      method: 'post',
      url: url.toString(),
      data,
      headers: { 'Content-Type': 'application/json' },
    });
    const messageId = response.data?.messages?.[0]?.id;
    return messageId;
  } catch (error) {
    logger.error((error as AxiosError).response?.data);
    return '';
  }
};

// input: +972584447873, +917206479844
export const getParsedPhoneNoObj = (
  phoneNumber: string,
): {
  dialingCode: string;
  phoneNumber: string;
} | null => {
  const parsedNumber = parsePhoneNumberFromString(phoneNumber);
  if (parsedNumber) {
    const countryCallingCode = parsedNumber.countryCallingCode;
    const nationalNumber = parsedNumber.nationalNumber;
    return {
      dialingCode: `+${countryCallingCode}`,
      phoneNumber: nationalNumber,
    };
  } else {
    return null;
  }
};

export const updateUserProfileUsingMobile = async (
  mobile: string,
  payload: Partial<IUserProfile>,
): Promise<void> => {
  const parsedPhoneNo = getParsedPhoneNoObj(mobile);
  if (!parsedPhoneNo) {
    logger.error(
      'Incorrect mobile supplied to updateUserProfileUsingMobile',
      mobile,
    );
    return;
  }
  try {
    const userProfiles: IUserProfile[] = [];
    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.USER_PROFILE)
      .where('mobile_dial_code', '==', parsedPhoneNo.dialingCode)
      .where('mobile', '==', parsedPhoneNo.phoneNumber)
      .get();
    snapshot.forEach((doc) => {
      const data = doc.data() as IUserProfile;
      userProfiles.push(data);
    });
    logger.info(
      `updateUserProfileUsingMobile: found ${userProfiles.length} entries`,
    );
    // more than 1 entry possible since we haven't put OTP verification on mobile
    for (const profile of userProfiles) {
      await db
        .collection(FIRESTORE_COLLECTIONS.USER_PROFILE)
        .doc(profile.uid)
        .update({
          ...payload,
          updated_at: Timestamp.now(),
        });
    }
  } catch (error) {
    logger.error(error);
  }
};

export const addCampaignToRunningList = async (
  campaign: ICampaign,
): Promise<void> => {
  try {
    const details = {
      id: campaign.id,
      name: campaign.name,
      campaign_end_date: Timestamp.fromDate(
        new Date(campaign?.details?.budget_and_scheduling?.end_time ?? ''),
      ),
      created_at: Timestamp.now(),
    };
    await db
      .collection(FIRESTORE_COLLECTIONS.RUNNING_CAMPAIGNS)
      .doc(campaign.id)
      .set(details, {
        merge: true,
      });
  } catch (error) {
    logger.error(error);
  }
};

export const getUserPerceivedCampaignSpendInInr = (
  spend: string,
  platformFeePercentage: number,
): number => {
  // same logic as FE, todo: expose an abstract API to FE for analytics
  // customer's budget = 1000, actual budget flowing to META = (1000 - 1000 * 10%)
  // now ad spent = 900, we should convert it back to 1000
  // 10 here is just example, currently it's 35.25 covering GST + platform fee
  let spendInInr = parseFloat(spend);
  spendInInr = spendInInr * (100 / (100 - platformFeePercentage));
  return spendInInr;
};

export const getParsedCampaignInsights = (params: {
  budgetAndScheduling?: IBudgetAndScheduling;
  insights: ICampaignInsightDetails;
  campaignType: GROWEASY_CAMPAIGN_TYPE;
}): Array<{
  label: string;
  value: string | number;
  description?: string;
}> => {
  const { budgetAndScheduling, insights, campaignType } = params;
  const campaignCurrencyBudgetNode =
    getCampaignCurrencyBudgetNode(budgetAndScheduling);

  // Meta Only, fallback to Leads count from API call
  insights.leads =
    getMetaLeadsCountFromInsightDetails(insights, campaignType) ||
    insights.leads;
  const platformFeePercentage =
    budgetAndScheduling?.platform_fee_percentage ?? 10;
  // same logic as FE
  // customer's budget = 1000, actual budget flowing to META = (1000 - 1000 * 10%)
  // now ad spent = 900, we should convert it back to 1000
  let spendInInr = parseFloat(insights.spend ?? '0');
  spendInInr = spendInInr * (100 / (100 - platformFeePercentage));

  const currency = budgetAndScheduling?.currency ?? Currency.INR;
  const currencyConversionFactor = [Currency.IDR, Currency.VND].includes(
    currency,
  )
    ? 1
    : 100;
  const lifetimeBudget = campaignCurrencyBudgetNode?.lifetime_budget ?? 0;
  const campaignBudgetInInr =
    (lifetimeBudget * (campaignCurrencyBudgetNode?.exchange_rate ?? 1)) /
    currencyConversionFactor;
  const spendInCampaignCurrency =
    spendInInr / (campaignCurrencyBudgetNode?.exchange_rate ?? 1);

  const currencyIcon = getCurrencySymbol(currency);
  // Ads manager campaign budget is always in INR since our Ad account is based out of India
  const budgetSpentPercentage = (spendInInr / campaignBudgetInInr) * 100;

  const details: Array<{
    label: string;
    value: string | number;
    description?: string;
  }> = [
    /* {
      label: 'Reach',
      value: data.reach,
      description: 'The number of people who saw your ads at least once. ',
    }, */
    {
      label: 'Views',
      value: insights.impressions ?? 0,
      description: 'The number of times your ads were on screen.',
    },
    {
      label: 'Clicks',
      value: insights.clicks ?? 0,
      description: 'The number of clicks on your ads.',
    },
    /* {
      label: 'CPC',
      value: `₹${parseFloat(data.cpc).toFixed(2)}`,
      description: 'The average cost for each click (all).',
    }, */
    {
      label: 'Cost',
      value: `${currencyIcon}${spendInCampaignCurrency.toFixed(2)}`,
      description:
        'The estimated total amount of money you have spent on your campaign',
    },
  ];
  // Google
  if (
    [
      GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
      GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
      GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
    ].includes(campaignType)
  ) {
    details.push(
      ...[
        {
          label: 'Conversions',
          value: insights.conversions ?? 0,
          description: '',
        },
        {
          label: 'CPCo',
          value: `${
            insights.conversions
              ? currencyIcon +
                (spendInCampaignCurrency / insights.conversions).toFixed(2)
              : '-'
          }`,
          description: 'Cost per Conversion.',
        },
      ],
    );
  } else if ([GROWEASY_CAMPAIGN_TYPE.META_SALES].includes(campaignType)) {
    const actualCpcoNode = getCpcoNodeForMetaSales(insights);
    const totalConversions = actualCpcoNode
      ? Math.round(
          parseFloat(insights.spend ?? '0') / parseFloat(actualCpcoNode.value),
        )
      : 0;
    details.push(
      ...[
        {
          label: 'Conversions',
          value: totalConversions ?? 0,
          description: '',
        },
        {
          label: 'CPCo',
          value: `${
            totalConversions
              ? currencyIcon +
                (spendInCampaignCurrency / totalConversions).toFixed(2)
              : '-'
          }`,
          description: 'Cost per Conversion.',
        },
        {
          label: 'CR',
          value: parseInt(insights.clicks)
            ? `${((totalConversions / parseInt(insights.clicks)) * 100).toFixed(
                2,
              )}%`
            : '-',
          description: 'Conversion Rate',
        },
      ],
    );
  } else {
    // handle for meta sales
    details.push(
      ...[
        {
          label: 'Leads',
          value: insights.leads ?? 0,
          description: '',
        },
        {
          label: 'CPL',
          value: `${
            insights.leads
              ? currencyIcon +
                (spendInCampaignCurrency / insights.leads).toFixed(2)
              : '-'
          }`,
          description: 'Cost per Lead.',
        },
      ],
    );
  }
  details.push(
    {
      label: 'Spent',
      value: `${Math.round(budgetSpentPercentage)}%`,
      description: 'Budget utilised so far',
    },
    {
      label: 'Budget',
      value: `${currencyIcon}${
        lifetimeBudget /
        ([Currency.IDR, Currency.VND].includes(currency) ? 1 : 100)
      }`,
      description: 'Total campaign budget',
    },
  );
  return details;
};

export const getParsedCampaignInsightsForTiktok = (params: {
  budgetAndScheduling?: IBudgetAndScheduling;
  insights: ITiktokCampaignInsights;
}): Array<{
  label: string;
  value: string | number;
  description?: string;
}> => {
  const { budgetAndScheduling, insights } = params;
  const { metrics } = insights;

  const campaignBudgetNode = getCampaignCurrencyBudgetNode(budgetAndScheduling);
  const spendInCampaignCurrency = Number(metrics.spend); // already in IDR
  const conversion = Number(metrics.conversion);
  const currency = budgetAndScheduling?.currency ?? Currency.INR;
  const currencyIcon = getCurrencySymbol(currency);
  const budgetSpentPercentage =
    (spendInCampaignCurrency / (campaignBudgetNode?.lifetime_budget ?? 0)) *
    100;

  const details: Array<{
    label: string;
    value: string | number;
    description?: string;
  }> = [
    /* {
      label: 'Reach',
      value: data.reach,
      description: 'The number of people who saw your ads at least once. ',
    }, */
    {
      label: 'Views',
      value: metrics.impressions ?? 0,
      description: 'The number of times your ads were on screen.',
    },
    {
      label: 'Clicks',
      value: metrics.clicks ?? 0,
      description: 'The number of clicks on your ads.',
    },
    /* {
      label: 'CPC',
      value: `₹${parseFloat(data.cpc).toFixed(2)}`,
      description: 'The average cost for each click (all).',
    }, */
    {
      label: 'Cost',
      value: `${currencyIcon}${spendInCampaignCurrency}`,
      description:
        'The estimated total amount of money you have spent on your campaign',
    },
    {
      label: 'Conversions',
      value: metrics.conversion,
      description: '',
    },
    {
      label: 'CPCo',
      value: `${
        conversion
          ? currencyIcon + (spendInCampaignCurrency / conversion).toFixed(2)
          : '-'
      }`,
      description: 'Cost per Conversion.',
    },
    {
      label: 'CR',
      value: metrics.conversion_rate,
      description: 'Conversion Rate',
    },
    {
      label: 'Spent',
      value: `${Math.round(budgetSpentPercentage)}%`,
      description: 'Budget utilised so far',
    },
    {
      label: 'Budget',
      value: `${currencyIcon}${campaignBudgetNode?.lifetime_budget}`,
      description: 'Total campaign budget',
    },
  ];
  return details;
};

// populate dynamic data of target template using source template
const populateDynamicDataFromSourceTemplate = (
  sourceTemplate: IBannerTemplate,
  targetTemplate: IBannerTemplate,
): IBannerTemplate => {
  let sourceTemplateTitle = '';
  let sourceTemplateCallout = '';
  let sourceTemplateCtaText = '';
  let sourceTemplateBusinessNameText = '';

  let sourceTemplateImageUrl = '';
  let sourceTemplateLogoUrl = '';

  sourceTemplate.elements.forEach((element) => {
    if (element.type === 'textArr' && element.texts) {
      for (const node of element.texts) {
        if (node.variableName === 'CREATIVE_TITLE_TEXT') {
          sourceTemplateTitle = node.value;
        } else if (node.variableName === 'CALL_OUT_TEXT') {
          sourceTemplateCallout = node.value;
        } else if (node.variableName === 'BUSINESS_NAME_TEXT') {
          sourceTemplateBusinessNameText = node.value;
        }
      }
    } else if (element.type === 'button') {
      sourceTemplateCtaText = element.textProps?.value;
    } else if (element.type === 'image') {
      if (element.imageProps?.variableName === 'CREATIVE_IMAGE') {
        sourceTemplateImageUrl = element.imageProps?.url;
      } else if (element.imageProps?.variableName === 'LOGO_IMAGE') {
        sourceTemplateLogoUrl = element.imageProps?.url;
      }
    }
  });
  const targetCopy = JSON.parse(
    JSON.stringify(targetTemplate),
  ) as IBannerTemplate;
  targetCopy.elements.forEach((element) => {
    if (element.type === 'textArr' && element.texts) {
      for (const node of element.texts) {
        if (node.variableName === 'CREATIVE_TITLE_TEXT') {
          node.value = sourceTemplateTitle;
        } else if (node.variableName === 'CALL_OUT_TEXT') {
          node.value = sourceTemplateCallout;
        } else if (node.variableName === 'BUSINESS_NAME_TEXT') {
          node.value = sourceTemplateBusinessNameText;
        }
      }
    } else if (element.type === 'button' && element.textProps) {
      element.textProps.value = sourceTemplateCtaText;
    } else if (element.type === 'image') {
      if (element.imageProps?.variableName === 'CREATIVE_IMAGE') {
        const elementWidth = element.container?.width;
        const elementHeight = element.container?.height;
        if (sourceTemplateImageUrl.includes('base64,')) {
          element.imageProps.url = sourceTemplateImageUrl;
        } else if (
          sourceTemplateImageUrl.includes('groweasy.ai/cdn-cgi/image')
        ) {
          // url is already from Cloudflare CDN, just update it (Freepik)
          element.imageProps.url = getUpdatedCloudflareImageUrl(
            sourceTemplateImageUrl,
            {
              width: elementWidth,
              height: elementHeight,
            },
          );
        } else {
          // unsplash/pexels URLs
          element.imageProps.url = getUpdatedUnsplashImageUrl(
            sourceTemplateImageUrl,
            {
              width: elementWidth,
              height: elementHeight,
            },
          );
        }
      } else if (element.imageProps?.variableName === 'LOGO_IMAGE') {
        element.imageProps.url = sourceTemplateLogoUrl;
      }
    }
  });
  return targetCopy;
};

// when user selects a square banner, we automatically construct corresponding portrait & landscape templates
// and upload for optimising Ad Placements
export const getAllCorrespondingPersonalisedTemplates = (
  squareTemplate: IBannerTemplate,
): IBannerTemplate[] => {
  const squareTemplateId = squareTemplate.id;
  const commonId = squareTemplateId.split('s')[0];
  const portraitTemplateId = commonId + 'p';
  const landscapeTemplateId = commonId + 'l';
  const allTemplates = [...templatesCollection1, ...templatesCollection2];
  let portraitTemplate = allTemplates.find(
    (item) => item.id === portraitTemplateId,
  );
  let landscapeTemplate = allTemplates.find(
    (item) => item.id === landscapeTemplateId,
  );

  const templatesArr = [squareTemplate];
  if (portraitTemplate) {
    portraitTemplate = populateDynamicDataFromSourceTemplate(
      squareTemplate,
      portraitTemplate,
    );
    templatesArr.push(portraitTemplate);
  }
  if (landscapeTemplate) {
    landscapeTemplate = populateDynamicDataFromSourceTemplate(
      squareTemplate,
      landscapeTemplate,
    );
    templatesArr.push(landscapeTemplate);
  }
  return templatesArr;
};

// Google mandates 1.91:1 for landscape, 4:5 for portrait
// to make it compliant, add white spaces in remaining area
export const resizeImageAsPerGoogleAds = async (params: {
  inputImagePath: string;
  outputImagePath: string;
  inputImageWidth: number;
  inputImageHeight: number;
}): Promise<string> => {
  const { inputImagePath, outputImagePath, inputImageWidth, inputImageHeight } =
    params;

  const GOOGLE_LANDSCAPE_RATIO = 1.91 / 1;
  const GOOGLE_PORTRAIT_RATIO = 4 / 5;
  // Determine the aspect ratio of the input image
  const inputAspectRatio = inputImageWidth / inputImageHeight;

  if (inputAspectRatio === 1) {
    // Case: 1:1 aspect ratio, square image
    // no padding needed. Just copy the original image to the output path.
    await fs.promises.copyFile(inputImagePath, outputImagePath);
  } else if (inputAspectRatio > 1) {
    // Case: Landscape image
    const targetWidth = inputImageWidth;
    const targetHeight = Math.round(inputImageWidth / GOOGLE_LANDSCAPE_RATIO);

    await sharp(inputImagePath)
      .resize({
        width: targetWidth,
        height: targetHeight,
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 },
      })
      .toFile(outputImagePath);
  } else {
    // Case: Portrait image
    const targetHeight = inputImageHeight;
    const targetWidth = Math.round(inputImageHeight * GOOGLE_PORTRAIT_RATIO);
    await sharp(inputImagePath)
      .resize({
        width: targetWidth,
        height: targetHeight,
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 },
      })
      .toFile(outputImagePath);
  }

  return outputImagePath;
};

// var d = new Date() => it'll be a date in machine's time zone
// since AWS runs on UTC, `d` above will be UTC date
// Google APIs will use Account timezone setting (IST in our case)
// hence for google campaign dates, explicitly use IST date
export const getFormattedDateAsPerGoogleAds = (date: Date): string => {
  // convert date to IST date
  const istDate = new Date(
    new Date(date).toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }),
  );

  const year = istDate.getFullYear();
  const month = String(istDate.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
  const day = String(istDate.getDate()).padStart(2, '0');

  return `${year}${month}${day}`;
};

export const uploadAssetToS3 = async (
  file: Express.Multer.File,
  /* queryParams?: Record<string, any>, */
): Promise<{
  s3_url: string;
}> => {
  const filePath = file.path;
  logger.info('util.uploadAssetToS3', file.path);
  try {
    const s3Url = await uploadFileToS3(
      BANNERBOT_S3_PUBLIC_BUCKET_NAME,
      filePath,
      `${S3_UPLOADED_ASSETS_DIR}/${file.filename}`,
    );
    return {
      s3_url: s3Url,
    };
  } catch (error) {
    logger.error(error);
    // always clean file even in case of error
    await fsUnlink(filePath);
    throw error;
  }
};

// same method in FE
export const getGoogleCampaignInsightDetails = (data: {
  insightsArr: IGoogleCampaignInsights[];
}): ICampaignInsightDetails => {
  const { insightsArr } = data;

  let totalClicks = 0;
  let totalCostMicros = 0;
  let totalImpressions = 0;
  let totalConversions = 0;

  insightsArr.forEach((item) => {
    totalClicks += parseInt(item.metrics?.clicks) ?? 0;
    totalCostMicros += parseInt(item.metrics?.costMicros) ?? 0;
    totalImpressions += parseInt(item.metrics?.impressions) ?? 0;
    totalConversions += item.metrics?.conversions ?? 0;
  });

  return {
    clicks: totalClicks.toString(),
    impressions: totalImpressions.toString(),
    spend: Math.round(totalCostMicros / 1000000).toString(),
    reach: '',
    date_start: '',
    date_stop: '',
    account_id: '',
    cpc: '', // todo
    ctr: '', // todo
    leads: 0,
    conversions: totalConversions,
  };
};

export const getGrowEasyHostedLeadFormUrl = (
  businessName: string,
  campaignDocId: string,
): string => {
  const parsedBusinessName = businessName?.replace(/\s+/g, '-')?.toLowerCase();
  const leadFormUrl = `https://connectform.co/lead-forms/${parsedBusinessName}/${campaignDocId}`;
  return leadFormUrl;
};

export const getMetaLeadsCountFromInsightDetails = (
  insights: ICampaignInsightDetails,
  campaignType: GROWEASY_CAMPAIGN_TYPE,
): number => {
  let leadsCount = 0;
  let actualCplNode:
    | {
        action_type: string;
        value: string;
      }
    | undefined;

  if (campaignType === GROWEASY_CAMPAIGN_TYPE.META_SALES) {
    actualCplNode = getCpcoNodeForMetaSales(insights);
  } else if (campaignType === GROWEASY_CAMPAIGN_TYPE.CTWA) {
    actualCplNode = insights?.cost_per_action_type?.find(
      (item) =>
        item.action_type === 'onsite_conversion.total_messaging_connection',
    );
  } else {
    // for Instant form both will have same value
    // for sales, "lead" can be greater than "onsite_conversion.lead_grouped" when using Instant form as secondary CTA
    actualCplNode = insights?.cost_per_action_type?.find(
      (item) =>
        item.action_type === 'lead' ||
        item.action_type === 'onsite_conversion.lead_grouped',
    );
  }
  if (actualCplNode) {
    leadsCount = Math.round(
      parseFloat(insights.spend ?? '0') / parseFloat(actualCplNode.value),
    );
  }
  return leadsCount;
};

export const addGrowEasyCampaignUtmParams = (params: {
  url: string;
  campaignId: string;
  platform?: AdPlatforms;
}): string => {
  const { url, campaignId, platform } = params;

  // website is optional for LEAD form / CTWA
  if (!url) {
    return '';
  }
  try {
    // Ensure URL has a valid scheme (http/https)
    let normalizedUrl = url.trim();
    if (!/^https?:\/\//i.test(normalizedUrl)) {
      normalizedUrl = `http://${normalizedUrl}`; // Default to HTTP
    }

    const urlObj = new URL(normalizedUrl);

    // Normalize only the hostname (domain) since path & query params are case sensitive
    urlObj.hostname = urlObj.hostname.toLowerCase();

    urlObj.searchParams.set('utm_source', 'groweasy');
    urlObj.searchParams.set('utm_medium', platform ?? '');
    urlObj.searchParams.set('utm_campaign', campaignId);

    return urlObj.toString();
  } catch (error) {
    logger.error(error);
    return '';
  }
};

export const getCampaignCurrencyBudgetNode = (
  budget?: IBudgetAndScheduling,
): ICurrencyBudget | undefined => {
  const currency = budget?.currency ?? Currency.INR;
  if (currency === Currency.INR) {
    return {
      lifetime_budget: budget?.lifetime_budget ?? 0,
      daily_budget: budget?.daily_budget ?? 0,
      exchange_rate: 1,
    };
  }
  const currencyKey = currency.toLowerCase() as keyof IBudgetAndScheduling;

  const currencyData = budget?.[currencyKey] as ICurrencyBudget | undefined;
  return currencyData;
};

// for USD, it reads usd.lifetime_budget node, for INR, it reads lifetime_budget and so on
export const getCampaignLifetimeBudget = (
  budget?: IBudgetAndScheduling,
): number => {
  const currencyData = getCampaignCurrencyBudgetNode(budget);

  return currencyData?.lifetime_budget ?? 0;
};

export const getMinOrderAmountForCurrency = (currency: Currency): number => {
  const minOrderAmountMap: Record<Currency, number> = {
    [Currency.INR]: 100, // ₹1.00
    [Currency.USD]: 100, // $1.00
    [Currency.PHP]: 100, // ₱1.00
    [Currency.THB]: 100, // ฿1.00
    [Currency.MYR]: 100, // RM1.00
    [Currency.IDR]: 1000, // Rp1000
    [Currency.VND]: 1000, // ₫1000
  };

  return minOrderAmountMap[currency] ?? 100;
};

export const getAdAccountIdBasedOnBusinessCategory = (
  category: string,
): string => {
  const GROWEASY_EDUCATION_AD_ACC_ID = '***************';
  const GROWEASY_TRAVELS_AD_ACC_ID = '****************';
  const GROWEASY_REAL_ESTATE_ADD_ACC_ID = '****************';

  if (
    ['Education and E-Learning', 'Educational Resources'].includes(category)
  ) {
    return GROWEASY_EDUCATION_AD_ACC_ID;
  }
  if (['Travel and Tourism'].includes(category)) {
    return GROWEASY_TRAVELS_AD_ACC_ID;
  }
  if (['Real Estate and Property Management'].includes(category)) {
    return GROWEASY_REAL_ESTATE_ADD_ACC_ID;
  }

  // for rest of the categories
  return GROWEASY_MAIN_AD_ACC_ID;
};

export const deduplicateTargetingItems = (
  items: IFlexibleTargetingItem[],
): IFlexibleTargetingItem[] => {
  const seen = new Set<string>();
  const uniqueItems: IFlexibleTargetingItem[] = [];

  for (const item of items) {
    const key = item.id;
    if (!seen.has(key)) {
      seen.add(key);
      uniqueItems.push(item);
    }
  }

  return uniqueItems;
};
