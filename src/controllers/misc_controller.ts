import axios from 'axios';
import { PRICE_PER_VIDEO, VIDEO_ORDERS_SHEET_ID } from '../constants';
import { addNewTabToSheet, appendDataToSheet } from '../modules/google_sheet';
import logger from '../modules/logger';
import {
  type IVideoOrdersResPayload,
  type IPaymentLinkPayload,
  type IVideoOrdersReqPayload,
  type IMasterClassRegistrationDetails,
} from '../types';
import {
  sendGoogleKeywordIdeasEmail,
  sendMetaAudienceSuggestionEmail,
  sendVideosPaymentPendingEmail,
} from './email_controller';
import {
  createRazorpayMasterClassOrder,
  createRazorpayVideoOrder,
} from './payment_controller';
import { getMetaAudienceV2 } from './openai_controller';
import { getGoogleSearchKeywordsPrompt } from '../prompts/groweasy_prompts';
import { getResponseFromAzureOpenAi } from '../modules/openai';
import { type IGoogleAdsData } from '../types/campaign_details';
import { generateKeywordIdeas } from './google/google_controller';

const RAZORPAY_CREDS = `${process.env.RAZORPAY_PROD_KEY_ID}:${process.env.RAZORPAY_PROD_KEY_SECRET}`;
const RAZORPAY_BASE64_CREDS = Buffer.from(RAZORPAY_CREDS).toString('base64');

export const createVideoOrders = async (
  payload: IVideoOrdersReqPayload,
): Promise<IVideoOrdersResPayload> => {
  const { user_details: userDetails, video_details: videoDetails } = payload;

  try {
    const spreadsheetId = VIDEO_ORDERS_SHEET_ID;
    const sheetName = 'groweasy_orders';

    await addNewTabToSheet({
      spreadsheetId,
      sheetName,
    });
    await appendDataToSheet({
      spreadsheetId: VIDEO_ORDERS_SHEET_ID,
      sheetName,
      rowData: videoDetails.map((details) => {
        return [
          new Date().toDateString(),
          `${userDetails?.name ?? ''}\n${userDetails.mobile ?? ''}\n${
            userDetails.email ?? ''
          }`,
          details.script,
          details.actor_id,
        ];
      }),
    });
    const orderDetails = await createRazorpayVideoOrder(payload);
    void sendVideosPaymentPendingEmail(orderDetails);
    return {
      price_per_video: PRICE_PER_VIDEO,
      razorpay_order_id: orderDetails.razorpay_order_id,
      quantity: orderDetails.video_details?.length,
      gst_percentage: 18,
      amount: orderDetails.amount,
      currency: orderDetails.currency,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

// 6 months expiry by default
// https://razorpay.com/docs/api/payments/payment-links/create-standard/
export const createPaymentLink = async (
  payload: IPaymentLinkPayload,
): Promise<{
  short_url: string;
}> => {
  const { customer, amount, description } = payload;

  try {
    const data = {
      amount: amount * 100, // in paise
      currency: 'INR',
      description,
      customer,
      notify: {
        sms: true,
        email: true,
      },
    };
    const config = {
      method: 'post',
      url: 'https://api.razorpay.com/v1/payment_links/',
      headers: {
        'Content-type': 'application/json',
        Authorization: `Basic ${RAZORPAY_BASE64_CREDS}`,
      },
      data,
    };
    const response = await axios.request(config);
    return {
      short_url: response.data.short_url,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const createMasterClassOrder = async (
  payload: IMasterClassRegistrationDetails,
): Promise<{ razorpay_order_id: string }> => {
  const { name, email, mobile, profession } = payload;

  try {
    const spreadsheetId = VIDEO_ORDERS_SHEET_ID;
    const sheetName = 'master_class_orders';

    await addNewTabToSheet({
      spreadsheetId,
      sheetName,
    });
    await appendDataToSheet({
      spreadsheetId: VIDEO_ORDERS_SHEET_ID,
      sheetName,
      rowData: [
        [
          new Date().toDateString(),
          `${name ?? ''}\n${mobile ?? ''}\n${email ?? ''}`,
          profession ?? 'NA',
        ],
      ],
    });
    const orderDetails = await createRazorpayMasterClassOrder(payload);
    return {
      razorpay_order_id: orderDetails.razorpay_order_id,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const sendFacebookAudienceSuggestions = async (payload: {
  product_or_service_description: string;
  email: string;
  business_category: string;
}): Promise<void> => {
  try {
    const metaAudience = await getMetaAudienceV2({
      businessDetails: {
        business_category: payload.business_category,
        product_or_service_description: payload.product_or_service_description,
      },
      targeting: {
        age_min: 21,
        age_max: 65,
        genders: [1, 2],
        geo_locations: {},
        flexible_spec: [],
        age_range: [],
      },
    });
    void sendMetaAudienceSuggestionEmail({
      ...payload,
      metaAudience,
    });
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const sendGoogleKeywordIdeas = async (payload: {
  product_or_service_description: string;
  email: string;
  business_category: string;
  website?: string;
  country_code: 'IN' | 'US';
}): Promise<void> => {
  const geoTargetConstantMapping = {
    IN: 'geoTargetConstants/2356',
    US: 'geoTargetConstants/2840',
  };
  let geoTargetConstant;
  if (payload.country_code) {
    geoTargetConstant = geoTargetConstantMapping[payload.country_code];
  }
  try {
    const prompt = getGoogleSearchKeywordsPrompt({
      business_details: {
        business_category: payload.business_category,
        product_or_service_description: payload.product_or_service_description,
        website: payload.website,
      },
      // hardcoded targeting for google
      targeting: {
        age_min: 21,
        age_max: 65,
        genders: [1, 2],
        geo_locations: {},
        flexible_spec: [],
        age_range: [],
      },
    });
    const searchKeywordsSuggestions = (await getResponseFromAzureOpenAi(
      prompt,
    )) as IGoogleAdsData['search_keywords_suggestions'];
    const seedKeywords: string[] = [];
    if (!searchKeywordsSuggestions) {
      throw new Error('Failed to generate searchKeywordsSuggestions');
    }
    // pick 2 keywords from each category
    Object.keys(searchKeywordsSuggestions).forEach((key) => {
      if (searchKeywordsSuggestions[key]?.length) {
        seedKeywords.push(...searchKeywordsSuggestions[key].slice(0, 2));
      }
    });

    const googleKeywordIdeas = (
      await generateKeywordIdeas({
        url: payload.website,
        seed_keywords: seedKeywords,
        geo_target_constants: geoTargetConstant ? [geoTargetConstant] : [],
      })
    )?.keyword_ideas;

    void sendGoogleKeywordIdeasEmail({
      ...payload,
      googleKeywordIdeas,
    });
  } catch (error) {
    logger.error(error);
    throw error;
  }
};
