import {
  type IUserProfile,
  type GrowEasyPartners,
  type IAuthUser,
} from '../types';
import { auth, db } from '../modules/firebase_admin_sdk';
import ValidationError from '../utils/validation_error';
import { FIRESTORE_COLLECTIONS } from '../constants';
import logger from '../modules/logger';
import { Timestamp } from 'firebase-admin/firestore';
import { sendWelcomeFollowupComm } from './comms_controller';
import { sendContactUsEmail } from './email_controller';

// being called for GrowEasy Partners (either to set whole custom claims profile or just partner)
export const updateProfileInCustomClaims = async (
  profile: {
    name: string;
    email: string;
    patner: GrowEasyPartners;
  },
  user?: IAuthUser,
): Promise<IAuthUser> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  // pass email only for mobile auth users
  if (profile.email) {
    // check if this email has been taken by email verified users
    // duplicate email can exists when Login in via Mobile, todo: also check userProfile.email to be unique
    const data = await auth.getUsers([{ email: profile.email }]);
    if (data.users?.length) {
      throw new ValidationError(400, 'This email is already being used');
    }
  }
  await auth.setCustomUserClaims(user.uid, {
    profile,
  });
  return user;
};

export const getUserProfile = async (
  user?: IAuthUser,
): Promise<IUserProfile | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const doc = await db
      .collection(FIRESTORE_COLLECTIONS.USER_PROFILE)
      .doc(user.uid)
      .get();
    return (doc.data() as IUserProfile) ?? null;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const createOrUpdateUserProfile = async (
  body: Partial<IUserProfile>,
  user?: IAuthUser,
): Promise<IUserProfile | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  // Some fields cannot be updated from FE
  delete body.uid;
  delete body.created_at;
  delete body.email;
  delete body.name;

  // generate whatsapp_id from country_code & mobile
  if (body?.mobile && body?.mobile_dial_code) {
    body.whatsapp_id = `${body.mobile_dial_code}${body.mobile}`.replace(
      '+',
      '',
    );
  }

  try {
    const documentRef = db
      .collection(FIRESTORE_COLLECTIONS.USER_PROFILE)
      .doc(user.uid);
    const documentSnanpshot = await documentRef.get();
    const profileDetails = documentSnanpshot.data();
    // merge data if profile info already exists
    // else create new record
    if (profileDetails) {
      await documentRef.set(
        {
          ...body,
          updated_at: Timestamp.now(),
        },
        {
          merge: true, // caution: shallow merge only
        },
      );
      const updatedDoc = await documentRef.get();
      return (updatedDoc.data() as IUserProfile) ?? null;
    } else {
      if (!body.mobile || !body.business_name) {
        throw new ValidationError(400, 'Missing required fields');
      }
      // create
      const data: Partial<IUserProfile> = {
        ...body,
        uid: user.uid,
        email: user.email ?? '', // Apple?
        name: user.name ?? '', // Apple signin might have no name
        created_at: Timestamp.now(),
        updated_at: Timestamp.now(),
      };

      await documentRef.set(data);

      // send WhatsApp comm on profile creation
      void sendWelcomeFollowupComm({
        uid: user.uid,
      });

      return data as IUserProfile;
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const deleteAccount = async (user?: IAuthUser): Promise<void> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  await auth.deleteUser(user.uid);
};

export const contactUsEmail = async (
  params: { subject: string; message: string },
  user?: IAuthUser,
): Promise<void> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  if (!params.subject || !params.message) {
    throw new ValidationError(400, 'Missing subject or message');
  }

  try {
    await sendContactUsEmail(params, user);
  } catch (error) {
    logger.error(error);
    throw error;
  }
};
