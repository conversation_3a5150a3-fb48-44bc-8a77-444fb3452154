import { BCN_WHATSAPP_NO, FIRESTORE_COLLECTIONS } from '../constants';
import { db } from '../modules/firebase_admin_sdk';
import { type IAuthUser } from '../types';
import { type ICtwaLead } from '../types/leads';
import { type ILeadsCrmDetails } from '../types/leads_crm';
import { type ICtwaChatMessage } from '../types/whatsapp';
import ValidationError from '../utils/validation_error';
import { isGrowEasyAdmin } from './util';

export const getCtwaLeads = async (
  queryParams: Record<string, any>,
  user: IAuthUser,
): Promise<ICtwaLead[]> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const { campaign_id: campaignId } = queryParams;
  const limit = parseInt(queryParams.limit) || 50;
  let snapshotRef = db
    .collection(FIRESTORE_COLLECTIONS.CTWA_LEADS)
    .where('campaign_id', '==', campaignId)
    .orderBy('created_time', 'desc');

  if (!isGrowEasyAdmin(user)) {
    snapshotRef = snapshotRef.where('client_uid', '==', user.uid);
  }
  snapshotRef = snapshotRef.limit(limit);
  const snapshot = await snapshotRef.get();
  const data: ICtwaLead[] = [];
  snapshot.docs.forEach((doc) => {
    data.push(doc.data() as ICtwaLead);
  });
  return data;
};

export const getCtwaLeadDetails = async (
  queryParams: Record<string, any>,
): Promise<{
  details: ICtwaLead;
  chats: ICtwaChatMessage[];
}> => {
  const { lead_id: leadId } = queryParams;
  const leadDetailsDoc = await db
    .collection(FIRESTORE_COLLECTIONS.CTWA_LEADS)
    .doc(leadId)
    .get();
  const leadDetails = leadDetailsDoc.data() as ICtwaLead;
  const chats: ICtwaChatMessage[] = [];
  if (leadDetails?.campaign_id && leadDetails?.wa_id) {
    const snapshot = await db
      .collection(`ctwa_${BCN_WHATSAPP_NO}`)
      .doc(leadDetails.wa_id)
      .collection(`messages_${leadDetails.campaign_id}`)
      .orderBy('time')
      .get();
    snapshot.docs.forEach((doc) => {
      chats.push(doc.data() as ICtwaChatMessage);
    });
  }
  return {
    details: leadDetails,
    chats,
  };
};

export const getAllCtwaLeads = async (
  queryParams: Record<string, any>,
  user?: IAuthUser,
): Promise<Array<ICtwaLead & { crm_details?: ILeadsCrmDetails }>> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  // max is 30, limitation of 'IN' operator
  // for these leads, we also have to fetch crm_details using IN operator
  const limit = Math.min(parseInt(queryParams.limit) || 30);
  const startAfter =
    parseInt(queryParams.start_after) || Math.floor(Date.now() / 1000);
  const leads: Array<ICtwaLead & { crm_details?: ILeadsCrmDetails }> = [];
  const leadsSnapshot = await db
    .collection(FIRESTORE_COLLECTIONS.CTWA_LEADS)
    .where('client_uid', '==', user.uid)
    .orderBy('created_time', 'desc')
    .startAfter(startAfter)
    .limit(limit)
    .get();
  const leadgenIds: string[] = [];
  leadsSnapshot.forEach((doc) => {
    const data = doc.data() as ICtwaLead;
    leads.push(data);
    leadgenIds.push(data.id);
  });

  if (leadgenIds.length) {
    // get all leads Crm for these leads
    const leadsCrmSnapshot = await db
      .collection(FIRESTORE_COLLECTIONS.LEADS_CRM)
      .where('leadgen_id', 'in', leadgenIds)
      .get();
    // match lead CRM and corresponding leads
    leadsCrmSnapshot.forEach((doc) => {
      for (let i = 0; i < leads.length; i++) {
        const leadCrm = doc.data() as ILeadsCrmDetails;
        if (leads[i].id === leadCrm.leadgen_id) {
          leads[i].crm_details = leadCrm;
          break;
        }
      }
    });
  }

  return leads;
};
