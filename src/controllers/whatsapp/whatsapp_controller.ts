import { BCN_WHATSAPP_NO, GROWEASY_WHATSAPP_NO } from '../../constants';
import {
  type IWebhookWhatsappEventEntryChange,
  type IMetaWebhookWhatsappEventBody,
} from '../../types/whatsapp';
import { checkAndProcessAgentEasyMessageFromWebhook } from '../agenteasy';
import { chunkArray } from '../util';
import { processIncomingPayloadForBcn } from './bcn_wa_controller';
import { processIncomingPayloadForGrowEasy } from './groweasy_wa_controller';

export const processWhatsappPayloadFromWebhook = async (
  body: IMetaWebhookWhatsappEventBody,
  partner?: 'INTERAKT',
): Promise<boolean[]> => {
  const changes: IWebhookWhatsappEventEntryChange[] = [];
  const responses: boolean[] = [];
  // club all entries
  body?.entry?.forEach((entry) => {
    entry.changes?.forEach((change) => {
      changes.push(change);
    });
  });

  // check and process in batch of 5
  const batchedChanges = chunkArray(changes, 5);
  for (let i = 0; i < batchedChanges.length; i++) {
    const changes = batchedChanges[i];
    const promises: Array<Promise<boolean>> = [];
    changes.forEach((change) => {
      if (partner === 'INTERAKT') {
        promises.push(checkAndProcessAgentEasyMessageFromWebhook(change));
      } else {
        promises.push(checkAndProcessMessageFromWebhook(change));
      }
    });
    const batchResponses = await Promise.all(promises);
    responses.push(...batchResponses);
  }
  return responses;
};

const checkAndProcessMessageFromWebhook = async (
  change: IWebhookWhatsappEventEntryChange,
): Promise<boolean> => {
  if (change.value?.metadata?.display_phone_number === BCN_WHATSAPP_NO) {
    await processIncomingPayloadForBcn(change.value);
  } else if (
    change.value?.metadata?.display_phone_number === GROWEASY_WHATSAPP_NO
  ) {
    await processIncomingPayloadForGrowEasy(change.value);
  }
  return true;
};
