import { type DocumentReference, Timestamp } from 'firebase-admin/firestore';
import { GROWEASY_WHATSAPP_ID } from '../../constants';
import { db } from '../../modules/firebase_admin_sdk';
import logger from '../../modules/logger';
import {
  type ICtwaChatMessage,
  type IGrowEasyWaConversationDetails,
  type IWaMessage,
  type IWebhookWhatsappEventEntryChange,
  WaMessageType,
} from '../../types/whatsapp';
import { sendWhatsappMessage, updateUserProfileUsingMobile } from '../util';
import { handleButtonMessageReply } from '../comms_controller';

export const sendWaMessage = async (data: any): Promise<string> => {
  return await sendWhatsappMessage(GROWEASY_WHATSAPP_ID, data);
};

export const processIncomingPayloadForGrowEasy = async (
  payload: IWebhookWhatsappEventEntryChange['value'],
): Promise<boolean> => {
  if (payload.messages?.length) {
    await processIncomingMessagesForGrowEasy(payload);
  }
  if (payload.statuses?.length) {
    await processStatusesUpdateForGrowEasy(payload);
  }
  return true;
};

const processStatusesUpdateForGrowEasy = async (
  payload: IWebhookWhatsappEventEntryChange['value'],
): Promise<boolean> => {
  return true;
};

const updateConversationDetails = async (
  conversationDocRef: DocumentReference,
  updatePayload: Partial<IGrowEasyWaConversationDetails>,
): Promise<void> => {
  await conversationDocRef.set(updatePayload, {
    merge: true,
  });
};

// wa_919315639185/917206479844
const processIncomingMessagesForGrowEasy = async (
  payload: IWebhookWhatsappEventEntryChange['value'],
): Promise<boolean> => {
  const message = payload.messages?.[0];
  if (!message?.from) {
    logger.error(
      'message.from not found in processIncomingMessagesForGrowEasy',
    );
    return false;
  }
  const sendersName = payload?.contacts?.[0]?.profile?.name ?? '';
  const sendersMobile = `+${message?.from}`;
  const conversationDocRef = db
    .collection(`wa_${payload.metadata?.display_phone_number}`)
    .doc(message.from);
  const conversationDoc = await conversationDocRef.get();
  const conversationDetails = conversationDoc.data();
  if (conversationDetails) {
    // update
    const updatePayload: Partial<IGrowEasyWaConversationDetails> = {
      updated_at: Timestamp.now(),
      last_message: message,
    };
    await updateConversationDetails(conversationDocRef, updatePayload);
  } else {
    // create
    const createPayload: IGrowEasyWaConversationDetails = {
      updated_at: Timestamp.now(),
      created_at: Timestamp.now(),
      last_message: message,
      mobile: sendersMobile,
      profile_name: sendersName,
    };
    await conversationDocRef.set(createPayload);
  }
  await replyForGrowEasyWaMessages(conversationDocRef, message);
  return true;
};

const unsubscribeFromWhatsappComms = async (
  conversationDocRef: DocumentReference,
  message: IWaMessage,
): Promise<void> => {
  await updateUserProfileUsingMobile(`+${message.from}`, {
    whatsapp_opt_in: false,
  });
  const replyPayload: ICtwaChatMessage = {
    messaging_product: 'whatsapp',
    to: message.from,
    type: WaMessageType.text,
    text: {
      body: 'You have successfully unsubscribed from WhatsApp messages. In case you wish to start receiving messages, reply with START.',
    },
  };
  const sentMessageId = await sendWaMessage(replyPayload);

  // update conversation
  await updateConversationDetails(conversationDocRef, {
    updated_at: Timestamp.now(),
    last_message: replyPayload,
  });
  // store sent reply
  if (sentMessageId) {
    await conversationDocRef
      .collection(`messages`)
      .doc(sentMessageId)
      .set({
        ...replyPayload,
        time: Timestamp.now(),
      });
  }
};

const subscribeToWhatsappComms = async (
  conversationDocRef: DocumentReference,
  message: IWaMessage,
): Promise<void> => {
  await updateUserProfileUsingMobile(`+${message.from}`, {
    whatsapp_opt_in: true,
  });
  // todo reply with options?
  const replyPayload: ICtwaChatMessage = {
    messaging_product: 'whatsapp',
    to: message.from,
    type: WaMessageType.text,
    text: {
      body: 'You have successfully subscribed to WhatsApp messages.',
    },
  };
  const sentMessageId = await sendWaMessage(replyPayload);

  // update conversation
  await updateConversationDetails(conversationDocRef, {
    updated_at: Timestamp.now(),
    last_message: replyPayload,
  });
  // store sent reply
  if (sentMessageId) {
    await conversationDocRef
      .collection(`messages`)
      .doc(sentMessageId)
      .set({
        ...replyPayload,
        time: Timestamp.now(),
      });
  }
};

// wa_919315639185/917206479844/messages/wamid.HBgMOTE3MjA2NDc5ODQ0FQIAEhggOUI4NTQ5NTZGRjk0ODI0NjU2NkNBMTg0Nzk1MkVFQTUA
const replyForGrowEasyWaMessages = async (
  conversationDocRef: DocumentReference,
  message: IWaMessage,
): Promise<void> => {
  // store response
  await conversationDocRef
    .collection(`messages`)
    .doc(message.id)
    .set({
      ...message,
      time: Timestamp.now(),
    });
  // send a contextual reply with necessary actions
  if (
    message?.type === WaMessageType.text &&
    message?.text?.body?.toLowerCase() === 'stop'
  ) {
    void unsubscribeFromWhatsappComms(conversationDocRef, message);
  } else if (
    message?.type === WaMessageType.text &&
    message?.text?.body?.toLowerCase() === 'start'
  ) {
    void subscribeToWhatsappComms(conversationDocRef, message);
  } else if (message?.type === WaMessageType.button) {
    void handleButtonMessageReply(message);
  }
};
