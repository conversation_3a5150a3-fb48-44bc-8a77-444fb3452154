import { Timestamp } from 'firebase-admin/firestore';
import { FIRESTORE_COLLECTIONS } from '../../constants';
import { db } from '../../modules/firebase_admin_sdk';
import logger from '../../modules/logger';
import { getResponseFromOpenAi } from '../../modules/openai';
import { getAiAgentRequirementsGatheringQuestionsPrompt } from '../../prompts/agenteasy_prompts';
import {
  GROWEASY_CAMPAIGN_TYPE,
  type ICampaign,
} from '../../types/campaign_details';
import { type IMetaLead } from '../../types/leads';
import {
  AgentEasyMessageRole,
  type IAgentEasyWaMessageDetails,
  type IAgentEasyCampaignContext,
  type IAgentEasyConversationDetails,
  type IAgentEasyDataForCampaign,
} from '../../types/agenteasy';
import {
  getCampaignDetailsForGrowEasyAdmin,
  getUserProfileDetails,
} from '../admin_controller';
import {
  type IWaMessageToBeSentPayload,
  WaMessageType,
} from '../../modules/interakt/types';
import { sendWhatsappMessage } from '../../modules/interakt/whatsapp';
import { type IWebhookWhatsappEventEntryChange } from '../../types/whatsapp';
import { processIncomingMessagesForAgentEasy } from './incoming_messages_controller';
import { type IAuthUser } from '../../types';
import ValidationError from '../../utils/validation_error';
import { isGrowEasyAdmin } from '../util';
import { updateLeadFromWebhook } from '../db_controller';

export const getAgentEasyConversationDetails = async (
  chatDocId: string,
): Promise<IAgentEasyConversationDetails | null> => {
  const conversationDocRef = db
    .collection(FIRESTORE_COLLECTIONS.AGENTEASY)
    .doc(chatDocId);

  const conversationDoc = await conversationDocRef.get();
  const conversationDetails =
    conversationDoc.data() as IAgentEasyConversationDetails | null;
  return conversationDetails;
};

export const storeAgentEasyDataForCampaignId = async (
  campaignId: string,
): Promise<void> => {
  const campaignDetailsDocSnapshot =
    await getCampaignDetailsForGrowEasyAdmin(campaignId);
  const campaignDetails = campaignDetailsDocSnapshot?.data() as ICampaign;
  await storeAgentEasyDataForCampaign(campaignDetails);
};

export const storeAgentEasyDataForCampaign = async (
  campaign: ICampaign,
): Promise<void> => {
  try {
    // AI WhatsApp sales agent only for Meta Instant form campaigns
    if (campaign.type !== GROWEASY_CAMPAIGN_TYPE.LEAD_FORM) {
      return;
    }

    const userProfile = await getUserProfileDetails({
      uid: campaign.uid,
    });
    // Check if feature is disabled for this user, by default it will be enabled for all
    if (userProfile?.feature_flags?.agenteasy_enabled === false) {
      logger.info(`AgentEasy feature is not enabled for user ${campaign.uid}`);
      return;
    }

    const {
      business_details: businessDetails,
      ad_language: adLanguage,
      ai_assisted_product_usps: productUsps,
      ad_copies: adCopies,
    } = campaign.details ?? {};

    if (!businessDetails) {
      logger.error('storeAgentEasyDataForCampaign: Missing business_details');
      return;
    }

    // Build AI prompt
    const prompt = getAiAgentRequirementsGatheringQuestionsPrompt({
      business_details: businessDetails,
      ad_language: adLanguage,
      ai_assisted_product_usps: productUsps ?? [],
      ad_copies: adCopies ?? [],
    });

    // Get AI-generated agent data
    const openAiResponse = await getResponseFromOpenAi(prompt);

    if (!openAiResponse) {
      logger.error(
        `storeAgentEasyDataForCampaign: No response from OpenAI for campaign ${campaign.id}`,
      );
      return;
    }
    const agentEasyDataForCampaign: IAgentEasyDataForCampaign = {
      ...openAiResponse,
      campaign_id: campaign.id,
      uid: campaign.uid,
      created_at: Timestamp.now(),
      is_active: true,
    };

    // Store result in Firestore
    await db
      .collection(FIRESTORE_COLLECTIONS.AGENTEASY_DATA)
      .doc(campaign.id)
      .set(agentEasyDataForCampaign);

    logger.info(`AgentEasy data stored for campaign ${campaign.id}`);
  } catch (error) {
    logger.error(
      `storeAgentEasyDataForCampaign: Failed for campaign ${campaign.id}`,
      error,
    );
  }
};

// output: ************
const getChatDocIdFromPhone = (phone: string): string => {
  if (phone.includes('+')) {
    return phone.replace(/\D/g, ''); // remove '+' and any non-digit characters
  } else {
    return `91${phone}`;
  }
};

const getContactDetailsFromLead = (
  leadDetails: IMetaLead,
): {
  full_name: string;
  phone: string;
  email: string;
} => {
  // question1, question2, & question3 are hardcoded for name, email, & mobile
  let fullName = '';
  let email = '';
  let phone = '';

  leadDetails?.field_data?.forEach((item) => {
    if (item.name === 'question1') {
      fullName = item.values?.[0] || '';
    } else if (item.name === 'question2') {
      email = item.values?.[0] || '';
    } else if (item.name === 'question3') {
      phone = item.values?.[0] || '';
    }
  });

  return {
    full_name: fullName.trim(),
    phone: phone.trim(),
    email: email.trim(),
  };
};

export const sendMessageToLead = async (
  chatDocId: string,
  messagePayload: IWaMessageToBeSentPayload,
): Promise<void> => {
  try {
    logger.info(`sendMessageToLead called for ${chatDocId}`);
    const waMessageId = await sendWhatsappMessage({}, messagePayload);

    if (!waMessageId) {
      // error occured
      return;
    }

    const messageDetails: IAgentEasyWaMessageDetails = {
      role: AgentEasyMessageRole.ASSISTANT,
      time: Timestamp.now(),
      payload: messagePayload,
    };

    // convert welcome template message payload to text payload
    // so that GrowEasy Chat UI & AI chat agent get actual text content for rendering & parsing
    if (messageDetails.payload?.type === WaMessageType.template) {
      if (
        messageDetails.payload.template?.name &&
        ['agenteasy_welcome_message_v1', 'ae_wc'].includes(
          messageDetails.payload.template?.name,
        )
      ) {
        const templateParams =
          messageDetails.payload?.template?.components?.[0]?.parameters;
        messageDetails.payload = {
          ...messageDetails.payload,
          type: WaMessageType.text,
          text: {
            body: `Hi ${templateParams?.[0]?.text}, I’m ${templateParams?.[1]?.text}’s sales agent. 

I’m following up regarding the details you requested through our form recently. 

I would like to know your requirements to assist you further.`,
          },
        };
      }
    }

    const docRef = db
      .collection(FIRESTORE_COLLECTIONS.AGENTEASY)
      .doc(chatDocId);

    // update conversation details
    await docRef.update({
      updated_at: Timestamp.now(),
      last_message: messageDetails,
    });

    // create conversation message
    await docRef
      .collection(FIRESTORE_COLLECTIONS.MESSAGES)
      .doc(waMessageId)
      .set(messageDetails);
  } catch (error) {
    logger.error(error);
  }
};

const sendWelcomeMessageToLead = async (
  leadDetails: IMetaLead,
): Promise<void> => {
  if (!leadDetails.campaign_id) {
    // should never happen, checks are already in place in start of flow
    return;
  }
  const contactDetails = getContactDetailsFromLead(leadDetails);
  const docId = getChatDocIdFromPhone(contactDetails.phone);

  // should not be null, checks are already there in beginning of the flow
  const agentEasyDataForCampaign = (
    await db
      .collection(FIRESTORE_COLLECTIONS.AGENTEASY_DATA)
      .doc(leadDetails.campaign_id)
      .get()
  ).data() as IAgentEasyDataForCampaign;
  await sendMessageToLead(docId, {
    messaging_product: 'whatsapp',
    to: docId,
    type: WaMessageType.template,
    template: {
      name: 'ae_wc', // 'agenteasy_welcome_message_v1',
      language: {
        code: 'en',
      },
      components: [
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: contactDetails.full_name,
              parameter_name: 'name',
            },
            {
              type: 'text',
              text: agentEasyDataForCampaign?.business_name ?? '',
              parameter_name: 'business_name',
            },
          ],
        },
      ],
    },
  });
};

// create or update doc agenteasy/************
const initChatToLead = async (leadDetails: IMetaLead): Promise<void> => {
  const contactDetails = getContactDetailsFromLead(leadDetails);

  if (!contactDetails.phone) {
    logger.error('initChatToLead: phone not found');
    return;
  }

  const docId = getChatDocIdFromPhone(contactDetails.phone);

  // create agenteasy node so that UI starts showing chat entry point
  await updateLeadFromWebhook({
    id: leadDetails.id,
    agenteasy: {
      chat_doc_id: docId,
    },
  });

  const conversationDocRef = db
    .collection(FIRESTORE_COLLECTIONS.AGENTEASY)
    .doc(docId);

  const conversationDetails = await getAgentEasyConversationDetails(docId);

  const campaignContext: IAgentEasyCampaignContext = {
    full_name: contactDetails.full_name,
    campaign_id: leadDetails.campaign_id ?? '',
    uid: leadDetails.uid ?? '',
    leadgen_id: leadDetails.id,
    classification_data: {
      source: 'GROWEASY', // for now
      chat_doc_id: docId,
    },
    chat_status: 'OPEN',
  };

  if (!conversationDetails) {
    const newConversationDetails: IAgentEasyConversationDetails = {
      created_at: Timestamp.now(),
      campaign_context: campaignContext,
      updated_at: Timestamp.now(),
    };
    // First time lead contact
    await conversationDocRef.set(newConversationDetails);

    logger.info(`initChatToLead: created new conversation for ${docId}`);

    // send welcome message
    await sendWelcomeMessageToLead(leadDetails);
    return;
  }

  // Idempotency check: if this leadgen_id already exists as current, do nothing
  if (conversationDetails.campaign_context?.leadgen_id === leadDetails.id) {
    logger.info(
      `initChatToLead: leadgen_id ${leadDetails.id} already exists for ${docId}, skipping update`,
    );
    return;
  }

  // Append old context to previous
  const previousContexts = [
    ...(conversationDetails.previous_campaigns_contexts ?? []),
    conversationDetails.campaign_context,
  ];

  const detailsToBeUpdated: Partial<IAgentEasyConversationDetails> = {
    campaign_context: campaignContext,
    previous_campaigns_contexts: previousContexts,
    updated_at: Timestamp.now(),
  };
  await conversationDocRef.update(detailsToBeUpdated);

  logger.info(`initChatToLead: updated conversation for ${docId}`);

  // send welcome message
  await sendWelcomeMessageToLead(leadDetails);
};

export const initAgentEasyChatToLead = async (
  leadDetails: IMetaLead,
): Promise<void> => {
  if (!leadDetails.campaign_id) {
    logger.error('sendAgentEasyWelcomeMessageToLead: campaign_id not found');
    return;
  }
  const agentEasyDataForCampaign = (
    await db
      .collection(FIRESTORE_COLLECTIONS.AGENTEASY_DATA)
      .doc(leadDetails.campaign_id)
      .get()
  ).data() as IAgentEasyDataForCampaign;

  if (!agentEasyDataForCampaign?.is_active) {
    // campaign not eligible for AgentEasy
    return;
  }

  if (
    !agentEasyDataForCampaign?.business_name ||
    !agentEasyDataForCampaign?.questions?.length
  ) {
    logger.error(
      `Missing agenteasy data for campaign ${leadDetails.campaign_id}`,
    );
    return;
  }

  try {
    await initChatToLead(leadDetails);
  } catch (error) {
    logger.error('initChatToLead', error);
  }
};

const processStatusesUpdate = async (
  payload: IWebhookWhatsappEventEntryChange['value'],
): Promise<void> => {
  logger.debug(payload);
};

export const checkAndProcessAgentEasyMessageFromWebhook = async (
  change: IWebhookWhatsappEventEntryChange,
): Promise<boolean> => {
  const payload = change.value;
  if (payload.messages?.length) {
    void processIncomingMessagesForAgentEasy(payload);
  }
  if (payload.statuses?.length) {
    void processStatusesUpdate(payload);
  }
  return true;
};

export const getAgentEasyChatMessages = async (
  queryParams: Record<string, any>,
  user?: IAuthUser,
): Promise<IAgentEasyWaMessageDetails[]> => {
  const chatDocId = queryParams.chat_doc_id;

  if (!chatDocId) {
    throw new ValidationError(400, 'Missing chat_doc_id');
  }
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  try {
    // Access check for non-admins
    if (!isGrowEasyAdmin(user)) {
      const conversationDetails =
        await getAgentEasyConversationDetails(chatDocId);
      if (conversationDetails?.campaign_context?.uid !== user.uid) {
        throw new ValidationError(401, 'You do not have access to this chat');
      }
    }

    const query = db
      .collection(FIRESTORE_COLLECTIONS.AGENTEASY)
      .doc(chatDocId)
      .collection(FIRESTORE_COLLECTIONS.MESSAGES)
      .orderBy('time', 'asc')
      .limit(500);

    const snapshot = await query.get();
    const messages: IAgentEasyWaMessageDetails[] = snapshot.docs.map(
      (doc) => doc.data() as IAgentEasyWaMessageDetails,
    );

    return messages;
  } catch (error) {
    logger.error('Error fetching AgentEasy chat details:', error);
    throw error;
  }
};

export const getAgentEasyConversationDetailsForAuthUser = async (
  queryParams: Record<string, any>,
  user?: IAuthUser,
): Promise<IAgentEasyConversationDetails | null> => {
  const chatDocId = queryParams.chat_doc_id;

  if (!chatDocId) {
    throw new ValidationError(400, 'Missing chat_doc_id');
  }
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  try {
    const conversationDetails =
      await getAgentEasyConversationDetails(chatDocId);
    // Access check for non-admins
    if (!isGrowEasyAdmin(user)) {
      if (conversationDetails?.campaign_context?.uid !== user.uid) {
        throw new ValidationError(401, 'You do not have access to this chat');
      }
    }

    return conversationDetails;
  } catch (error) {
    logger.error('Error fetching AgentEasy conversation details:', error);
    throw error;
  }
};
