import { tool } from '@langchain/core/tools';
import {
  type IAgentEasyConversationDetails,
  type IAgentEasyLeadClassificationData,
} from '../../types/agenteasy';
import { db } from '../../modules/firebase_admin_sdk';
import { FIRESTORE_COLLECTIONS } from '../../constants';
import { Timestamp } from 'firebase-admin/firestore';
import { updateLeadFromWebhook } from '../db_controller';
import logger from '../../modules/logger';

interface IUpdateLeadCategoryAndSummaryParams {
  chat_doc_id: string;
  lead_summary: string;
  lead_category: 'HOT' | 'WARM' | 'COLD';
}

interface IAddLeadAskedQuestionParams {
  chat_doc_id: string;
  question: string;
  answer: string;
}

const updateLeadCategoryAndSummary = async (
  params: IUpdateLeadCategoryAndSummaryParams,
): Promise<void> => {
  logger.info(`updateLeadCategoryAndSummary called for ${params.chat_doc_id}`);
  const docRef = db
    .collection(FIRESTORE_COLLECTIONS.AGENTEASY)
    .doc(params.chat_doc_id);
  const conversationDoc = await docRef.get();
  const conversationDetails =
    conversationDoc.data() as IAgentEasyConversationDetails;
  const currentClassificationData =
    conversationDetails?.campaign_context?.classification_data ?? {};

  const updatedClassificationData: IAgentEasyLeadClassificationData = {
    ...currentClassificationData,
    lead_summary: params.lead_summary,
    lead_category: params.lead_category,
    chat_doc_id: params.chat_doc_id,
  };

  await docRef.update({
    updated_at: Timestamp.now(),
    campaign_context: {
      ...conversationDetails?.campaign_context,
      classification_data: updatedClassificationData,
    },
  });

  await updateLeadFromWebhook({
    id: conversationDetails.campaign_context?.leadgen_id,
    agenteasy: updatedClassificationData,
  });
};

const addLeadAskedQuestion = async (
  params: IAddLeadAskedQuestionParams,
): Promise<void> => {
  logger.info(`addLeadAskedQuestion called for ${params.chat_doc_id}`);
  const docRef = db
    .collection(FIRESTORE_COLLECTIONS.AGENTEASY)
    .doc(params.chat_doc_id);
  const conversationDoc = await docRef.get();
  const conversationDetails =
    conversationDoc.data() as IAgentEasyConversationDetails;
  const currentClassificationData = conversationDetails?.campaign_context
    ?.classification_data as IAgentEasyLeadClassificationData;

  const updatedAskedQuestions = [
    ...(currentClassificationData.asked_questions ?? []),
    { question: params.question, answer: params.answer },
  ];

  const updatedClassificationData = {
    ...currentClassificationData,
    asked_questions: updatedAskedQuestions,
  };

  await docRef.update({
    updated_at: Timestamp.now(),
    campaign_context: {
      ...conversationDetails?.campaign_context,
      classification_data: updatedClassificationData,
    },
  });

  await updateLeadFromWebhook({
    id: conversationDetails.campaign_context?.leadgen_id,
    agenteasy: updatedClassificationData,
  });
};

export const updateLeadCategoryAndSummaryTool = tool(
  async (params) => {
    await updateLeadCategoryAndSummary(
      params as IUpdateLeadCategoryAndSummaryParams,
    );
    return 'Lead category & summary updated successfully';
  },
  {
    name: 'update_lead_category_and_summary',
    description: 'Update the lead classification category and summary.',
    schema: {
      type: 'object',
      properties: {
        chat_doc_id: {
          type: 'string',
          description: 'Conversation document ID',
        },
        lead_summary: { type: 'string', description: 'Lead summary text' },
        lead_category: {
          type: 'string',
          enum: ['HOT', 'WARM', 'COLD'],
          description: 'Lead classification category',
        },
      },
      required: ['chat_doc_id', 'lead_summary', 'lead_category'],
    },
  },
);

export const addLeadAskedQuestionTool = tool(
  async (params) => {
    await addLeadAskedQuestion(params as IAddLeadAskedQuestionParams);
    return 'Lead asked question added successfully';
  },
  {
    name: 'add_lead_asked_question',
    description: 'Add a new asked question and its answer for a lead.',
    schema: {
      type: 'object',
      properties: {
        chat_doc_id: {
          type: 'string',
          description: 'Conversation document ID',
        },
        question: { type: 'string', description: 'The asked question' },
        answer: { type: 'string', description: 'The answer to the question' },
      },
      required: ['chat_doc_id', 'question', 'answer'],
    },
  },
);

const markConversationAsClosed = async (params: {
  chat_doc_id: string;
}): Promise<void> => {
  logger.info(`markConversationAsClosed called for ${params.chat_doc_id}`);
  const chatDocId = params.chat_doc_id;
  const docRef = db.collection(FIRESTORE_COLLECTIONS.AGENTEASY).doc(chatDocId);
  const conversationDoc = await docRef.get();
  const conversationDetails =
    conversationDoc.data() as IAgentEasyConversationDetails;
  if (conversationDetails?.campaign_context?.chat_status === 'CLOSED') {
    return;
  }
  const detailsToBeUpdated: Partial<IAgentEasyConversationDetails> = {
    updated_at: Timestamp.now(),
    campaign_context: {
      ...conversationDetails?.campaign_context,
      chat_status: 'CLOSED',
    },
  };
  await docRef.update(detailsToBeUpdated);
};

export const markConversationAsClosedTool = tool(
  async (params) => {
    try {
      await markConversationAsClosed(
        params as {
          chat_doc_id: string;
        },
      );
      return 'Conversation closed successfully';
    } catch (error) {
      throw new Error(
        'Error in closing conversation: ' + (error as Error).message,
      );
    }
  },
  {
    name: 'mark_conversation_closed',
    description: `Close the conversation once sufficient questions are asked and final lead_category is determined`,
    schema: {
      type: 'object',
      properties: {
        chat_doc_id: {
          type: 'string',
          description:
            'Firestore document ID for the conversation with the lead',
        },
      },
      required: ['chat_doc_id'],
    },
  },
);
