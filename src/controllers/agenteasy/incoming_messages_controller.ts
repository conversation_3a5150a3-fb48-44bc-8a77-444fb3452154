import { Timestamp } from 'firebase-admin/firestore';
import logger from '../../modules/logger';
import {
  type IWaMessage,
  type IWebhookWhatsappEventEntryChange,
} from '../../types/whatsapp';
import { db } from '../../modules/firebase_admin_sdk';
import { FIRESTORE_COLLECTIONS } from '../../constants';
import {
  AgentEasyMessageRole,
  type IAgentEasyConversationDetails,
} from '../../types/agenteasy';
import { isFirestoreTimePast24Hours } from '../../utils';
import { getCampaignDetailsForGrowEasyAdmin } from '../admin_controller';
import { type ICampaign } from '../../types/campaign_details';
import { getAgentEasyConversationDetails, sendMessageToLead } from '.';
import { WaMessageType } from '../../modules/interakt/types';
import { initAgentEasyChatChain } from './llm';

const handleReplyForClosedOrExpiredConversation = async (
  chatDocId: string,
  conversationDetails: IAgentEasyConversationDetails,
): Promise<void> => {
  // get contact details of client
  const campaignDetailsDocSnapshot = await getCampaignDetailsForGrowEasyAdmin(
    conversationDetails.campaign_context?.campaign_id,
  );
  const campaignDetails = campaignDetailsDocSnapshot?.data() as ICampaign;
  const clientContactDetails = [];
  if (campaignDetails?.details?.business_details?.mobile) {
    clientContactDetails.push(
      campaignDetails?.details?.business_details?.mobile,
    );
  }
  if (campaignDetails?.details?.business_details?.website) {
    clientContactDetails.push(
      campaignDetails?.details?.business_details?.website,
    );
  }
  const textMessage = `Please contact our team for more details: ${clientContactDetails.join(
    ', ',
  )}`;
  await sendMessageToLead(chatDocId, {
    messaging_product: 'whatsapp',
    type: WaMessageType.text,
    text: {
      body: textMessage,
    },
    to: chatDocId,
  });
};

const handleReplyForOpenConversation = async (
  chatDocId: string,
  incomingMessage: IWaMessage,
): Promise<void> => {
  if (incomingMessage.type === WaMessageType.text) {
    const chain = await initAgentEasyChatChain({
      chatDocId,
    });
    await chain.invoke({ input: incomingMessage.text.body });
  }
};

export const processIncomingMessagesForAgentEasy = async (
  payload: IWebhookWhatsappEventEntryChange['value'],
): Promise<void> => {
  try {
    const message = payload.messages?.[0];
    const chatDocId = message?.from;
    if (!chatDocId) {
      logger.error(
        'message.from not found in processIncomingMessagesForAgentEasy',
      );
      return;
    }

    // store this message
    const messageDetails = {
      role: AgentEasyMessageRole.USER,
      time: Timestamp.now(),
      payload: message,
    };

    const docRef = db
      .collection(FIRESTORE_COLLECTIONS.AGENTEASY)
      .doc(chatDocId);
    const conversationDetails = (await getAgentEasyConversationDetails(
      chatDocId,
    )) as IAgentEasyConversationDetails;
    const conversationExpired = isFirestoreTimePast24Hours(
      conversationDetails?.user_last_reply_time,
    );
    const conversationClosed =
      conversationDetails?.campaign_context?.chat_status === 'CLOSED';

    const detailsToBeUpdated: Partial<IAgentEasyConversationDetails> = {
      updated_at: Timestamp.now(),
      last_message: messageDetails,
      user_last_reply_time: Timestamp.now(),
      campaign_context: conversationDetails?.campaign_context,
    };
    if (conversationExpired && detailsToBeUpdated.campaign_context) {
      detailsToBeUpdated.campaign_context.chat_status = 'EXPIRED';
    }

    // update conversation details
    await docRef.update(detailsToBeUpdated);

    // create conversation message
    await docRef
      .collection(FIRESTORE_COLLECTIONS.MESSAGES)
      .doc(message.id)
      .set(messageDetails);

    // handle reply
    if (conversationClosed || conversationExpired) {
      await handleReplyForClosedOrExpiredConversation(
        chatDocId,
        conversationDetails,
      );
    } else {
      await handleReplyForOpenConversation(chatDocId, message);
    }
  } catch (error) {
    logger.error(error);
  }
};
