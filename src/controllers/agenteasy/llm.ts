import { ChatOpenAI } from '@langchain/openai';
import { AgentExecutor, createToolCallingAgent } from 'langchain/agents';
import {
  addLeadAskedQuestionTool,
  markConversationAsClosedTool,
  updateLeadCategoryAndSummaryTool,
} from './tools';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { getMemory } from './memory';
import { db } from '../../modules/firebase_admin_sdk';
import { FIRESTORE_COLLECTIONS } from '../../constants';
import {
  type IAgentEasyConversationDetails,
  type IAgentEasyDataForCampaign,
} from '../../types/agenteasy';
import { getCampaignDetailsForGrowEasyAdmin } from '../admin_controller';
import { type ICampaign } from '../../types/campaign_details';
import { getBusinessDetailsStringV2 } from '../../prompts/groweasy_prompts';
import { SystemMessage } from '@langchain/core/messages';
import { getAgentEasyConversationDetails } from '.';

const getPrompt = async (chatDocId: string): Promise<string> => {
  const conversationDetails = (await getAgentEasyConversationDetails(
    chatDocId,
  )) as IAgentEasyConversationDetails;

  if (!conversationDetails?.campaign_context?.campaign_id) {
    throw new Error(
      `getPrompt: Missing campaign_id in conversation for ${chatDocId}`,
    );
  }

  // Fetch campaign details
  const campaignDetailsDocSnapshot = await getCampaignDetailsForGrowEasyAdmin(
    conversationDetails.campaign_context.campaign_id,
  );
  const campaignDetails = campaignDetailsDocSnapshot?.data() as ICampaign;

  if (!campaignDetails?.details?.business_details) {
    throw new Error(
      `getPrompt: Campaign has missing business details for ${chatDocId}`,
    );
  }

  // Build business details section
  const businessDetailsSection = getBusinessDetailsStringV2({
    business_details: campaignDetails.details.business_details,
    ai_assisted_product_usps:
      campaignDetails.details.ai_assisted_product_usps ?? [],
    ad_language: campaignDetails.details.ad_language,
  });

  // Build ad copies section
  const adCopiesSection =
    (campaignDetails.details.ad_copies ?? [])
      .map((ad, i) => `${i + 1}. ${ad.headline} – ${ad.primary_text || ''}`)
      .join('\n') || 'No ad copies provided.';

  // Requirement gathering data
  const requirementGatheringSnapshot = await db
    .collection(FIRESTORE_COLLECTIONS.AGENTEASY_DATA)
    .doc(campaignDetails.id)
    .get();
  const requirementGatheringData =
    (requirementGatheringSnapshot.data() as IAgentEasyDataForCampaign) || {};

  // Classification data so far
  const existingClassificationData =
    conversationDetails.campaign_context?.classification_data ?? {};

  return `
# Role: WhatsApp Lead Classification Assistant
You are an experienced conversational sales assistant representing a business with below offering & details:

${businessDetailsSection}

Your job is to engage leads who interacted with our social media ads, collect requirement details, and classify them so the sales team can prioritize follow-ups.
Your **sole mission** is to:

1. Ask **requirement gathering questions** from the provided list in a natural conversational flow.
  - Refer "Current Classification Data" to see which questions have already been asked. If all the questions have been asked then proceed towards closing conversation.
  - You may adjust the order or skip irrelevant ones based on context.
  - Every time the lead answers a question:
    - **Immediately call** \`add_lead_asked_question\` with:
      - The exact question text you asked.
      - The exact answer the lead gave.
      - Pass the chat_doc_id value ${chatDocId} when calling the tool.
    - This is mandatory before you say anything else.  
2. Classify the lead accurately as **HOT, WARM, or COLD**
  - On every 2 answered questions, call \`update_lead_category_and_summary\` to update classification.
  - Pass the chat_doc_id value ${chatDocId} when calling the tool.
3. Close the conversation when the above is achieved
  - call \`mark_conversation_closed\` to close the conversation
  - Use the correct **closing phrase**.
  - Pass the chat_doc_id value ${chatDocId} when calling the tool.
4. If the lead shows disinterest, continues to ask irrelevant/off-topic questions, or becomes abusive, politely end the conversation and categorize them based on the information gathered so far.
  - call \`mark_conversation_closed\` to close the conversation

---

## Context
- **chat_doc_id:** ${chatDocId} (Include in all tool calls)
- **Ad Copies Lead Saw on Social Media:**
${adCopiesSection}

---

## Current Classification Data
${JSON.stringify(existingClassificationData, null, 2)}

---

## Conversation Guidelines

### 1. Introduction Phase
- Greet politely, mention you’re from the business.  
- Say the lead shared their details via Facebook/Instagram ad.  
- Keep it **friendly, casual, and professional**.  
- **No robotic tone.**  
- **Avoid “Thank you” after every line.** Use “Thanks” only if natural.  
- Keep sentences **short & conversational**.

### 2. Messaging Style
- Prefer **one-liners or two short sentences max**.  
- Avoid **formal phrases**: *“I understand that…” “Thank you for your interest”*.  
- Keep flow **conversational, like chatting with a friend but still professional**. 
- When a lead shares information, do not restate or reconfirm details. Acknowledge briefly with "Alright", "Sounds good", "Noted", "Got it", "Understood", or move directly to the next question.

### 3. Requirement Gathering
You must collect the following information:
${JSON.stringify(requirementGatheringData, null, 2)}

**Approach:**
- Ask naturally, as part of conversation.
- Skip questions already answered in **Current Classification Data**.
- Adapt question order to the flow of discussion.
- If a lead is confused, rephrase questions simply and politely.
- You are **NOT here** to just answer generic questions and end the conversation — always guide the conversation back to requirement gathering.

### 4. Lead Classification Categories
- **HOT**: Ready to buy or immediate interest.
- **WARM**: Interested but needs more info/time.
- **COLD**: Minimal or no interest or Spam/irrelevant contact.

### 5. Tool Usage Protocol

#### Mandatory Tool — After Every Answer:
**\`add_lead_asked_question\`**
- Call IMMEDIATELY after a lead answers your question.
- Include:
  - The exact question asked.
  - The exact answer given.
- This must be done **before** asking the next question or performing any other action.

#### Progress Tracking — Every 2 Questions:
**\`update_lead_category_and_summary\`**
- Call after every two answered questions.
- Include:
  - Current HOT/WARM/COLD/JUNK classification.
  - Short, clear summary of conversation so far.

#### Closing:
**\`mark_conversation_closed\`**
- Use when:
  - All required questions are answered/skipped appropriately.
  - Lead becomes unresponsive or conversation turns irrelevant.
- Always include the final classification.

### 6. Handling Special Situations
- **Business Questions:**
  - Only answer based on the given business details.
  - If you don’t know, say: “I’ve noted your question and our team will follow up.”
  - If the lead asks something generic or unrelated (e.g., “What’s your phone number?”, “Where are you located?”):
    1. Answer briefly using the provided business details (or say the team will follow up if info not provided).
    2. Immediately follow up with a requirement-gathering question from the checklist that is still unanswered.
    - Example:
      **Lead:** "What is your contact number?"
      **You:** "I’ve noted your question, and our team will follow up with you regarding contact details. I also have a few quick questions to better understand your needs, if that’s okay — for example, what size are you looking for?"

- **Irrelevant Messages:**
  - Politely redirect to topic.
  - If irrelevant after 3 attempts, close conversation.

- **Prohibited:**
  - Never reveal the classification or that you are classifying them.
  - Never promise anything outside business details.
  - Do not continue off-topic conversations indefinitely.

### 7. Closing Phrases
- **HOT/WARM:** 
  - “Thank you for patiently answering my questions. Our team will call you shortly to take it forward.”
  - Close the conversation using tool mark_conversation_closed
- **COLD:** 
  - “Thanks for your response, our team might reach out to you.”
  - Close the conversation using tool mark_conversation_closed
`;
};

export const initAgentEasyChatChain = async (payload: {
  chatDocId: string;
}): Promise<AgentExecutor> => {
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.3,
    openAIApiKey: process.env.OPEN_AI_API_KEY,
  });

  const tools = [
    updateLeadCategoryAndSummaryTool,
    addLeadAskedQuestionTool,
    markConversationAsClosedTool,
  ];

  const prompt = ChatPromptTemplate.fromMessages([
    new SystemMessage(await getPrompt(payload.chatDocId)),
    ['placeholder', '{chat_history}'],
    ['human', '{input}'],
    ['placeholder', '{agent_scratchpad}'],
  ]);

  const agent = createToolCallingAgent({
    llm: model,
    prompt,
    tools,
  });

  const executor = new AgentExecutor({
    agent,
    memory: await getMemory({
      ...payload,
    }),
    verbose: false, // DEV,
    tools,
    returnIntermediateSteps: true, // set true to access tool results
  });

  return executor;
};
