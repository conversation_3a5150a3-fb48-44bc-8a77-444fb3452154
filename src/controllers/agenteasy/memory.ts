import { FirestoreChatMessageHistory } from '@langchain/community/stores/message/firestore';
import {
  AIMessage,
  type BaseMessage,
  HumanMessage,
} from '@langchain/core/messages';
import { WaMessageType } from '../../modules/interakt/types';
import { sendMessageToLead } from '.';
import { BufferMemory, type MemoryVariables } from 'langchain/memory';
import { FIRESTORE_COLLECTIONS } from '../../constants';
import { db } from '../../modules/firebase_admin_sdk';
import {
  AgentEasyMessageRole,
  type IAgentEasyWaMessageDetails,
} from '../../types/agenteasy';
import logger from '../../modules/logger';
import { type IWaTextMessage } from '../../types/whatsapp';

class AgentEasyFirestoreChatHistory extends FirestoreChatMessageHistory {
  private readonly chatDocId: string;

  constructor({ chatDocId }: any) {
    super({
      sessionId: chatDocId,
      userId: chatDocId,
      collectionName: 'DUMMY', // since I am overriding both addMessage & getMessages functions
    });

    this.chatDocId = chatDocId;
  }

  async addMessage(message: BaseMessage): Promise<void> {
    // only store AI messages, user messages have been stored already
    if (message.getType() === 'ai') {
      // this util internally sends to WhatsApp & stores to Firestore
      await sendMessageToLead(this.chatDocId, {
        messaging_product: 'whatsapp',
        type: WaMessageType.text,
        text: {
          body: message.text,
        },
        to: this.chatDocId,
      });
    }
  }

  async getMessages(): Promise<BaseMessage[]> {
    try {
      const messagesRef = db
        .collection(FIRESTORE_COLLECTIONS.AGENTEASY)
        .doc(this.chatDocId)
        .collection(FIRESTORE_COLLECTIONS.MESSAGES)
        .orderBy('time', 'asc');

      const snapshot = await messagesRef.get();

      if (snapshot.empty) {
        return [];
      }

      return snapshot.docs.map((doc) => {
        const data = doc.data() as IAgentEasyWaMessageDetails;
        const content = (data.payload as IWaTextMessage)?.text?.body ?? '';
        return data.role === AgentEasyMessageRole.ASSISTANT
          ? new AIMessage(content)
          : new HumanMessage(content);
      });
    } catch (err) {
      logger.error('Error in getMessages:', err);
      return [];
    }
  }
}

class LimitedBufferMemory extends BufferMemory {
  async loadMemoryVariables(values: any): Promise<MemoryVariables> {
    const result = await super.loadMemoryVariables(values);
    if (result.chat_history && Array.isArray(result.chat_history)) {
      result.chat_history = result.chat_history.slice(-20); // Keep last 20 messages
    }
    logger.debug(result);
    return result;
  }
}

export const getMemory = async (payload: {
  chatDocId: string; // WhatsApp phone number
}): Promise<LimitedBufferMemory> => {
  const { chatDocId } = payload;

  const chatHistory = new AgentEasyFirestoreChatHistory({
    chatDocId,
  });

  return new LimitedBufferMemory({
    chatHistory,
    memoryKey: 'chat_history', // Key used in the prompt template
    inputKey: 'input', // Matches the chain input key
    outputKey: 'output', // Matches the chain output key
    returnMessages: true, // Required for chat models
  });
};
