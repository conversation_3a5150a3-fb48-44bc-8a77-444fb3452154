import razorpay, {
  generateRazorpaySignature,
  generateRazorpayWebhookSignature,
  zendotRazorpayInstance,
} from '../modules/razorpay';
import stripe from '../modules/stripe';
import { auth, db } from '../modules/firebase_admin_sdk';
import {
  Currency,
  GrowEasyCampaignStatus,
  type ICampaign,
} from '../types/campaign_details';
import { FIRESTORE_COLLECTIONS, PRICE_PER_VIDEO } from '../constants';
import {
  GrowEasyOrderType,
  GrowEasyPaymentSource,
  type IRazorpayError,
  type ICampaignExtensionDetails,
  type IOrderDetails,
  type IXenditWebhookEventBody,
} from '../types/payments_invoices';
import { type DocumentData, Timestamp } from 'firebase-admin/firestore';
import {
  AdPlatforms,
  GrowEasyPartners,
  type IVideoOrderDetails,
  type IVideoOrdersReqPayload,
  type IAuthUser,
  type IVerifyPaymentPayload,
  type IMasterClassRegistrationDetails,
  type IMasterClassOrderDetails,
} from '../types';
import ValidationError from '../utils/validation_error';
import { updateAdset, updateMetaCampaign } from './meta_controller';
import {
  addCampaignToRunningList,
  getCampaignCurrencyBudgetNode,
  getFormattedDateAsPerGoogleAds,
  getMinOrderAmountForCurrency,
  updateFirestoreCampaign,
} from './util';
import {
  sendMasterClassPaymentReceivedEmail,
  sendPaymentSuccessEmail,
  sendVideosPaymentReceivedEmail,
} from './email_controller';
import logger from '../modules/logger';
import { generateInvoices } from './invoice_controller';
import { getDateDifferenceInDays } from '../utils';
import { getUserProfile } from './user_controller';
import COUNTRIES from '../constants/countries';
import {
  getGoogleAdsDataFromSubCollection,
  getTiktokAdsDataFromSubCollection,
} from './db_controller';
import {
  updateGoogleCampaign,
  updateGoogleCampaignBudget,
} from './google/google_controller';
import { writeOrderToBusinessTransactionsSheet } from './admin_controller';
import xendit from '../modules/xendit';
import { InvoiceStatus } from 'xendit-node/invoice/models';
import { type XenditSdkError } from 'xendit-node';
import {
  createAdCreditTransaction,
  getUsersAdCreditBalanceForACurrency,
} from './ad_credits_controller';
import { storeAgentEasyDataForCampaign } from './agenteasy';
import {
  updateTiktokAdGroup,
  updateTiktokCampaign,
} from './tiktok/tiktok_controller';

// Razorpay Order is always created but payment can be done either via Razorpay (INR) / Stripe (USD) / Xendit (IDR etc)
export const createRazorpayOrder = async (payload: {
  amount: number; // in paise or cents
  campaign_id: string;
  uid: string;
  type: GrowEasyOrderType;
  campaign_extension_details?: ICampaignExtensionDetails;
  currency?: Currency;
  platform?: AdPlatforms;
}): Promise<IOrderDetails> => {
  try {
    const currency = payload.currency ?? Currency.INR;
    const userDetails = await auth.getUser(payload.uid);
    let razorpayOrdres = razorpay.orders;
    if (
      userDetails?.customClaims?.profile?.partner === GrowEasyPartners.ZENDOT
    ) {
      razorpayOrdres = zendotRazorpayInstance.orders;
    }

    // if ad credit is available, it should get autodeducted on payment success
    const currencyConversionFactor = [Currency.IDR, Currency.VND].includes(
      currency,
    )
      ? 1
      : 100;
    const totalAdCreditAmountInSmallestSubunit =
      (await getUsersAdCreditBalanceForACurrency(payload.uid, currency)) *
      currencyConversionFactor;
    let adCreditAmountToBeConsumed = 0;

    // 100 paise, 100 cents or 1000 IDR, min amount to pay if sufficient credit is available
    const minOrderAmountAsPerCurrency = getMinOrderAmountForCurrency(currency);

    // case when credit can cover whole ad budget
    if (totalAdCreditAmountInSmallestSubunit >= payload.amount) {
      adCreditAmountToBeConsumed = payload.amount - minOrderAmountAsPerCurrency;
    } else if (totalAdCreditAmountInSmallestSubunit > 0) {
      adCreditAmountToBeConsumed = totalAdCreditAmountInSmallestSubunit;
    }

    // always an integer
    const orderAmount = Math.ceil(payload.amount - adCreditAmountToBeConsumed);

    logger.info(`order amount: ${orderAmount}, 
      currency: ${currency},
      adCreditAmountToBeConsumed ${adCreditAmountToBeConsumed}, 
      totalAdCreditAmountInSmallestSubunit: ${totalAdCreditAmountInSmallestSubunit}`);

    const razorpayOrder = await razorpayOrdres.create({
      amount: orderAmount, // in paise or cents or IDR (for 0 decimal)
      currency, // spectific to a meta ad account
      // we use notes.source to identify source in razorpay webhook
      notes: {
        ...payload,
        source: GrowEasyPaymentSource.GROWEASY_AI,
        campaign_extension_details: JSON.stringify(
          payload.campaign_extension_details ?? {},
        ),
      },
    });
    const order: IOrderDetails = {
      ...payload,
      amount: orderAmount,
      razorpay_order_id: razorpayOrder.id,
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
      currency,
      status: razorpayOrder.status,
      ad_credit: {
        amount_to_be_consumed: adCreditAmountToBeConsumed,
      },
    };

    // write to Firestore
    await db.collection(FIRESTORE_COLLECTIONS.ORDERS).add(order);
    return order;
  } catch (error) {
    if ((error as IRazorpayError)?.error?.description) {
      throw new ValidationError(
        Number((error as IRazorpayError).statusCode),
        (error as IRazorpayError).error.description,
      );
    } else {
      throw error;
    }
  }
};

// todo: Google extend has issue due to unavailability of lifetime_budget, caution
const processExtendedCampaign = async (payload: {
  campaign: ICampaign;
  orderDetails: Partial<IOrderDetails>;
  campaignDoc: DocumentData;
}): Promise<void> => {
  const { campaign, orderDetails, campaignDoc } = payload;

  const budgetAndScheduling = campaign?.details?.budget_and_scheduling;

  const currency = budgetAndScheduling?.currency ?? Currency.INR;
  const campaignCurrencyBudgetNode =
    getCampaignCurrencyBudgetNode(budgetAndScheduling);
  if (campaign.meta_adset_id) {
    // update lifetime_budget post calculation and campaign end_time
    let lifetimeBudget =
      (orderDetails.campaign_extension_details?.lifetime_budget ?? 0) *
      (campaignCurrencyBudgetNode?.exchange_rate ?? 1);
    // deduct platform fee
    lifetimeBudget =
      lifetimeBudget -
      (lifetimeBudget * (budgetAndScheduling?.platform_fee_percentage ?? 0)) /
        100;
    if ([Currency.IDR, Currency.VND].includes(currency)) {
      // lifetimeBudget stored in db is in IDR, multiplying by exchange_rate got it converted into INR
      // so for 0 decimal currencies, multiply by 100 since Meta accepts budget in paise
      lifetimeBudget = lifetimeBudget * 100;
    }
    // has to be an integer
    lifetimeBudget = Math.floor(lifetimeBudget);
    if (
      campaign.details?.config?.advantage_campaign_budget &&
      campaign.meta_id
    ) {
      await updateMetaCampaign(campaign.meta_id, {
        lifetime_budget: lifetimeBudget,
      });
      await updateAdset(campaign.meta_adset_id, {
        end_time: orderDetails.campaign_extension_details?.end_time ?? '',
      });
    } else {
      await updateAdset(campaign.meta_adset_id, {
        lifetime_budget: lifetimeBudget,
        end_time: orderDetails.campaign_extension_details?.end_time ?? '',
      });
    }
  } else if (
    campaign.google_ads_data?.campaign_budget_resource &&
    campaign.google_ads_data?.campaign_resource
  ) {
    let dailyBudget =
      (orderDetails.campaign_extension_details?.daily_budget ?? 0) *
      (campaignCurrencyBudgetNode?.exchange_rate ?? 1);
    // deduct platform fee
    dailyBudget =
      dailyBudget -
      (dailyBudget * (budgetAndScheduling?.platform_fee_percentage ?? 0)) / 100;
    await updateGoogleCampaignBudget(
      campaign.google_ads_data?.campaign_budget_resource,
      {
        // division by 100 because amount is in paise (2 decimal currencies only), multiplication by 10^6 because amount has to be in micro
        amountMicros:
          dailyBudget *
          (1000000 /
            ([Currency.IDR, Currency.VND].includes(currency) ? 1 : 100)),
      },
      campaign.details?.config?.google_ad_account_id,
    );
    await updateGoogleCampaign(
      campaign.google_ads_data.campaign_resource,
      {
        endDate: getFormattedDateAsPerGoogleAds(
          new Date(orderDetails.campaign_extension_details?.end_time ?? ''),
        ),
      },
      campaign.details?.config?.google_ad_account_id,
    );
  } else if (
    campaign.tiktok_ads_data?.campaign_id &&
    campaign.tiktok_ads_data?.adgroup_id
  ) {
    let lifetimeBudget =
      orderDetails.campaign_extension_details?.lifetime_budget ?? 0;
    // deduct platform fee
    lifetimeBudget =
      lifetimeBudget -
      (lifetimeBudget * (budgetAndScheduling?.platform_fee_percentage ?? 0)) /
        100;
    await updateTiktokCampaign({
      campaign_id: campaign.tiktok_ads_data?.campaign_id,
      budget: lifetimeBudget,
    });
    await updateTiktokAdGroup({
      adgroup_id: campaign.tiktok_ads_data?.adgroup_id,
      schedule_end_time:
        orderDetails.campaign_extension_details?.end_time ?? '',
    });
  }

  // todo, handle extend for non INR currency?
  await campaignDoc.ref.update({
    'details.budget_and_scheduling.lifetime_budget':
      orderDetails.campaign_extension_details?.lifetime_budget,
    'details.budget_and_scheduling.daily_budget':
      orderDetails.campaign_extension_details?.daily_budget ??
      campaign.details?.budget_and_scheduling?.daily_budget,
    'details.budget_and_scheduling.end_time':
      orderDetails.campaign_extension_details?.end_time,
  });
  // db is updated but we also need to update in memory (here)
  // so that payment mail has updated campaign info
  if (campaign?.details?.budget_and_scheduling) {
    campaign.details.budget_and_scheduling.lifetime_budget =
      orderDetails.campaign_extension_details?.lifetime_budget ?? 0;
    campaign.details.budget_and_scheduling.end_time =
      orderDetails.campaign_extension_details?.end_time ?? '';
    if (orderDetails.campaign_extension_details?.daily_budget) {
      campaign.details.budget_and_scheduling.daily_budget =
        orderDetails.campaign_extension_details?.daily_budget;
    }
  }
};

const processLaunchedCampaign = async (payload: {
  campaign: ICampaign;
  campaignDoc: DocumentData;
}): Promise<void> => {
  const { campaign, campaignDoc } = payload;

  // end date might be obsolete, Adjust this along with start date
  if (campaign?.details?.budget_and_scheduling) {
    const campaignStartTimeString =
      campaign?.details?.budget_and_scheduling?.start_time;
    const campaignEndTimeString =
      campaign?.details?.budget_and_scheduling?.end_time ?? '';
    const noOfDays = getDateDifferenceInDays(
      new Date(campaignStartTimeString),
      new Date(campaignEndTimeString),
    );
    const newStartDate = new Date();
    const newEndDate = new Date();
    newEndDate.setDate(newStartDate.getDate() + noOfDays);
    if (campaign.meta_adset_id) {
      // update ad set, no need to update start time here
      await updateAdset(campaign.meta_adset_id, {
        end_time: newEndDate.toISOString(),
      });
    } else if (campaign.google_ads_data?.campaign_resource) {
      await updateGoogleCampaign(
        campaign.google_ads_data.campaign_resource,
        {
          endDate: getFormattedDateAsPerGoogleAds(newEndDate),
        },
        campaign.details?.config?.google_ad_account_id,
      );
    } else if (campaign.tiktok_ads_data?.adgroup_id) {
      await updateTiktokAdGroup({
        adgroup_id: campaign.tiktok_ads_data?.adgroup_id,
        schedule_end_time: newEndDate.toISOString(),
      });
    }
    // update Firestore, start time updation is required so that correct "no of days left" is visible on UI
    await campaignDoc.ref.update({
      'details.budget_and_scheduling.end_time': newEndDate.toISOString(),
      'details.budget_and_scheduling.start_time': newStartDate.toISOString(),
    });
    // update in memory
    campaign.details.budget_and_scheduling.end_time = newEndDate.toISOString();
    campaign.details.budget_and_scheduling.start_time =
      newStartDate.toISOString();

    // for AgentEasy
    void storeAgentEasyDataForCampaign(campaign);
  }
};

// call once payment is verified
export const markOrderAsPaidAndActivateCampaign = async ({
  razorpayOrderId,
  user,
  updatedOrderDetails,
}: {
  razorpayOrderId: string;
  user?: IAuthUser;
  updatedOrderDetails: Partial<IOrderDetails>;
}): Promise<{ order: Partial<IOrderDetails> }> => {
  const orderRef = db.collection(FIRESTORE_COLLECTIONS.ORDERS);
  const orderSnapshot = await orderRef
    .where('razorpay_order_id', '==', razorpayOrderId)
    .get();
  const orderDoc = orderSnapshot.docs[0];

  if (!orderDoc?.id) {
    throw new ValidationError(400, 'Order not found, please contact support');
  }
  const orderDocData = orderDoc.data() as IOrderDetails;

  // either from webhook or from callback handler
  // since callback handler is not reliable, we have added webhook trigger as well
  if (orderDocData?.status === 'paid') {
    logger.info('Order is already marked as paid');
    return {
      order: orderDocData,
    };
  }

  // get campaign details
  const campaignSnapshot = await db
    .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
    .where('id', '==', orderDocData.campaign_id)
    .get();
  const campaignDoc = campaignSnapshot.docs[0];
  const campaign = campaignDoc?.data() as ICampaign;
  const adPlatform = campaign.platform ?? AdPlatforms.META;

  // include platform data too
  if (campaignDoc?.id) {
    if (adPlatform === AdPlatforms.GOOGLE) {
      campaign.google_ads_data = await getGoogleAdsDataFromSubCollection(
        campaignDoc.id,
      );
    } else if (adPlatform === AdPlatforms.TIKTOK) {
      campaign.tiktok_ads_data = await getTiktokAdsDataFromSubCollection(
        campaignDoc.id,
      );
    }
  }

  // deduct ad credit
  let adCreditTransactionId: string | null = null;
  try {
    if (orderDocData.ad_credit?.amount_to_be_consumed) {
      adCreditTransactionId = await createAdCreditTransaction(
        orderDocData.uid,
        {
          type: 'DEBIT',
          currency: orderDocData.currency,
          value:
            orderDocData.ad_credit.amount_to_be_consumed /
            ([Currency.IDR, Currency.VND].includes(orderDocData.currency)
              ? 1
              : 100),
          description: `Debit for campaign ${
            campaign?.friendly_name ??
            campaign?.details?.business_details?.business_category
          }`,
          campaign_id: orderDocData.campaign_id,
        },
      );
    }
  } catch (error) {
    // not enough credit,
    // since we are already checking credit balance before initiating payment, this should not happen
    logger.error(
      `markOrderAsPaidAndActivateCampaign error in createAdCreditTransaction for order ${orderDocData.razorpay_order_id}`,
    );
    throw error;
  }

  // Update order
  const orderDetailsToBeUpdated: Partial<IOrderDetails> = {
    status: 'paid',
    updated_at: Timestamp.now(),
    ad_credit: {
      ...orderDocData.ad_credit,
      amount_to_be_consumed: orderDocData.ad_credit?.amount_to_be_consumed ?? 0,
      transaction_id: adCreditTransactionId,
    },
    ...updatedOrderDetails,
  };

  await orderRef.doc(orderDoc.id).set(orderDetailsToBeUpdated, {
    merge: true,
  });
  const orderDetails = {
    ...orderDoc.data(),
    ...orderDetailsToBeUpdated,
  };

  // campaign data sanity check
  if (
    adPlatform === AdPlatforms.META &&
    (!campaign?.meta_id || !campaign?.meta_adset_id)
  ) {
    throw new ValidationError(
      400,
      'Campaign is incomplete, please contact support',
    );
  }
  if (
    adPlatform === AdPlatforms.GOOGLE &&
    (!campaign?.google_ads_data?.campaign_resource ||
      !campaign?.google_ads_data?.campaign_budget_resource)
  ) {
    throw new ValidationError(
      400,
      'Campaign is incomplete, please contact support',
    );
  }
  if (
    adPlatform === AdPlatforms.TIKTOK &&
    !campaign?.tiktok_ads_data?.campaign_id
  ) {
    throw new ValidationError(
      400,
      'Campaign is incomplete, please contact support',
    );
  }

  // Extend vs Launch
  if (orderDetails.type === GrowEasyOrderType.EXTEND) {
    await processExtendedCampaign({
      campaign,
      orderDetails,
      campaignDoc,
    });
  } else {
    await processLaunchedCampaign({
      campaign,
      campaignDoc,
    });
  }

  if (campaign.meta_id) {
    // activate campaign on Meta
    await updateMetaCampaign(campaign.meta_id, {
      status: GrowEasyCampaignStatus.ACTIVE,
    });
  } else if (campaign.google_ads_data?.campaign_resource) {
    // activate campaign on Google
    await updateGoogleCampaign(
      campaign.google_ads_data.campaign_resource,
      {
        status: 'ENABLED',
      },
      campaign.details?.config?.google_ad_account_id,
    );
  } else if (campaign.tiktok_ads_data?.campaign_id) {
    await updateTiktokCampaign({
      campaign_id: campaign.tiktok_ads_data?.campaign_id,
      operation_status: 'ENABLE',
    });
  }

  // update status in db
  await updateFirestoreCampaign(campaignDoc.id, {
    status: GrowEasyCampaignStatus.ACTIVE,
    updated_at: Timestamp.now(),
  });
  void addCampaignToRunningList(campaign);

  // generate invoice
  await generateInvoices({
    order_ids: [orderDetails.razorpay_order_id ?? ''],
  });
  // in case of request from Razorpay (non-auth call), user will not be there
  if (!user) {
    const userDetails = await auth.getUser(campaign.uid);
    user = {
      name: userDetails?.displayName ?? '',
      uid: userDetails.uid,
      email: userDetails.email,
    };
  }

  // trigger payment success + campaign launched email
  void sendPaymentSuccessEmail({
    campaign,
    user,
    orderDetails,
  });

  // write transaction into sheet
  writeOrderToBusinessTransactionsSheet({
    order: orderDetails as IOrderDetails,
  }).catch((error) => {
    logger.error(error);
  });
  return {
    order: orderDetails,
  };
};

export const verifyRazorpayPayment = async (
  payload: IVerifyPaymentPayload,
  user?: IAuthUser,
): Promise<{ order: Partial<IOrderDetails | IVideoOrderDetails> }> => {
  const {
    razorpay_order_id: razorpayOrderId,
    razorpay_payment_id: razorpayPaymentId,
    razorpay_signature: razorpaySignature,
    razorpay_webhook_body: razorpayWebhookBody,
  } = payload;
  if (!razorpayOrderId || !razorpayPaymentId || !razorpaySignature) {
    throw new ValidationError(
      400,
      'Missing razorpay_order_id / razorpay_payment_id / razorpay_signature',
    );
  }
  const generatedSignature = razorpayWebhookBody
    ? generateRazorpayWebhookSignature(razorpayWebhookBody)
    : generateRazorpaySignature({
        razorpayOrderId,
        razorpayPaymentId,
        partner: user?.partner,
      });
  if (generatedSignature === razorpaySignature) {
    // update order details
    if (payload.payment_source === GrowEasyPaymentSource.GROWEASY_VIDEOS) {
      const response = await markVideoOrderAsPaid({
        razorpayOrderId,
        updatedOrderDetails: {
          razorpay_payment_id: razorpayPaymentId,
        },
      });
      return response;
    } else if (
      payload.payment_source === GrowEasyPaymentSource.GROWEASY_MASTER_CLASS
    ) {
      const response = await markMasterClassOrderAsPaid({
        razorpayOrderId,
        updatedOrderDetails: {
          razorpay_payment_id: razorpayPaymentId,
        },
      });
      return response;
    } else {
      // do not fail webhook
      try {
        const response = await markOrderAsPaidAndActivateCampaign({
          razorpayOrderId,
          user,
          updatedOrderDetails: {
            razorpay_payment_id: razorpayPaymentId,
          },
        });
        return response;
      } catch (error) {
        logger.error(error);
        return { order: {} };
      }
    }
  } else {
    throw new ValidationError(400, 'Signature mismatch');
  }
};

export const createStripePaymentIntent = async (
  razorpayOrderId: string,
  user?: IAuthUser,
): Promise<{
  client_secret: string | null;
  stripe_payment_intent_id: string;
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const orderRef = db.collection(FIRESTORE_COLLECTIONS.ORDERS);
    const orderSnapshot = await orderRef
      .where('razorpay_order_id', '==', razorpayOrderId)
      .where('uid', '==', user?.uid)
      .get();
    const orderDoc = orderSnapshot.docs[0];

    if (!orderDoc?.id) {
      throw new ValidationError(400, 'Order not found, please contact support');
    }
    const orderDetails = orderDoc.data() as IOrderDetails;
    const userProfile = await getUserProfile(user);
    const userCountry = COUNTRIES.find(
      (item) => item.dial_code === userProfile?.mobile_dial_code,
    );
    // if valid payment intent exists, return the same
    if (orderDetails?.stripe_payment_intent_id) {
      const existingPaymentIntent = await stripe.paymentIntents.retrieve(
        orderDetails?.stripe_payment_intent_id,
      );
      if (existingPaymentIntent.status !== 'canceled') {
        return {
          client_secret: existingPaymentIntent.client_secret,
          stripe_payment_intent_id: existingPaymentIntent.id,
        };
      }
    }

    const customer = await stripe.customers.create({
      name: userProfile?.name,
      email: userProfile?.email,
      address: {
        line1: userProfile?.business_name ?? '',
        country: userCountry?.code,
      },
    });
    const paymentIntent = await stripe.paymentIntents.create({
      amount: orderDetails.amount, // Amount in the smallest unit of currency (cents for USD)
      currency: orderDetails.currency,
      description: 'Lead generation services',
      customer: customer.id,
    });
    // write stripe_payment_intent_id to order
    const orderDetailsToBeUpdated: Partial<IOrderDetails> = {
      stripe_payment_intent_id: paymentIntent.id,
      updated_at: Timestamp.now(),
    };
    await orderRef.doc(orderDoc.id).set(orderDetailsToBeUpdated, {
      merge: true,
    });
    return {
      client_secret: paymentIntent.client_secret,
      stripe_payment_intent_id: paymentIntent.id,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const verifyStripePayment = async (
  payload: {
    stripe_payment_intent_id: string;
  },
  user?: IAuthUser,
): Promise<{ order: Partial<IOrderDetails> }> => {
  const { stripe_payment_intent_id: paymentIntentId } = payload;
  if (!paymentIntentId) {
    throw new ValidationError(400, 'Missing stripe_payment_intent_id');
  }
  const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
  logger.info('Payment intent status', paymentIntent.status);
  if (paymentIntent.status === 'succeeded') {
    const orderRef = db.collection(FIRESTORE_COLLECTIONS.ORDERS);
    const orderSnapshot = await orderRef
      .where('stripe_payment_intent_id', '==', paymentIntentId)
      .get();
    const orderDoc = orderSnapshot.docs[0];
    if (!orderDoc?.id) {
      throw new ValidationError(400, 'Order not found, please contact support');
    }
    const orderDetails = orderDoc.data() as IOrderDetails;
    // do not fail webhook
    try {
      const response = await markOrderAsPaidAndActivateCampaign({
        razorpayOrderId: orderDetails.razorpay_order_id,
        user,
        updatedOrderDetails: {
          stripe_payment_intent_id: paymentIntentId,
        },
      });
      return response;
    } catch (error) {
      logger.error(error);
      return { order: {} };
    }
  } else {
    throw new ValidationError(400, `Payment Status: ${paymentIntent.status}`);
  }
};

export const createRazorpayVideoOrder = async (
  payload: IVideoOrdersReqPayload,
): Promise<IVideoOrderDetails> => {
  const videosPrice = PRICE_PER_VIDEO * payload.video_details?.length;
  const videosPriceIncludingGst = videosPrice + Math.ceil(videosPrice * 0.18); // 18% GST
  const currency = 'INR';
  const amount = videosPriceIncludingGst * 100;
  const razorpayOrder = await razorpay.orders.create({
    amount, // in paise
    currency,
    // we use notes.source to identify source in razorpay webhook
    notes: {
      source: GrowEasyPaymentSource.GROWEASY_VIDEOS,
      ...payload.user_details,
    },
  });
  const order: IVideoOrderDetails = {
    ...payload,
    razorpay_order_id: razorpayOrder.id,
    created_at: Timestamp.now(),
    updated_at: Timestamp.now(),
    currency,
    amount,
    status: razorpayOrder.status,
  };
  // write to Firestore
  await db.collection(FIRESTORE_COLLECTIONS.VIDEOS_ORDERS).add(order);
  return order;
};

// call once payment is verified
export const markVideoOrderAsPaid = async ({
  razorpayOrderId,
  updatedOrderDetails,
}: {
  razorpayOrderId: string;
  updatedOrderDetails: Partial<IVideoOrderDetails>;
}): Promise<{ order: Partial<IVideoOrderDetails> }> => {
  const orderRef = db.collection(FIRESTORE_COLLECTIONS.VIDEOS_ORDERS);
  const orderSnapshot = await orderRef
    .where('razorpay_order_id', '==', razorpayOrderId)
    .get();
  const orderDoc = orderSnapshot.docs[0];

  if (!orderDoc?.id) {
    throw new ValidationError(
      400,
      'Video Order not found, please contact support',
    );
  }
  const orderDocData = orderDoc.data() as IVideoOrderDetails;

  // either from webhook or from callback handler
  if (orderDocData?.status === 'paid') {
    logger.info('Video Order is already marked as paid');
    return {
      order: orderDocData,
    };
  }

  const orderDetailsToBeUpdated: Partial<IVideoOrderDetails> = {
    status: 'paid',
    updated_at: Timestamp.now(),
    ...updatedOrderDetails,
  };

  await orderRef.doc(orderDoc.id).set(orderDetailsToBeUpdated, {
    merge: true,
  });
  const orderDetails = {
    ...(orderDoc.data() as IVideoOrderDetails),
    ...orderDetailsToBeUpdated,
  };

  void sendVideosPaymentReceivedEmail(orderDetails);
  return {
    order: orderDetails,
  };
};

export const createRazorpayMasterClassOrder = async (
  payload: IMasterClassRegistrationDetails,
): Promise<IMasterClassOrderDetails> => {
  const currency = 'INR';
  const amount = 199 * 100; // Rs 199/
  const razorpayOrder = await razorpay.orders.create({
    amount, // in paise
    currency,
    // we use notes.source to identify source in razorpay webhook
    notes: {
      source: GrowEasyPaymentSource.GROWEASY_MASTER_CLASS,
      ...payload,
    },
  });
  const order: IMasterClassOrderDetails = {
    registration_details: payload,
    razorpay_order_id: razorpayOrder.id,
    created_at: Timestamp.now(),
    updated_at: Timestamp.now(),
    currency,
    amount,
    status: razorpayOrder.status,
  };
  // write to Firestore
  await db.collection(FIRESTORE_COLLECTIONS.MASTER_CLASS_ORDERS).add(order);
  return order;
};

// call once payment is verified
export const markMasterClassOrderAsPaid = async ({
  razorpayOrderId,
  updatedOrderDetails,
}: {
  razorpayOrderId: string;
  updatedOrderDetails: Partial<IMasterClassOrderDetails>;
}): Promise<{ order: Partial<IMasterClassOrderDetails> }> => {
  const orderRef = db.collection(FIRESTORE_COLLECTIONS.MASTER_CLASS_ORDERS);
  const orderSnapshot = await orderRef
    .where('razorpay_order_id', '==', razorpayOrderId)
    .get();
  const orderDoc = orderSnapshot.docs[0];

  if (!orderDoc?.id) {
    throw new ValidationError(
      400,
      'MasterClass Order not found, please contact support',
    );
  }
  const orderDocData = orderDoc.data() as IMasterClassOrderDetails;

  // either from webhook or from callback handler
  if (orderDocData?.status === 'paid') {
    logger.info('Order is already marked as paid');
    return {
      order: orderDocData,
    };
  }

  const orderDetailsToBeUpdated: Partial<IMasterClassOrderDetails> = {
    status: 'paid',
    updated_at: Timestamp.now(),
    ...updatedOrderDetails,
  };

  await orderRef.doc(orderDoc.id).set(orderDetailsToBeUpdated, {
    merge: true,
  });
  const orderDetails = {
    ...(orderDoc.data() as IMasterClassOrderDetails),
    ...orderDetailsToBeUpdated,
  };

  void sendMasterClassPaymentReceivedEmail(orderDetails);
  return {
    order: orderDetails,
  };
};

export const createXenditInvoice = async (
  razorpayOrderId: string,
  user?: IAuthUser,
): Promise<{
  id?: string;
  invoice_url: string;
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const orderRef = db.collection(FIRESTORE_COLLECTIONS.ORDERS);
    const orderSnapshot = await orderRef
      .where('razorpay_order_id', '==', razorpayOrderId)
      .where('uid', '==', user?.uid)
      .get();
    const orderDoc = orderSnapshot.docs[0];

    if (!orderDoc?.id) {
      throw new ValidationError(400, 'Order not found, please contact support');
    }
    const orderDetails = orderDoc.data() as IOrderDetails;
    const userProfile = await getUserProfile(user);

    // if valid invoice exists, return the same
    if (orderDetails?.xendit_invoice_id) {
      const existingInvoice = await xendit.getInvoiceById(
        orderDetails.xendit_invoice_id,
      );
      if (existingInvoice.status !== InvoiceStatus.Expired) {
        return {
          id: existingInvoice.id,
          invoice_url: existingInvoice.invoiceUrl,
        };
      }
    }

    const redirectUrl = `https://adglobalai.com/payment?campaign_id=${orderDetails.campaign_id}&order_id=${orderDetails.razorpay_order_id}`;
    const xenditInvoice = await xendit.createInvoice({
      // Xendit does not expect amount in smallest unit so converting it back
      externalId: orderDetails.razorpay_order_id,
      amount:
        orderDetails.amount /
        ([Currency.IDR, Currency.VND].includes(orderDetails.currency)
          ? 1
          : 100),
      currency: orderDetails.currency,
      customer: {
        email: userProfile?.email,
      },
      description: 'Digital marketing services',
      invoiceDuration: `${3600 * 24 * 7}`, // 7 days
      successRedirectUrl: redirectUrl,
      failureRedirectUrl: redirectUrl,
      metadata: {
        campaign_id: orderDetails.campaign_id,
        uid: orderDetails.uid,
      },
    });

    // write xendit_invoice_id to order
    const orderDetailsToBeUpdated: Partial<IOrderDetails> = {
      xendit_invoice_id: xenditInvoice.id,
      updated_at: Timestamp.now(),
    };
    await orderRef.doc(orderDoc.id).set(orderDetailsToBeUpdated, {
      merge: true,
    });
    return {
      id: xenditInvoice.id,
      invoice_url: xenditInvoice.invoiceUrl,
    };
  } catch (error) {
    // console.dir(error, { depth: null });
    logger.error((error as XenditSdkError)?.rawResponse ?? error);
    throw error;
  }
};

export const verifyXenditPayment = async (
  payload: IXenditWebhookEventBody,
): Promise<{ order: Partial<IOrderDetails> } | null> => {
  const { id: xenditInvoiceId } = payload;
  if (!xenditInvoiceId) {
    throw new ValidationError(400, 'Missing id');
  }
  const xenditInvoice = await xendit.getInvoiceById(xenditInvoiceId);
  logger.info('Xendit invoice status', xenditInvoice.status);
  if (
    xenditInvoice.status === InvoiceStatus.Paid ||
    xenditInvoice.status === InvoiceStatus.Settled
  ) {
    const orderRef = db.collection(FIRESTORE_COLLECTIONS.ORDERS);
    const orderSnapshot = await orderRef
      .where('xendit_invoice_id', '==', xenditInvoiceId)
      .get();
    const orderDoc = orderSnapshot.docs[0];
    if (!orderDoc?.id) {
      throw new ValidationError(400, 'Order not found, please contact support');
    }
    const orderDetails = orderDoc.data() as IOrderDetails;
    try {
      const response = await markOrderAsPaidAndActivateCampaign({
        razorpayOrderId: orderDetails.razorpay_order_id,
        user: undefined,
        updatedOrderDetails: {
          xendit_invoice_id: xenditInvoiceId,
        },
      });
      return response;
    } catch (error) {
      logger.error(error);
      return { order: {} };
    }
  } else {
    logger.error(`Payment Status: ${xenditInvoice.status}`);
    return null;
  }
};
