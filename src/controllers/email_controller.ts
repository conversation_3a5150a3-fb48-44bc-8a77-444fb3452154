import { type SES } from 'aws-sdk';
import logger from '../modules/logger';
import { sendSimpleEmail } from '../modules/mailer';
import {
  COMMS_PLATFORM,
  sendSimpleEmail as sendSimpleEmailV2,
} from '../modules/aws/ses';
import {
  GrowEasyPartners,
  type IUserProfile,
  type IAuthUser,
  type IAdCreditsTransaction,
  type IVideoOrderDetails,
  type IMasterClassOrderDetails,
  AdPlatforms,
} from '../types';
import {
  type ITargeting,
  type ICampaign,
  type IBusinessDetails,
  type ICampaignInsightDetails,
  type IFlexibleSpecItem,
  type IFlexibleTargetingItem,
  type IGoogleKeywordIdeas,
  Currency,
  type ITiktokCampaignInsights,
} from '../types/campaign_details';
import ValidationError from '../utils/validation_error';
import { auth } from '../modules/firebase_admin_sdk';
import { type ICtwaLead, type ILeadsNotificationPayload } from '../types/leads';
import { type IOrderDetails } from '../types/payments_invoices';
import { type UserRecord } from 'firebase-admin/auth';
import {
  getCampaignLifetimeBudget,
  getParsedCampaignInsights,
  getParsedCampaignInsightsForTiktok,
} from './util';
import { formatCurrencyAmount, getCurrencySymbol } from '../utils';

const getGenderString = (targeting: ITargeting): string => {
  if (targeting.genders && targeting.genders.length === 1) {
    return targeting.genders.includes(1) ? 'Male' : 'Female';
  }
  return 'All';
};

const getLocationsString = (campaign: ICampaign): string => {
  const locations: string[] = [];
  if (campaign.google_ads_data?.geo_locations?.length) {
    campaign.google_ads_data.geo_locations.forEach((item) => {
      locations.push(item.geoTargetConstant?.canonicalName);
    });
  } else if (campaign.tiktok_ads_data?.geo_locations?.length) {
    campaign.tiktok_ads_data.geo_locations.forEach((item) => {
      locations.push(item.name);
    });
  } else if (campaign?.details?.targeting) {
    const targeting = campaign?.details?.targeting;

    targeting.geo_locations?.countries?.forEach((item) => locations.push(item));
    targeting.geo_locations?.regions?.forEach((item) =>
      locations.push(item.name ?? ''),
    );
    targeting.geo_locations?.cities?.forEach((item) =>
      locations.push(item.name ?? ''),
    );
    targeting.geo_locations?.neighborhoods?.forEach((item) =>
      locations.push(item.name ?? ''),
    );
    targeting.geo_locations?.places?.forEach((item) =>
      locations.push(item.name ?? ''),
    );
  }
  return locations.join(', ');
};

const getCampaignDetailsHtml = (campaign: ICampaign): string => {
  const businessDetails = campaign?.details?.business_details;
  const targeting = campaign?.details?.targeting;
  const budgetDetails = campaign.details?.budget_and_scheduling;
  const businessCategory = businessDetails?.business_category;
  const currency =
    campaign?.details?.budget_and_scheduling?.currency ?? Currency.INR;
  const currencyIcon = getCurrencySymbol(currency);
  const lifetimeBudget = getCampaignLifetimeBudget(budgetDetails);

  if (!targeting || !budgetDetails?.end_time) {
    return 'Error: Something went wrong.';
  }
  return `
    <div>
      <p><b>Business Category:</b> ${businessCategory}</p>
      <p><b>Product / Service Description:</b> ${businessDetails?.product_or_service_description}</p>
      <p><b>Product / Service Offers or USP:</b> ${businessDetails?.product_or_service_offers_or_usp}</p>
      <p><b>Targeting Age:</b> [${targeting?.age_min}, ${targeting?.age_max}]</p>
      <p><b>Targeting Gender:</b> ${getGenderString(targeting)} </p>
      <p><b>Locations:</b>${getLocationsString(campaign)}</p>
      <p><b>Lifetime budget</b>: ${currencyIcon}${
        lifetimeBudget /
        ([Currency.IDR, Currency.VND].includes(currency) ? 1 : 100)
      }</p>
      <p><b>End Date:</b> ${new Date(budgetDetails.end_time).toDateString()}</p>
      <p><b>Business WhatsApp No:</b> <a href="https://wa.me/${businessDetails?.mobile}">${businessDetails?.mobile}</a></p>
      <p><b>Platform:</b> ${campaign.platform}</p>
    </div>
  `;
};

const getToEmailForSendingComms = (user: UserRecord): string => {
  let toEmail = user.email ?? user.customClaims?.profile?.email ?? '';
  if (user.customClaims?.profile?.partner === GrowEasyPartners.NIVIDA) {
    toEmail = '<EMAIL>';
  } else if (user.customClaims?.profile?.partner === GrowEasyPartners.ZENDOT) {
    toEmail = '<EMAIL>';
  } else if (
    user.customClaims?.profile?.partner === GrowEasyPartners.GENIUS_ADS
  ) {
    toEmail = '<EMAIL>';
  }
  return toEmail;
};

const getEmailFooter = (partner?: GrowEasyPartners | null): string => {
  if (partner === GrowEasyPartners.AD_GLOBAL_AI) {
    return `
      <br>
      <p>Regards,</p>
      <p>Team AdGlobalAI</p>
    `;
  }
  return `
    <br>
    <p>Regards,</p>
    <p>Team GrowEasy</p>
  `;
};

const sendEmail = async (
  params: {
    to: string[];
    htmlBody: string;
    subject: string;
  },
  partner?: GrowEasyPartners | null,
): Promise<{ MessageId?: string }> => {
  if (partner && [GrowEasyPartners.AD_GLOBAL_AI].includes(partner)) {
    return await sendSimpleEmailV2(params, COMMS_PLATFORM.AD_GLOBAL_AI);
  }
  return await sendSimpleEmail(params);
};

const getWelcomeEmailHtmlContent = (
  authUser: IAuthUser,
  partner: GrowEasyPartners,
): string => {
  if (partner === GrowEasyPartners.AD_GLOBAL_AI) {
    return `<div>
      <p>Hi ${authUser.name},</p>
      <p>
        Welcome to AdGlobalAI - AI powered Lead generation app! 
        We're delighted to have you join us on this journey, where turning leads into opportunities is just a few clicks away.
      </p>
      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      <br>
      <p>Regards,</p>
      <p>Team AdGlobalAI</p>
    </div>`;
  }
  return `<div>
    <p>Hi ${authUser.name},</p>
    <p>
      Welcome to GrowEasy - AI powered Lead generation app! 
      We're delighted to have you join us on this journey, where turning leads into opportunities is just a few clicks away.
    </p>

    <h3>About GrowEasy:</h3>
    <p>
      Explore how GrowEasy simplifies lead generation. 
      Learn more about us on our <a href="https://groweasy.ai/about-us">About Us</a> page.
    </p>
    <br>

    <h3>Watch Our Demo:</h3>
    <p>
      To guide you through the process, check out our <a href="https://www.youtube.com/watch?v=X7OBaR2dF98">demo video.</a> 
      It provides a quick overview of how to make the most of our platform.
    </p>

    <br>
    <p>If you have any questions or need assistance, feel free to reply to this email.</p>

    <br>
    <p>Regards,</p>
    <p>Team GrowEasy</p>
  </div>`;
};

// campaign launched in pause mode, payment is pending here
export const sendCampaignLaunchedEmail = async (
  campaign: ICampaign,
  authUser: IAuthUser,
): Promise<void> => {
  const businessDetails = campaign?.details?.business_details;
  const targeting = campaign?.details?.targeting;
  const budgetDetails = campaign.details?.budget_and_scheduling;
  const user = await auth.getUser(authUser.uid);
  const partner = user.customClaims?.profile?.partner;
  const selfAdAccountFlow =
    !!campaign.details?.config?.self_ad_account_configs?.ad_account_id;

  if (!businessDetails || !targeting || !budgetDetails?.end_time) {
    logger.error('sendCampaignLaunchedEmail failed due to missing data');
    return;
  }
  const businessCategory = businessDetails.business_category;
  const toEmail = getToEmailForSendingComms(user);
  const emailBodyIntro = selfAdAccountFlow
    ? `
    <p>Your campaign is now live and running on your ad account. 🚀</p>
  `
    : `
    <p>
      Your campaign setup is complete! 🚀
      Please complete your payment to launch it and start getting leads.
    </p>
  `;

  const params = {
    to: [toEmail],
    subject: selfAdAccountFlow
      ? `Your "${businessCategory}" campaign is live.`
      : `Payment Pending for "${businessCategory}" campaign.`,
    htmlBody: `<div>
      <p>Hi ${authUser.name},</p>
      ${emailBodyIntro}
      <h3>Campaign Details-</h3>

      ${getCampaignDetailsHtml(campaign)}

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      ${getEmailFooter(partner)}
    </div>`,
  };
  try {
    const response = await sendEmail(params, partner);
    logger.info(
      `Campaign launched email sent to ${toEmail} with message id: ${response.MessageId}`,
    );
  } catch (error) {
    logger.error(error);
  }
};

export const sendWelcomeEmail = async (
  authUser?: IAuthUser,
): Promise<object> => {
  if (!authUser?.email) {
    throw new ValidationError(400, 'Missing auth user email');
  }
  // check if this user has just signed-up
  const user = await auth.getUser(authUser.uid);
  const partner = user.customClaims?.profile?.partner;
  const createdAt = user.metadata.creationTime;
  const createdAtTime = new Date(createdAt).getTime();
  const currentTime = Date.now();
  const difference = currentTime - createdAtTime;
  // if difference is greater than 60 secs then not a new user
  if (difference > 60 * 1000) {
    return {
      MessageId: '',
    };
  }

  const toEmail = getToEmailForSendingComms(user);
  const params = {
    to: [toEmail],
    subject: `Welcome to ${
      partner === GrowEasyPartners.AD_GLOBAL_AI ? 'AdGlobalAI' : 'GrowEasy'
    } - Launch Your Lead Generation Campaign in Minutes! 🚀`,
    htmlBody: getWelcomeEmailHtmlContent(authUser, partner),
  };
  const response = await sendEmail(params, partner);
  logger.info(
    `Welcome email sent to ${toEmail} with message id: ${response.MessageId}`,
  );
  return response;
};

const getLeadDetailsHtml = (payload: ILeadsNotificationPayload): string => {
  let leadDetailsHtml = '';
  payload.parsed_leads.forEach((lead, index) => {
    const name =
      lead.field_data?.find((fieldItem) => fieldItem.type === 'FULL_NAME')
        ?.values?.[0] ?? '';
    const email =
      lead.field_data?.find((fieldItem) => fieldItem.type === 'EMAIL')
        ?.values?.[0] ?? '';
    const phone =
      lead.field_data?.find((fieldItem) => fieldItem.type === 'PHONE')
        ?.values?.[0] ?? '';
    let moreDetailsHtml = '';
    lead.field_data
      ?.filter((fieldItem) => fieldItem.type === 'CUSTOM')
      .forEach((item, index) => {
        const value = item.values?.[0];
        moreDetailsHtml += `
        <div style="margin-top: 12px;">
          <p>
            ${index + 1}. ${item.label}
          </p>
          <p style="color: #646464;"><b>${value}</b></p>
        </div>
      `;
      });

    leadDetailsHtml += `
      <p>Campaign Name: <b>${lead.campaign_name}</b></p>
      <p style="font-size: 10px;">${
        lead.business_product_or_service_description ?? ''
      }</p>
      <p style="font-size: 16px;">👤 ${name}</p>
      <p style="font-size: 16px;">${
        email ? `✉️ ${email} | ` : ''
      } 📞 <a href="tel:${phone}">${phone}</a></p>
      ${moreDetailsHtml}
      <br>
    `;
  });
  return leadDetailsHtml;
};

export const sendNewLeadsEmail = async (
  payload: ILeadsNotificationPayload,
): Promise<object> => {
  const user = await auth.getUser(payload.uid);
  const partner = user.customClaims?.profile?.partner;
  const toEmail = getToEmailForSendingComms(user);
  const leadDetailsHtml = getLeadDetailsHtml(payload);
  const params = {
    to: [toEmail],
    subject:
      payload.platform === AdPlatforms.GOOGLE
        ? `Hurray, You've Got New Leads!`
        : `Hurray, You Got New Leads!`,
    htmlBody: `<div>
      <p>Hi ${user.displayName},</p>
      <p>
        Cheers! You have received new Leads.
      </p>

      <h2>Details-</h2>
      ${leadDetailsHtml}

      <p>
        Act on your Leads now: ${
          partner === GrowEasyPartners.AD_GLOBAL_AI
            ? '<a href="https://adglobalai.com/">Visit AdGlobalAI</a>'
            : '<a href="https://groweasy.ai">Visit GrowEasy</a>'
        }
      </p>

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      ${getEmailFooter(partner)}
    </div>`,
  };
  const response = await sendEmail(params, partner);
  logger.info(
    `New Leads email sent to ${toEmail} with message id: ${response.MessageId}`,
  );
  return response;
};

// campaign launched, payment received
export const sendPaymentSuccessEmail = async (payload: {
  campaign: ICampaign;
  user?: IAuthUser;
  orderDetails: Partial<IOrderDetails>;
}): Promise<void> => {
  const { campaign, user: authUser, orderDetails } = payload;
  const partner = campaign.details?.config?.partner;
  const businessDetails = campaign?.details?.business_details;
  const targeting = campaign?.details?.targeting;
  const budgetDetails = campaign.details?.budget_and_scheduling;
  if (
    !authUser?.email ||
    !businessDetails ||
    !targeting ||
    !budgetDetails?.end_time ||
    !orderDetails.amount
  ) {
    logger.error('sendPaymentSuccessEmail failed due to missing data');
    return;
  }
  const currency =
    campaign?.details?.budget_and_scheduling?.currency ?? Currency.INR;
  const currencyIcon = getCurrencySymbol(currency);
  const businessCategory = businessDetails.business_category;
  // cents to USD, paise to Rupees
  const orderAmount = [Currency.INR, Currency.USD].includes(currency)
    ? orderDetails.amount / 100
    : orderDetails.amount;
  const params = {
    to: [authUser.email],
    subject: `💰 Payment Success: Campaign Launched!`,
    htmlBody: `<div>
      <p>Hi ${authUser.name},</p>
      <p>
        Thank you for completing your payment of ${currencyIcon}${orderAmount}. Your "${businessCategory}" campaign is now live. ⚡
      </p>

      <h3>Campaign Details-</h3>

      ${getCampaignDetailsHtml(campaign)}

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      ${getEmailFooter(partner)}
    </div>`,
  };
  try {
    const response = await sendEmail(params, partner);
    logger.info(
      `Payment success email sent to ${authUser.email} with message id: ${response.MessageId}`,
    );
  } catch (error) {
    logger.error(error);
  }
};

export const sendAlertEmailOnPartnersLeadsWebhookError = async (data: {
  email: string;
  payload: ILeadsNotificationPayload;
  error: string;
}): Promise<SES.SendTemplatedEmailResponse> => {
  const { email: toEmail, payload, error } = data;
  const leadDetailsHtml = getLeadDetailsHtml(payload);
  const params = {
    to: [toEmail],
    subject: `Alert: Failed to sync Lead via Webhook`,
    htmlBody: `<div>
      <p>Dear Partner,</p>
      <p>
        Please check your webhook endpoint health status. We couldn't sync lead to your CRM.
      </p>

      <h2>Error-</h2>
      <p><code>${error}</code></p>

      <h2>Lead Details-</h2>
      ${leadDetailsHtml}

      <br>
      <p>Regards,</p>
      <p>Team GrowEasy</p>
    </div>`,
  };
  const response = await sendSimpleEmail(params);
  logger.info(
    `Lead sync failed alert sent to ${toEmail} with message id: ${response.MessageId}`,
  );
  return response;
};

const getSignupsDetailsHtml = (
  payload: Array<{
    profile: IUserProfile;
    business_details?: IBusinessDetails;
  }>,
): string => {
  if (!payload || payload.length === 0) {
    return '<p>No signups found.</p>';
  }

  // Style generator functions
  const headerCell = (): string =>
    'width: 30%; padding: 10px 12px; border: 1px solid #dddddd; background-color: #f2f2f2; font-weight: 600; color: #4a5568; text-align: left;';
  const dataCell = (): string =>
    'width: 70%; padding: 10px 12px; border: 1px solid #dddddd; text-align: left;';
  const linkStyle = 'color: #3182ce; text-decoration: none;';

  const renderRow = (
    label: string,
    value: string | undefined,
    isLink = false,
    linkType?: 'email' | 'tel' | 'wa',
  ): string => {
    const displayValue = value ?? '-';
    let content = displayValue;

    if (isLink && value) {
      if (linkType === 'email') {
        content = `<a href="mailto:${value}" style="${linkStyle}">${displayValue}</a>`;
      } else if (linkType === 'tel') {
        content = `<a href="tel:${value}" style="${linkStyle}">${displayValue}</a>`;
      } else if (linkType === 'wa') {
        content = `<a href="https://wa.me/${value}" target="_blank" style="${linkStyle}">${displayValue}</a>`;
      }
    }

    return `
      <tr>
        <td style="${headerCell()}">${label}</td>
        <td style="${dataCell()}">${content}</td>
      </tr>
    `;
  };

  let html = `
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; color: #333;">
      <h1 style="font-size: 24px; margin-bottom: 16px; color: #2d3748;">Hourly Signups Report</h1>
      <p>Total signups: ${payload.length}</p>
  `;

  payload.forEach((data, index) => {
    const { profile, business_details: businessDetails } = data;
    const mobile = `${profile?.mobile_dial_code ?? ''}${profile?.mobile ?? ''}`;

    html += `
      <div style="background-color: #f8fafc; border-radius: 8px; padding: 16px; margin-bottom: 24px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
        <h3 style="font-size: 18px; font-weight: bold; margin: 16px 0 8px; color: #1a365d;">Signup #${
          index + 1
        }</h3>
        
        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse: collapse; margin-bottom: 16px;">
          ${renderRow('Name', profile?.name)}
          ${renderRow('Business Name', profile?.business_name)}
          ${renderRow('Employee Count', profile?.number_of_employees)}
          ${renderRow(
            'Marketing Budget',
            `${profile?.monthly_marketing_budget_currency} ${profile?.monthly_marketing_budget}`,
          )}
          ${renderRow('Team', profile?.has_calling_team)}
          ${renderRow('Email', profile?.email, true, 'email')}
          ${renderRow('Mobile', mobile, true, 'tel')}
          ${renderRow('WhatsApp', mobile, true, 'wa')}
          ${renderRow(
            'WhatsApp Opt-in',
            profile?.whatsapp_opt_in ? 'Yes' : 'No',
          )}
          ${renderRow(
            'Affiliate Marketing',
            profile?.is_affiliate_marketing ? 'Yes' : 'No',
          )}
          ${renderRow('Acquisition Source', profile?.acquisition_source)}
          ${renderRow('Partner', profile?.partner)}
        </table>
    `;

    if (businessDetails) {
      html += `
        <h4 style="font-size: 18px; font-weight: bold; margin: 16px 0 8px; color: #1a365d;">Business Details</h4>
        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
          ${renderRow('Category', businessDetails.business_category)}
          ${renderRow(
            'Product Description',
            businessDetails.product_or_service_description,
          )}
          ${renderRow(
            'Product USP',
            businessDetails?.product_or_service_offers_or_usp,
          )}
          ${
            businessDetails?.mobile
              ? renderRow(
                  'Business Mobile',
                  businessDetails.mobile,
                  true,
                  'tel',
                )
              : ''
          }
        </table>
      `;
    }

    html += `</div>`;
  });

  html += `</div>`;
  return html;
};

export const sendHourlySignupsEmailToSupport = async (
  payload: Array<{
    profile: IUserProfile;
    business_details?: IBusinessDetails;
  }>,
): Promise<SES.SendTemplatedEmailResponse> => {
  const toEmail = '<EMAIL>';
  const params = {
    to: [toEmail],
    subject: `Hourly Signups`,
    htmlBody: `<div>
      <h2 style="color:#47494C;">Last hour Signups-</h2>
      ${getSignupsDetailsHtml(payload)}

      <br>
      <p>Regards,</p>
      <p>Team GrowEasy</p>
    </div>`,
  };
  const response = await sendSimpleEmail(params);
  logger.info(
    `Hourly Signups email sent to ${toEmail} with message id: ${response.MessageId}`,
  );
  return response;
};

export const sendNewCtwaLeadsEmail = async (
  lead: ICtwaLead,
): Promise<object> => {
  const user = await auth.getUser(lead.client_uid);
  const partner = user.customClaims?.profile?.partner;
  const toEmail = getToEmailForSendingComms(user);
  const leadPhone = lead.field_data?.find((item) => item.name === 'question3')
    ?.values[0];
  const params = {
    to: [toEmail],
    subject: `Hurray, You got new customer inquiry!`,
    htmlBody: `<div>
      <p>Hi ${user.displayName},</p>
      <p>
        Cheers! You've received a new customer inquiry!.
      </p>

      <h2>Details-</h2>
      <p>Campaign Name: <b>${lead.business_details?.business_category}</b></p>
      <p style="font-size: 10px;">${
        lead.business_details?.product_or_service_description ?? ''
      }</p>
      <p style="font-size: 16px;">👤 ${lead.field_data?.find(
        (item) => item.name === 'question1',
      )?.values[0]}</p>
      <p style="font-size: 16px;">📞 <a href="tel:${leadPhone}">${leadPhone}</a></p>
      <p style="font-size: 16px; display: flex; align-items: center;">
        <img src="https://groweasy.ai/images/common/whatsapp-icon-green.png" width="20" height="20" style="margin-right:8px;" /> 
        <a href="https://wa.me/${leadPhone}">${leadPhone}</a>
      </p>

      <p>
        Check conversation details here: <a href="https://groweasy.ai/ctwa-leads/${
          lead.id
        }">https://groweasy.ai/ctwa-leads/${lead.id}</a>
      </p>

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      ${getEmailFooter(partner)}
    </div>`,
  };
  const response = await sendEmail(params, partner);
  logger.info(
    `New Leads email sent to ${toEmail} with message id: ${response.MessageId}`,
  );
  return response;
};

const getCampaignInsightsHtml = (
  campaignDetails: ICampaign,
  insights: ICampaignInsightDetails | ITiktokCampaignInsights,
): string => {
  const data =
    'metrics' in insights
      ? getParsedCampaignInsightsForTiktok({
          budgetAndScheduling: campaignDetails?.details?.budget_and_scheduling,
          insights,
        })
      : getParsedCampaignInsights({
          budgetAndScheduling: campaignDetails?.details?.budget_and_scheduling,
          insights,
          campaignType: campaignDetails?.type,
        });
  let tableHtml = '<table border="1" cellpadding="5" cellspacing="0"><tbody>';

  // Loop through the array in chunks of 3
  for (let i = 0; i < data.length; i += 3) {
    tableHtml += '<tr>';

    // Process each item in the current chunk
    for (let j = i; j < i + 3; j++) {
      if (data[j]) {
        tableHtml += `<td>${data[j].label}: <b>${data[j].value}</b></td>`;
      } else {
        tableHtml += '<td></td>'; // Fill empty cells if data length is not a multiple of 3
      }
    }

    tableHtml += '</tr>';
  }

  tableHtml += '</tbody></table>';
  return tableHtml;
};

export const sendCampaignFinishedEmail = async (payload: {
  campaign: ICampaign;
  authUser: IAuthUser;
  insights: ICampaignInsightDetails | ITiktokCampaignInsights;
}): Promise<void> => {
  const { campaign, authUser, insights } = payload;
  const partner = authUser?.partner;
  const businessDetails = campaign?.details?.business_details;
  const targeting = campaign?.details?.targeting;
  const budgetDetails = campaign.details?.budget_and_scheduling;
  const user = await auth.getUser(authUser.uid);
  if (!businessDetails || !targeting || !budgetDetails?.end_time) {
    logger.error('sendCampaignFinishedEmail failed due to missing data');
    return;
  }
  const businessCategory = businessDetails.business_category;
  const toEmail = getToEmailForSendingComms(user);
  const params = {
    to: [toEmail],
    subject: `Your "${businessCategory}" Campaign is Finished Running - Check Out Your Performance!`,
    htmlBody: `<div>
      <p>Hi ${authUser.name},</p>
      <p>
        Below is the report of your recent ad campaign with GrowEasy!
      </p>

      <h3>Campaign Details-</h3>

      ${getCampaignDetailsHtml(campaign)}

      <h3>Campaign Performance-</h3>

      ${getCampaignInsightsHtml(campaign, insights)}

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      ${getEmailFooter(partner)}
    </div>`,
  };
  try {
    const response = await sendEmail(params, partner);
    logger.info(
      `Campaign finished email sent to ${toEmail} with message id: ${response.MessageId}`,
    );
  } catch (error) {
    logger.error(error);
  }
};

export const sendLaunchEasyQueryReceivedEmail = async (
  payload: object,
): Promise<void> => {
  const params = {
    to: ['<EMAIL>', '<EMAIL>'],
    subject: `LaunchEasy | New Query Received`,
    htmlBody: JSON.stringify(payload),
  };
  try {
    const response = await sendSimpleEmail(params);
    logger.info(
      `sendLaunchEasyQueryReceivedEmail with message id: ${response.MessageId}`,
    );
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const sendCampaignStoppedEmail = async (payload: {
  campaign: ICampaign;
  authUser: IAuthUser;
  transaction?: IAdCreditsTransaction;
}): Promise<void> => {
  const { campaign, authUser, transaction } = payload;
  const businessDetails = campaign?.details?.business_details;
  const targeting = campaign?.details?.targeting;
  const budgetDetails = campaign.details?.budget_and_scheduling;
  const user = await auth.getUser(authUser.uid);
  const partner = user.customClaims?.profile?.partner;
  if (!businessDetails || !targeting || !budgetDetails?.end_time) {
    logger.error('sendCampaignStoppedEmail failed due to missing data');
    return;
  }
  const businessCategory = businessDetails.business_category;
  const toEmail = getToEmailForSendingComms(user);
  const params = {
    to: [toEmail],
    subject: transaction
      ? `Your "${businessCategory}" Campaign is Stopped - Balance transferred to Wallet!`
      : `Your "${businessCategory}" Campaign is Stopped.`,
    htmlBody: transaction
      ? `<div>
      <p>Hi ${authUser.name},</p>
      <p>
        We have stopped your campaign as per your request, and the remaining balance has been credited to your wallet.
      </p>

      <h3>Campaign Details-</h3>

      ${getCampaignDetailsHtml(campaign)}

      <p style="color: ${
        transaction.type === 'DEBIT' ? '#D41F1F' : '#2ECC71'
      };">
        <b>Transaction Details:</b> ${transaction.type} ${formatCurrencyAmount(
          transaction.value,
          transaction.currency,
        )}
      </p>

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      ${getEmailFooter(partner)}
    </div>`
      : `<div>
      <p>Hi ${authUser.name},</p>
      <p>
        We have stopped your campaign as per your request.
      </p>

      <h3>Campaign Details-</h3>

      ${getCampaignDetailsHtml(campaign)}

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>
    </div>`,
  };
  try {
    const response = await sendEmail(params, partner);
    logger.info(
      `Campaign stopped email sent to ${toEmail} with message id: ${response.MessageId}`,
    );
  } catch (error) {
    logger.error(error);
  }
};

export const sendVideosPaymentPendingEmail = async (
  payload: IVideoOrderDetails,
): Promise<void> => {
  const {
    user_details: userDetails,
    video_details: videoDetails,
    amount,
    currency,
  } = payload;

  const toEmail = userDetails.email;

  let videoDetailsHtml = '<ul>';
  videoDetails.forEach((video) => {
    videoDetailsHtml += `
      <li><code>${video.script}</code></li>
    `;
  });
  videoDetailsHtml += '</ul>';

  const params = {
    to: [toEmail],
    subject: `GrowEasy Videos | Payment Pending`,
    htmlBody: `<div>
      <p>Hi ${userDetails.name},</p>
      <p>
        Please complete the payment of ${currency} ${Math.ceil(
          amount / 100,
        )} towards your videos order with us.
      </p>

      <h3>Your Details-</h3>

      <p><b>Name:</b> ${userDetails.name}</p>
      <p><b>Email:</b> ${userDetails.email}</p>
      <p><b>Mobile No:</b> <a href="https://wa.me/${userDetails?.mobile}">${userDetails?.mobile}</a></p>

      <h3>Video Scripts-</h3>

      ${videoDetailsHtml}

      <p>You can reach out to us at <a href="https://wa.me/+919998269709">+919998269709</a> for assistance with your payment.</p>

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      <br>
      <p>Regards,</p>
      <p>Team GrowEasy</p>
    </div>`,
  };
  try {
    const response = await sendSimpleEmail(params);
    logger.info(
      `sendVideosPaymentPendingEmail ${toEmail} with message id: ${response.MessageId}`,
    );
  } catch (error) {
    logger.error(error);
  }
};

export const sendVideosPaymentReceivedEmail = async (
  payload: IVideoOrderDetails,
): Promise<void> => {
  const {
    user_details: userDetails,
    video_details: videoDetails,
    amount,
    currency,
  } = payload;

  const toEmail = userDetails.email;

  let videoDetailsHtml = '<ul>';
  videoDetails.forEach((video) => {
    videoDetailsHtml += `
      <li><code>${video.script}</code></li>
    `;
  });
  videoDetailsHtml += '</ul>';

  const params = {
    to: [toEmail],
    subject: `GrowEasy Videos | Payment Received`,
    htmlBody: `<div>
      <p>Hi ${userDetails.name},</p>
      <p>
        Thank you for placing your videos order with us. We have received payment of ${currency} ${Math.ceil(
          amount / 100,
        )}.
      </p>

      <h3>Your Details-</h3>

      <p><b>Name:</b> ${userDetails.name}</p>
      <p><b>Email:</b> ${userDetails.email}</p>
      <p><b>Mobile No:</b> <a href="https://wa.me/${userDetails?.mobile}">${userDetails?.mobile}</a></p>

      <h3>Video Scripts-</h3>

      ${videoDetailsHtml}

      <h3>Next Step-</h3>

      <p>Our team will reach out to you for more details.</p>
      <p>You can also reach out to us at <a href="https://wa.me/+919998269709">+919998269709</a></p>

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      <br>
      <p>Regards,</p>
      <p>Team GrowEasy</p>
    </div>`,
  };
  try {
    const response = await sendSimpleEmail(params);
    logger.info(
      `sendVideosPaymentReceivedEmail ${toEmail} with message id: ${response.MessageId}`,
    );
  } catch (error) {
    logger.error(error);
  }
};

export const sendMasterClassPaymentReceivedEmail = async (
  payload: IMasterClassOrderDetails,
): Promise<void> => {
  const {
    registration_details: registrationDetails,
    amount,
    currency,
  } = payload;

  const toEmail = registrationDetails.email;

  const params = {
    to: [toEmail],
    subject: `GrowEasy Digital Marketing Master Class | Payment Received`,
    htmlBody: `<div>
      <p>Hi ${registrationDetails.name},</p>
      <p>
        Thank you for registering for Digital Marketing & Growth Strategy. We have received payment of ${currency} ${Math.ceil(
          amount / 100,
        )}.
      </p>

      <h3>Registration Details-</h3>

      <p><b>Name:</b> ${registrationDetails.name}</p>
      <p><b>Email:</b> ${registrationDetails.email}</p>
      <p><b>Mobile No:</b> <a href="https://wa.me/${registrationDetails?.mobile}">${registrationDetails?.mobile}</a></p>
      <p><b>Profession:</b> ${registrationDetails.profession ?? ''}</p>

      <h3>Next Step-</h3>

      <p>Our team will reach out to you for more details.</p>
      <p>You can also reach out to us at <a href="https://wa.me/+919998269709">+919998269709</a></p>

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      <br>
      <p>Regards,</p>
      <p>Team GrowEasy</p>
    </div>`,
  };
  try {
    const response = await sendSimpleEmail(params);
    logger.info(
      `sendMasterClassPaymentReceivedEmail ${toEmail} with message id: ${response.MessageId}`,
    );
  } catch (error) {
    logger.error(error);
  }
};

export const sendMetaAudienceSuggestionEmail = async (payload: {
  email: string;
  metaAudience: Partial<IFlexibleSpecItem>;
  product_or_service_description: string;
  business_category: string;
}): Promise<SES.SendTemplatedEmailResponse> => {
  const { email, metaAudience } = payload;

  // Function to format audience data into HTML
  const formatAudienceHtml = (
    title: string,
    items: Array<{ id: string; name: string }>,
  ): string => {
    if (!items || items.length === 0) return '';
    return `
      <h4>${title}:</h4>
      <ul>
        ${items.map((item) => `<li>${item?.name} (${item?.id})</li>`).join('')}
      </ul>
    `;
  };

  // Build HTML body with dynamic audience data
  const audienceHtml = `
    ${formatAudienceHtml(
      'Behaviors',
      (metaAudience.behaviors as IFlexibleTargetingItem[]) || [],
    )}
    ${formatAudienceHtml(
      'Interests',
      (metaAudience.interests as IFlexibleTargetingItem[]) || [],
    )}
    ${formatAudienceHtml(
      'Life Events',
      (metaAudience.life_events as IFlexibleTargetingItem[]) || [],
    )}
    ${formatAudienceHtml(
      'Industries',
      (metaAudience.industries as IFlexibleTargetingItem[]) || [],
    )}
    ${formatAudienceHtml(
      'Work Positions',
      (metaAudience.work_positions as IFlexibleTargetingItem[]) || [],
    )}
  `;

  const params = {
    to: [email],
    subject: `Audience Suggestions for Your Business`,
    htmlBody: `<div>
      <p>Hi,</p>
      <p>
        Thank you for trying out GrowEasy Audience Builder! We’ve generated the following audience suggestions based on your inputs:
      </p>

      <p><b>Business category:</b>  ${payload.business_category}</p>
      <p><b>Your Offering:</b>  ${payload.product_or_service_description}</p>

      ${audienceHtml}
      <br>

      <h3>About GrowEasy:</h3>
      <p>
        Explore how GrowEasy simplifies lead generation. 
        Learn more about us on our <a href="https://groweasy.ai/">Home</a> page.
      </p>
      <br>

      <h3>Watch Our Demo:</h3>
      <p>
        To guide you through the process, check out our <a href="https://www.youtube.com/watch?v=X7OBaR2dF98">demo video.</a> 
        It provides a quick overview of how to make the most of our platform.
      </p>

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      <br>
      <p>Regards,</p>
      <p>Team GrowEasy</p>
    </div>`,
  };

  // Send email
  const response = await sendSimpleEmail(params);
  logger.info(
    `Audience suggestion email sent to ${email} with message id: ${response.MessageId}`,
  );
  return response;
};

export const sendGoogleKeywordIdeasEmail = async (payload: {
  email: string;
  googleKeywordIdeas: IGoogleKeywordIdeas[];
  product_or_service_description: string;
  business_category: string;
  country_code: string;
  website?: string;
}): Promise<SES.SendTemplatedEmailResponse> => {
  const { email, googleKeywordIdeas } = payload;

  const keywordsHtml = `
    <table style="border: 1px solid #ccc; border-collapse: collapse; width: 100%; margin-top: 20px;">
      <thead>
        <tr>
          <th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Keyword</th>
          <th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Competition</th>
          <th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Avg Monthly Searches</th>
        </tr>
      </thead>
      <tbody>
        ${googleKeywordIdeas
          .map((idea) => {
            const avgSearch = idea.keywordIdeaMetrics.avgMonthlySearches;
            return `
              <tr>
                <td style="border: 1px solid #ccc; padding: 8px;">${idea.text}</td>
                <td style="border: 1px solid #ccc; padding: 8px;">${idea.keywordIdeaMetrics.competition}</td>
                <td style="border: 1px solid #ccc; padding: 8px;">${avgSearch}</td>
              </tr>
            `;
          })
          .join('')}
      </tbody>
    </table>
  `;

  const params = {
    to: [email],
    subject: `High Volume Keyword Ideas for Your Business`,
    htmlBody: `<div>
      <p>Hi,</p>
      <p>
        Thank you for trying out GrowEasy Keyword Ideas Generator! We’ve generated the following keywords based on your inputs:
      </p>

      <p><b>Business category:</b>  ${payload.business_category}</p>
      <p><b>Your Offering:</b>  ${payload.product_or_service_description}</p>
      <p><b>Targeting Country:</b>  ${payload.country_code}</p>
      <p><b>Your Website:</b>  ${payload.website}</p>

      ${keywordsHtml}
      <br>

      <h3>About GrowEasy:</h3>
      <p>
        Explore how GrowEasy simplifies lead generation. 
        Learn more about us on our <a href="https://groweasy.ai/">Home</a> page.
      </p>
      <br>

      <h3>Watch Our Demo:</h3>
      <p>
        To guide you through the process, check out our <a href="https://www.youtube.com/watch?v=X7OBaR2dF98">demo video.</a> 
        It provides a quick overview of how to make the most of our platform.
      </p>

      <br>
      <p>If you have any questions or need assistance, feel free to reply to this email.</p>

      <br>
      <p>Regards,</p>
      <p>Team GrowEasy</p>
    </div>`,
  };

  // Send email
  const response = await sendSimpleEmail(params);
  logger.info(
    `Keyword ideas email sent to ${email} with message id: ${response.MessageId}`,
  );
  return response;
};

export const sendContactUsEmail = async (
  params: { subject: string; message: string },
  authUser: IAuthUser,
): Promise<object> => {
  const user = await auth.getUser(authUser.uid);
  const userName = authUser.name;
  const toEmail = getToEmailForSendingComms(user);

  const emailHtmlBody = `
    <div style="font-family: sans-serif; line-height: 1.5;">
      <p>Hi ${userName},</p>
      <p>Thanks for reaching out! We've received your message and will get back to you as soon as possible.</p>

      <h3>Your Query:</h3>
      <p><strong>Subject:</strong> ${params.subject}</p>
      <p style="white-space: pre-wrap;"><strong>Message:</strong><br/>${
        params.message
      }</p>

      ${getEmailFooter()}
    </div>
  `;

  const emailParams = {
    to: [toEmail],
    subject: `We've received your query`,
    htmlBody: emailHtmlBody,
  };

  const response = await sendEmail(emailParams);
  logger.info(
    `Contact us email sent to ${toEmail} with message id: ${response.MessageId}`,
  );
  return response;
};
