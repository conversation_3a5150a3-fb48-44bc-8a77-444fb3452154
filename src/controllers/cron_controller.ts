import { Timestamp } from 'firebase-admin/firestore';
import {
  ADGLOBAL_AI_BE_MASTER_GOOGLE_SHEET_ID,
  FIRESTORE_COLLECTIONS,
} from '../constants';
import { auth, db } from '../modules/firebase_admin_sdk';
import { appendDataToSheet } from '../modules/google_sheet';
import logger from '../modules/logger';
import {
  AdPlatforms,
  GrowEasyPartners,
  type IAuthUser,
  type IUserProfile,
} from '../types';
import {
  type ICampaign,
  type IBusinessDetails,
  GrowEasyCampaignStatus,
  type ICampaignInsightDetails,
  type ITiktokCampaignInsights,
} from '../types/campaign_details';
import {
  sendCampaignFinishedEmail,
  sendHourlySignupsEmailToSupport,
} from './email_controller';
import {
  getGoogleCampaignInsightDetails,
  getMetaLeadsCountFromInsightDetails,
} from './util';
import { getCampaignInsights } from './meta_controller';
import { getGoogleAdsDataFromSubCollection } from './db_controller';
import { getCampaignReportingFromGoogle } from './google/google_controller';
import axios, { type AxiosError } from 'axios';
import { sendCampaignNotLaunchedCheckinComm } from './comms_controller';
import { getTiktokCampaignInsights } from './tiktok/tiktok_controller';

/* const sendOnboardingFunnelDropTemplateToUser = async (
  profile: IUserProfile,
  waTemplateName: string,
): Promise<void> => {
  if (profile.mobile_dial_code && profile.mobile) {
    const userMobile = `${profile.mobile_dial_code}${profile.mobile}`;
    // not using '+' since doc id of collection wa_919315639185 does not have +
    const userWhatsappId = userMobile.replace('+', '');
    logger.info(
      'sendOnboardingFunnelDropComms',
      profile.uid,
      profile.name,
      userWhatsappId,
    );
    const replyPayload: ICtwaChatMessage = {
      messaging_product: 'whatsapp',
      to: userWhatsappId,
      type: WaMessageType.template,
      template: {
        name: waTemplateName,
        language: {
          code: 'en',
        },
      },
    };
    await sendWhatsAppComm(replyPayload, profile);
  }
}; */
/**
 * Current time -> 6:46 PM 17 June
 * Day 1 slab -> 5:46 PM - 6:48 PM on 16 June (including 2 mins buffer for processing/cron delay)
 * Day 2 slab -> 5:46 PM - 6:48 PM on 15 June (including 2 mins buffer for processing/cron delay)
 * ... so on
 *
 * Current time -> 7:46 PM 17 June
 * Day 1 slab -> 6:46 PM - 7:48 PM on 16 June (including 2 mins buffer for processing/cron delay)
 * Day 2 slab -> 6:46 PM - 7:48 PM on 15 June (including 2 mins buffer for processing/cron delay)
 * ... so on
 */
/* const sendOnboardingFunnelDropComms = async (): Promise<void> => {
  const currentDate = new Date();
  const days = [3, 7]; // [1, 2, 3, 4, 5, 6, 7];
  const BUFFER_MINUTES = 2; // add 2 mins to all end dates
  try {
    for (const day of days) {
      const waTemplateName = `onboarding_dropoff_comms_day${day}_v1`;
      const startDate = new Date(currentDate);
      const endDate = new Date(currentDate);
      startDate.setHours(currentDate.getHours() - 24 * day - 1);
      endDate.setHours(currentDate.getHours() - 24 * day);
      endDate.setMinutes(endDate.getMinutes() + BUFFER_MINUTES);
      logger.info(
        `Day ${day}`,
        startDate.toISOString(),
        endDate.toISOString(),
        waTemplateName,
      );
      // get all profiles created in these slots and having whatsapp optin enabled
      const userProfiles: IUserProfile[] = [];
      const snapshot = await db
        .collection(FIRESTORE_COLLECTIONS.USER_PROFILE)
        .where('whatsapp_opt_in', '==', true)
        .where('created_at', '>', startDate)
        .where('created_at', '<', endDate)
        .get();
      snapshot.forEach((doc) => {
        const data = doc.data() as IUserProfile;
        userProfiles.push(data);
      });
      for (const profile of userProfiles) {
        try {
          // this user should not have any campaign in ACTIVE state
          const campaignsSnapshot = await db
            .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
            .where('uid', '==', `${profile?.uid}`)
            .where('status', 'in', [
              GrowEasyCampaignStatus.ACTIVE,
              // GrowEasyCampaignStatus.PAUSED,
            ])
            .select('details.business_details')
            .limit(1)
            .get();
          const campaigns: ICampaign[] = [];
          campaignsSnapshot.forEach((doc) => {
            campaigns.push(doc.data() as ICampaign);
          });
          if (!campaigns.length) {
            void sendOnboardingFunnelDropTemplateToUser(
              profile,
              waTemplateName,
            );
          } else {
            logger.info(
              `${profile.name} has a campaign in ACTIVE state`,
              campaigns[0]?.details?.business_details?.business_category,
            );
          }
        } catch (error) {
          logger.error(error);
        }
      }
    }
  } catch (error) {
    logger.error(error);
  }
}; */

// Send comms to all users who have campaigns in draft state, created 24 hours ago
const sendOnboardingFunnelDropComms = async (): Promise<void> => {
  const currentDate = new Date();
  const BUFFER_MINUTES = 2; // add 2 mins to all end dates
  try {
    const startDate = new Date(currentDate);
    const endDate = new Date(currentDate);
    startDate.setHours(currentDate.getHours() - 24 - 1);
    endDate.setHours(currentDate.getHours() - 24);
    endDate.setMinutes(endDate.getMinutes() + BUFFER_MINUTES);
    // get all campaigns created in these slots
    const campaignsSnapshot = await db
      .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
      .where('created_at', '>', startDate)
      .where('created_at', '<', endDate)
      .select('uid', 'status')
      .get();
    const campaigns: ICampaign[] = [];
    campaignsSnapshot.forEach((doc) => {
      const data = doc.data() as ICampaign;
      // only campaigns in draft
      if (data.status === GrowEasyCampaignStatus.DRAFT) {
        campaigns.push(data);
      }
    });
    for (const campaign of campaigns) {
      // get user profile, check WhatsApp opt-in and send comm
      await sendCampaignNotLaunchedCheckinComm({
        uid: campaign.uid,
      });
    }
  } catch (error) {
    logger.error(error);
  }
};

export const checkAndSendFinishedCampaignsReport = async (): Promise<void> => {
  try {
    const finishedCampaignIds: string[] = [];
    const currentDate = new Date();
    const runningCampaignsSnapshot = await db
      .collection(FIRESTORE_COLLECTIONS.RUNNING_CAMPAIGNS)
      .where('campaign_end_date', '<', currentDate)
      .get();
    runningCampaignsSnapshot.forEach((doc) => {
      finishedCampaignIds.push(doc.data()?.id);
    });
    for (const campaignId of finishedCampaignIds) {
      // Get Insight, send Report
      const campaignSnapshot = await db
        .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
        .where('id', '==', campaignId)
        .get();
      const campaignDoc = campaignSnapshot.docs[0];

      const campaign = (campaignDoc?.data() as ICampaign) ?? null;

      // include google data too
      if (campaignDoc?.id) {
        campaign.google_ads_data = await getGoogleAdsDataFromSubCollection(
          campaignDoc.id,
        );
      }

      if (campaign) {
        logger.info('checkAndSendFinishedCampaignsReport', campaign?.name);
        // get insights
        let insights: ICampaignInsightDetails | ITiktokCampaignInsights | null =
          null;
        const adPlatform = campaign.platform ?? AdPlatforms.META;

        if (adPlatform === AdPlatforms.META) {
          // Meta insights
          const queryParams = {
            campaign_id: campaign.meta_id,
          };

          try {
            const campaignInsightsResponse =
              await getCampaignInsights(queryParams);
            insights = campaignInsightsResponse?.data
              ?.data?.[0] as ICampaignInsightDetails;
            if (insights) {
              // get leads count
              /* if (campaign.type === GROWEASY_CAMPAIGN_TYPE.CTWA) {
                const leadsCount = await db
                  .collection(FIRESTORE_COLLECTIONS.CTWA_LEADS)
                  .where('campaign_id', '==', campaignId)
                  .count()
                  .get();
                insights.leads = leadsCount.data().count;
              } else {
                const leadsCount = await db
                  .collection(FIRESTORE_COLLECTIONS.META_WEBHOOK_LEADS)
                  .where('form_id', '==', campaign.meta_leadgen_form_id)
                  .count()
                  .get();
                insights.leads = leadsCount.data().count;
              } */
              insights.leads = getMetaLeadsCountFromInsightDetails(
                insights,
                campaign.type,
              );
            }
          } catch (error) {
            logger.error(error);
          }
        } else if (adPlatform === AdPlatforms.GOOGLE) {
          // google reporting
          if (campaign.google_ads_data?.campaign_resource) {
            const insightsArr = await getCampaignReportingFromGoogle(
              campaign.google_ads_data?.campaign_resource,
            );
            insights = getGoogleCampaignInsightDetails({ insightsArr });
          }
        } else if (adPlatform === AdPlatforms.TIKTOK) {
          insights = await getTiktokCampaignInsights({
            campaign_id: campaign.tiktok_ads_data?.campaign_id ?? '',
          });
        }
        const userDetails = await auth.getUser(campaign.uid);
        const authUser: IAuthUser = {
          name: userDetails?.displayName ?? '',
          uid: userDetails.uid,
          email: userDetails.email,
          partner: userDetails.customClaims?.profile?.partner,
        };
        if (insights) {
          await sendCampaignFinishedEmail({
            campaign,
            insights,
            authUser,
          });
        }
        // delete entry
        await db
          .collection(FIRESTORE_COLLECTIONS.RUNNING_CAMPAIGNS)
          .doc(campaignId)
          .delete();
      }
    }
  } catch (error) {
    logger.error(error);
  }
};

const updateExchangeRate = async (): Promise<void> => {
  const docRef = db.collection(FIRESTORE_COLLECTIONS.EXCHANGE_RATE).doc('INR');
  const docSnap = await docRef.get();

  const now = Timestamp.now();

  // Check if last updated time is less than 12 hours ago
  if (docSnap.exists) {
    const data = docSnap.data();
    const lastUpdated: Timestamp = data?.updated_at;
    const twelveHoursInMillis = 12 * 60 * 60 * 1000;

    if (
      lastUpdated &&
      now.toMillis() - lastUpdated.toMillis() < twelveHoursInMillis
    ) {
      logger.info('Exchange rate update skipped (within 12 hours)');
      return;
    }
  }

  // varunon99 account
  const apiKey = 'fca_live_vo1qN6AlcnZiPknERcLY1JypDvrNTpf4nQFg7FiV';
  const url = `https://api.freecurrencyapi.com/v1/latest?apikey=${apiKey}&base_currency=INR&currencies=USD,IDR,PHP,THB,MYR`;

  try {
    const response = await axios.get(url);
    const rates = response?.data?.data;

    await docRef.set(
      {
        ...rates,
        INR: 1,
        VND: 305, // fallback since not supported
        updated_at: now,
      },
      { merge: true },
    );
    logger.info('Exchange rate updated');
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
  }
};

export const sendHourlySignupsToAdmins = async (): Promise<{
  profiles_count: number;
}> => {
  try {
    const data: Array<{
      profile: IUserProfile;
      business_details?: IBusinessDetails;
    }> = [];
    const currentDate = new Date();
    const lastHourDate = new Date(currentDate.getTime() - 62 * 60 * 1000); // 62 mins ago, taking buffer of 2 mins
    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.USER_PROFILE)
      .where('created_at', '>', lastHourDate)
      .orderBy('created_at', 'desc')
      .limit(50)
      .get();
    const profiles: IUserProfile[] = [];
    snapshot.forEach((doc) => {
      profiles.push(doc.data() as IUserProfile);
    });
    // for every user, find its last campaign
    for (let i = 0; i < profiles.length; i++) {
      const profile = profiles[i];
      const lastCampaignsSnapshot = await db
        .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
        .where('uid', '==', `${profile?.uid}`)
        .orderBy('created_at', 'desc')
        .limit(1)
        .get();
      const campaigns: ICampaign[] = [];
      lastCampaignsSnapshot.forEach((doc) => {
        campaigns.push(doc.data() as ICampaign);
      });
      data.push({
        profile,
        business_details: campaigns[0]?.details?.business_details,
      });
    }
    if (data.length) {
      void sendHourlySignupsEmailToSupport(data);
      const getSignupsRowData = (item: {
        profile: IUserProfile;
        business_details?: IBusinessDetails;
      }): Array<string | number> => {
        const { profile } = item;

        // keep employee count & marketing budget in number for filters to work
        const employeeMap: Record<string, number> = {
          only_me: 1,
          less_than_5: 3,
          more_than_5: 10,
        };

        let employeeCount: string | number = '';
        if (profile?.number_of_employees) {
          employeeCount =
            employeeMap[profile.number_of_employees] ??
            profile.number_of_employees; // keep free-text
        }

        // --- Marketing Budget ---
        const budgetMap: Record<string, number> = {
          less_than_5k: 2000,
          '5k_to_30k': 15000,
          more_than_30k: 50000,
        };

        let marketingBudget: string | number = '';
        if (profile?.monthly_marketing_budget) {
          marketingBudget =
            budgetMap[profile.monthly_marketing_budget] ??
            profile.monthly_marketing_budget; // keep free-text
        }

        // --- Currency Conversion (only if numeric) ---
        if (
          profile?.monthly_marketing_budget_currency &&
          typeof marketingBudget === 'number'
        ) {
          // approx conversion rates
          const rates: Record<string, number> = {
            USD: 90,
            IDR: 0.005,
          };
          marketingBudget *=
            rates[profile.monthly_marketing_budget_currency] ?? 1;
          // ensure whole number
          marketingBudget = Math.round(marketingBudget);
        }
        return [
          item.profile?.created_at.toDate().toDateString(),
          item.profile?.name,
          item.profile?.business_name ?? '',
          employeeCount,
          marketingBudget,
          item.profile?.has_calling_team ?? '',
          item.profile?.is_affiliate_marketing ? 'Yes' : 'No',
          item.profile?.email,
          `${item.profile?.mobile_dial_code}${item.profile?.mobile}`,
          item.business_details?.business_category ?? '',
          item.business_details?.product_or_service_description ?? '',
          item.business_details?.product_or_service_offers_or_usp ?? '',
        ];
      };
      // write to BE master sheet
      await appendDataToSheet({
        sheetName: 'Leads', // 'signups',
        rowData: data.map((item) => {
          return getSignupsRowData(item);
        }),
      });

      // also to AdGlobalAI BE master sheet
      await appendDataToSheet({
        spreadsheetId: ADGLOBAL_AI_BE_MASTER_GOOGLE_SHEET_ID,
        sheetName: 'Leads', // 'signups',
        rowData: data
          .filter(
            (item) => item.profile?.partner === GrowEasyPartners.AD_GLOBAL_AI,
          )
          .map((item) => {
            return getSignupsRowData(item);
          }),
      });
    }
    void sendOnboardingFunnelDropComms();
    void checkAndSendFinishedCampaignsReport();
    void updateExchangeRate();
    return {
      profiles_count: data.length,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};
