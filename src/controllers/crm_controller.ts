import { type DocumentData } from 'firebase-admin/firestore';
import { FIRESTORE_COLLECTIONS } from '../constants';
import { db } from '../modules/firebase_admin_sdk';
import logger from '../modules/logger';
import { type IAuthUser } from '../types';
import { ILeadsStatus, type ILeadsCrmDetails } from '../types/leads_crm';
import ValidationError from '../utils/validation_error';
import { getSingleCampaign, isAdGlobalAiAdmin, isGrowEasyAdmin } from './util';
import { CapiLeadFunnelEvents } from '../types/conversion_api';
import {
  getCampaignDetailsForAdGlobalAiAdmin,
  getCampaignDetailsForGrowEasyAdmin,
} from './admin_controller';
import { type ICampaign } from '../types/campaign_details';
import { sendCapiLeadEventToMeta } from './conversion_api_controller';

export const getLeadsCrmRecords = async (
  queryParams: Record<string, any>,
  user?: IAuthUser,
): Promise<ILeadsCrmDetails[] | null> => {
  const campaignId = queryParams.campaign_id;
  try {
    let query = db
      .collection(FIRESTORE_COLLECTIONS.LEADS_CRM)
      .where('campaign_id', '==', campaignId);
    if (isGrowEasyAdmin(user) || isAdGlobalAiAdmin(user)) {
      // no uid filter, so that Admin can also see details
    } else {
      query = query.where('uid', '==', `${user?.uid}`);
    }
    const snapshot = await query.limit(500).get();
    const leads: ILeadsCrmDetails[] = [];
    snapshot.forEach((doc) => {
      leads.push(doc.data() as ILeadsCrmDetails);
    });
    return leads;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getLeadsCrmDetails = async (
  leadgenId: string,
  user?: IAuthUser,
): Promise<DocumentData | null> => {
  let query = db
    .collection(FIRESTORE_COLLECTIONS.LEADS_CRM)
    .where('leadgen_id', '==', leadgenId);
  if (isGrowEasyAdmin(user) || isAdGlobalAiAdmin(user)) {
    // no uid filter, so that Admin can also see details
  } else {
    query = query.where('uid', '==', `${user?.uid}`);
  }
  const snapshot = await query.get();
  return snapshot.docs[0] ?? null;
};

export const createOrUpdateLeadsCrm = async (
  leadDetails: Partial<ILeadsCrmDetails>,
  user?: IAuthUser,
): Promise<ILeadsCrmDetails | null> => {
  if (!leadDetails.leadgen_id || !leadDetails.campaign_id || !user?.uid) {
    throw new ValidationError(
      400,
      'leadgen_id and campaign_id are mandatory fields',
    );
  }
  try {
    let campaignDetailsDocSnapshot;
    if (isGrowEasyAdmin(user)) {
      campaignDetailsDocSnapshot = await getCampaignDetailsForGrowEasyAdmin(
        leadDetails.campaign_id,
      );
    } else if (isAdGlobalAiAdmin(user)) {
      campaignDetailsDocSnapshot = await getCampaignDetailsForAdGlobalAiAdmin(
        leadDetails.campaign_id,
      );
    } else {
      campaignDetailsDocSnapshot = await getSingleCampaign(
        user?.uid,
        leadDetails.campaign_id,
      );
    }
    const campaignDetails = campaignDetailsDocSnapshot?.data() as ICampaign;
    if (!campaignDetails) {
      throw new ValidationError(400, 'Campaign not found');
    }
    // determine conversion api funnel events state
    if (campaignDetails?.details?.config?.meta_capi_enabled) {
      const capiLeadFunnelEvents = leadDetails.capi_lead_funnel_events ?? [
        CapiLeadFunnelEvents.INITIAL_LEAD,
      ];
      if (leadDetails.status === ILeadsStatus.GOOD_LEAD_FOLLOW_UP) {
        // fire sales_opportunity if not already fired
        if (
          !capiLeadFunnelEvents.includes(CapiLeadFunnelEvents.SALES_OPPORTUNITY)
        ) {
          void sendCapiLeadEventToMeta({
            lead_id: leadDetails.leadgen_id,
            event_name: CapiLeadFunnelEvents.SALES_OPPORTUNITY,
            campaign_id: campaignDetails.id,
          });
          capiLeadFunnelEvents.push(CapiLeadFunnelEvents.SALES_OPPORTUNITY);
        }
      } else if (leadDetails.status === ILeadsStatus.SALE_DONE) {
        // fire sales_opportunity if not already fired
        if (!capiLeadFunnelEvents.includes(CapiLeadFunnelEvents.CONVERTED)) {
          void sendCapiLeadEventToMeta({
            lead_id: leadDetails.leadgen_id,
            event_name: CapiLeadFunnelEvents.CONVERTED,
            campaign_id: campaignDetails.id,
          });
          capiLeadFunnelEvents.push(CapiLeadFunnelEvents.CONVERTED);
        }
      }
      // update funnel events state
      leadDetails.capi_lead_funnel_events = capiLeadFunnelEvents;
    }

    let updatedOrCreatedDocId;
    const doc = await getLeadsCrmDetails(leadDetails.leadgen_id, user);
    if (doc) {
      // update
      await db
        .collection(FIRESTORE_COLLECTIONS.LEADS_CRM)
        .doc(doc.id)
        .set(leadDetails, {
          // caution: shallow merge only
          merge: true,
        });
      updatedOrCreatedDocId = doc.id;
    } else {
      const ref = await db.collection(FIRESTORE_COLLECTIONS.LEADS_CRM).add({
        ...leadDetails,
        uid: user.uid,
      });
      updatedOrCreatedDocId = ref.id;
    }
    const updatedOrCreatedDoc = await db
      .collection(FIRESTORE_COLLECTIONS.LEADS_CRM)
      .doc(updatedOrCreatedDocId)
      .get();
    return (updatedOrCreatedDoc.data() as ILeadsCrmDetails) ?? null;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};
