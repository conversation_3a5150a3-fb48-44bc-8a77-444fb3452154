import { Timestamp } from 'firebase-admin/firestore';
import { BANNERBOT_FIRESTORE_COLLECTIONS } from '../../constants';
import { bannerbotDb } from '../../modules/firebase_admin_sdk';
import { type IAuthUser } from '../../types';
import { type ICouponCode } from '../../types/bannerbot';
import {
  UserSubscriptionSource,
  type IUserSubscription,
} from '../../types/bannerbot/subscription';
import ValidationError from '../../utils/validation_error';

export async function redeemCoupon(
  {
    coupon_code: couponCode,
  }: {
    coupon_code: string;
  },
  user: IAuthUser,
): Promise<void> {
  if (!user?.uid) {
    throw new ValidationError(400, 'Unauthorized user');
  }

  if (!couponCode) {
    throw new ValidationError(400, 'Valid coupon code is required');
  }

  // Sanitize coupon code
  const sanitizedCode = couponCode.trim().toUpperCase();

  const query = bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.COUPON_CODES)
    .where('code', '==', sanitizedCode)
    .where('is_active', '==', true);

  const querySnapshot = await query.get();

  // Check if coupon exists
  if (querySnapshot.empty) {
    throw new ValidationError(
      400,
      'Invalid coupon code. Please check your code and try again.',
    );
  }

  const couponDocRef = querySnapshot.docs[0].ref;
  const couponData = querySnapshot.docs[0].data() as ICouponCode;

  // Check if already redeemed
  if (couponData.redeemed_at ?? couponData.redeemed_by) {
    throw new ValidationError(
      400,
      'This coupon code has already been redeemed.',
    );
  }

  // Check if coupon is expired
  const now = new Date();
  const validUntil = couponData.valid_until.toDate();

  if (validUntil < now) {
    throw new ValidationError(400, 'This coupon code has expired.');
  }

  // Check if user already redeemed any AppSumo coupon
  const query2 = bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.COUPON_CODES)
    .where('redeemed_by', '==', user.uid)
    .where('metadata.source', '==', UserSubscriptionSource.APP_SUMO);

  const userRedemptionSnapshot = await query2.get();

  if (!userRedemptionSnapshot.empty) {
    throw new ValidationError(
      400,
      'You have already redeemed an AppSumo coupon. Only one redemption per user is allowed.',
    );
  }

  const redemptionData: Pick<ICouponCode, 'redeemed_at' | 'redeemed_by'> = {
    redeemed_at: Timestamp.now(),
    redeemed_by: user.uid,
  };

  await couponDocRef.update(redemptionData);

  // create subscription for the user
  const userSubscriptionDoc = bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.USER_SUBSCRIPTIONS)
    .doc();

  const subscriptionData: IUserSubscription = {
    id: userSubscriptionDoc.id,
    uid: user.uid,
    source: couponData.metadata.source ?? UserSubscriptionSource.APP_SUMO,
    subscription_plan_id: 'ToHOwEvhcVCqlyfTJGsO', // the subscription plan id
    appsumo_coupon_code: couponCode,
    created_at: Timestamp.now(),
    updated_at: Timestamp.now(),
  };

  await userSubscriptionDoc.set(subscriptionData);
}
