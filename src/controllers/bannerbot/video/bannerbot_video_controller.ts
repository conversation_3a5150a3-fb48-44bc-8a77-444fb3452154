import { Timestamp } from 'firebase-admin/firestore';
import { getSampleVideoData } from '../../../modules/fp-video-service';
import {
  FpVideoTemplates,
  type IFpVideoData,
} from '../../../modules/fp-video-service/types';
import logger from '../../../modules/logger';
import { getResponseFromAzureOpenAi } from '../../../modules/openai';
import {
  getImageAndVideoKeywordsPrompt,
  getVideoDataPrompt,
} from '../../../prompts/bannerbot_prompts';
import { type IAuthUser } from '../../../types';
import {
  BannerbotAdType,
  type IBannerbotVideoAdDetails,
  type IBannerbotProject,
} from '../../../types/bannerbot';
import ValidationError from '../../../utils/validation_error';
import {
  addTemplatizedVideo,
  getSingleProject,
  isDailyLimitReached,
  updateBannerbotProject,
  updateTemplatizedVideo,
} from '../bannerbot_controller';
import { getImagesFromFreepik } from '../../../modules/freepik';
import { getVideosFromPexels } from '../../../modules/pexels';
import { type AxiosError } from 'axios';
import { createVideo } from '../../../modules/remotion';
import { sendVideoGeneratedEmail } from '../email_controller';
import { DEV } from '../../../constants';
import { isGrowEasyAdmin } from '../../util';

export const getVideoData = async (
  params: {
    templateId: FpVideoTemplates;
    projectId: string;
  },
  user?: IAuthUser,
): Promise<IFpVideoData | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const { templateId, projectId } = params;

  const projectDetails = (
    await getSingleProject(user.uid, projectId)
  )?.data() as IBannerbotProject;
  const sampleVideoData = getSampleVideoData(templateId);

  if (!projectDetails || !sampleVideoData) {
    throw new ValidationError(400, 'Missing project details or video data');
  }

  const videoDataPrompt = getVideoDataPrompt({
    businessDetails: projectDetails?.details?.business_details ?? {},
    sampleVideoData,
  });
  const videoData = (await getResponseFromAzureOpenAi(
    videoDataPrompt,
  )) as IFpVideoData;

  // remove the logo node if the url is not present
  if (videoData?.branding?.logo?.url === '') {
    videoData.branding.logo = undefined;
  }

  // populate images & videos
  const mediaKeywordsPrompt = getImageAndVideoKeywordsPrompt({
    businessDetails: projectDetails?.details?.business_details ?? {},
  });
  try {
    const openAiResponseForMediaKeywords = (await getResponseFromAzureOpenAi(
      mediaKeywordsPrompt,
    )) as {
      image_keywords: string[];
      video_content_description_involving_people: string;
    };
    logger.info(openAiResponseForMediaKeywords);
    const relevantImages = await getImagesFromFreepik(
      openAiResponseForMediaKeywords.image_keywords?.join(', '),
      '25',
    );
    const relevantVideos = await getVideosFromPexels({
      queries: [
        openAiResponseForMediaKeywords.video_content_description_involving_people,
      ],
      orientation: 'portrait',
    });
    // only HD videos
    const relevantHdVideosLinks: string[] = [];
    for (const relevantVideo of relevantVideos) {
      const hdVideoUrl = relevantVideo.video_files?.find(
        (item) => item.quality === 'hd',
      )?.link;
      if (hdVideoUrl) {
        relevantHdVideosLinks.push(hdVideoUrl);
      }
    }

    const productImages =
      projectDetails?.details?.business_details?.product_images ?? [];

    let imageIndex = 0;
    let videoIndex = 0;

    // populate images & videos
    videoData.scenes?.forEach((scene) => {
      scene.assets?.forEach((asset) => {
        if (asset.type === 'image') {
          asset.url =
            productImages[imageIndex]?.url ??
            relevantImages[imageIndex % relevantImages.length]?.url;
          imageIndex++;
        } else if (asset.type === 'video' && relevantHdVideosLinks.length) {
          asset.url =
            relevantHdVideosLinks[videoIndex % relevantHdVideosLinks.length];
          videoIndex++;
        }
      });
    });

    // special handling for base video
    if (
      videoData?.base_assets?.[0]?.type === 'video' &&
      relevantHdVideosLinks[0]
    ) {
      videoData.base_assets[0].url = relevantHdVideosLinks[0];
    }
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
  }

  return videoData;
};

// async function to generate video and persist + notify
const createVideoUsingRemotionAndSaveToDb = async (params: {
  uid: string;
  templateData: IFpVideoData;
  videoId: string;
  projectId: string;
  templatizedVideoId: string;
}): Promise<void> => {
  const { uid, templateData, videoId, projectId, templatizedVideoId } = params;
  const videoCreationStartTs = Date.now();

  try {
    const createVideoResponse = await createVideo({
      uid,
      templateData,
      projectId,
      videoId,
    });
    // save video link in project details
    logger.info(
      `createVideo response ${createVideoResponse.s3_url} for video id: ${videoId}`,
    );
    // get project details from db
    const currentProjectDetails = (
      await getSingleProject(uid, projectId)
    )?.data() as IBannerbotProject;
    const currentExistingVideos =
      currentProjectDetails.details?.saved_assets?.[BannerbotAdType.VIDEO] ??
      [];

    const finalVideos = currentExistingVideos.map((video) =>
      video.id === videoId
        ? {
            ...video,
            url: createVideoResponse.s3_url,
            thumbnail_url: createVideoResponse.thumbnail_s3_url,
            updated_at: Date.now(),
          }
        : video,
    );
    await updateBannerbotProject(projectId, {
      details: {
        ...currentProjectDetails.details,
        saved_assets: {
          ...currentProjectDetails.details?.saved_assets,
          [BannerbotAdType.VIDEO]: finalVideos,
        },
      },
      updated_at: Timestamp.now(),
    });

    void updateTemplatizedVideo(templatizedVideoId, {
      thumbnail_url: createVideoResponse.thumbnail_s3_url,
      video_url: createVideoResponse.s3_url,
      updated_at: Timestamp.now(),
    });

    const videoCreationEndTs = Date.now();
    const videoCreationTimeInSecs =
      (videoCreationEndTs - videoCreationStartTs) / 1000;
    logger.info(
      `generateFpVideo processed in ${videoCreationTimeInSecs} secs with URL: ${createVideoResponse.s3_url} for video id ${videoId} and project ${projectId}`,
    );

    // notify user via email on prod env
    if (!DEV) {
      void sendVideoGeneratedEmail({
        uid,
        businessDetails: currentProjectDetails.details?.business_details,
        videoCreationTimeInSecs,
        videoUrl: createVideoResponse.s3_url,
        projectId,
      });
    }
  } catch (error) {
    logger.error(error);
    logger.error(
      `generateFpVideo failed to process for project ${projectId} and video id ${videoId}`,
    );
  }
};

// it actually generates remotion video, check import of `createVideo`
export const generateFpVideo = async (
  params: {
    projectId: string;
    templateData: IFpVideoData;
  },
  user?: IAuthUser,
): Promise<IBannerbotVideoAdDetails> => {
  const { projectId, templateData } = params;

  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  // First check if the user limit is reached or not
  const { doesUserhaveSubscription, isLimitReached } =
    await isDailyLimitReached(user.uid, 'fp_video');

  if (!isGrowEasyAdmin(user) && isLimitReached && doesUserhaveSubscription) {
    throw new ValidationError(
      402,
      'You have exhausted your daily credits. Please try again tomorrow.',
    );
  } else if (!isGrowEasyAdmin(user) && isLimitReached) {
    throw new ValidationError(
      403,
      'You have reached your daily limit. Please subscribe to continue.',
    );
  }

  const projectDetails = (
    await getSingleProject(user.uid, projectId)
  )?.data() as IBannerbotProject;

  if (!projectDetails) {
    throw new ValidationError(400, 'Missing project details');
  }

  if (!Object.keys(FpVideoTemplates).includes(templateData.template_id)) {
    throw new ValidationError(400, 'Bad template_id');
  }

  try {
    const timestamp = Date.now();
    const videoId = `${templateData.template_id}_${timestamp}`;

    // Fetch existing saved video assets
    const existingVideos =
      projectDetails.details?.saved_assets?.[BannerbotAdType.VIDEO] ?? [];

    const savedVideoAsset: IBannerbotVideoAdDetails = {
      id: videoId,
      url: '',
      thumbnail_url: '',
      caption: templateData.video_caption,
      template_id: templateData.template_id,
      width: templateData.width,
      height: templateData.height,
      created_at: timestamp,
      updated_at: timestamp,
    };
    // make video entry into saved_assets
    const updatedVideos = [...existingVideos, savedVideoAsset];
    await updateBannerbotProject(projectId, {
      details: {
        ...projectDetails.details,
        saved_assets: {
          ...projectDetails.details?.saved_assets,
          [BannerbotAdType.VIDEO]: updatedVideos,
        },
      },
      updated_at: Timestamp.now(),
    });

    const templatizedVideoId = await addTemplatizedVideo({
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
      id: '',
      project_id: projectId,
      video_url: '',
      thumbnail_url: '',
      uid: user.uid,
      video_id: savedVideoAsset.id,
    });

    logger.info(
      `generateFpVideo processing started for video id: ${videoId} and project id: ${projectId}`,
    );
    void createVideoUsingRemotionAndSaveToDb({
      uid: user.uid,
      templateData,
      videoId,
      projectId,
      templatizedVideoId,
    });
    return savedVideoAsset;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};
