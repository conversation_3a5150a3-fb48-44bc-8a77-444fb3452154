import fs, { existsSync, mkdirSync } from 'fs';
import {
  generateSpeechWithTimingFromText,
  getWordsAlignmentFromCharAlignment,
  removeLongFormWordsFromTranscript,
} from '../../../modules/elevenlabs';
import {
  getResponseFromAzureOpenAi,
  getResponseFromOpenAi,
} from '../../../modules/openai';
import { type IAuthUser } from '../../../types';
import {
  BANNERBOT_FIRESTORE_COLLECTIONS,
  BANNERBOT_S3_PUBLIC_BUCKET_NAME,
  S3_BANNERBOT_VIDEO_MAKER_AUDIO_UPLOAD_DIR,
  TMP_BANNERBOT_AUDIO_UPLOAD_DIR,
} from '../../../constants';
import { v4 as uuidv4 } from 'uuid';
import { uploadFileToS3 } from '../../../modules/aws/s3/index';
import { getVideosFromPexels } from '../../../modules/pexels';
import logger from '../../../modules/logger';
import { bannerbotDb } from '../../../modules/firebase_admin_sdk';
import { Timestamp } from 'firebase-admin/firestore';
import ValidationError from '../../../utils/validation_error';
import { getSingleProject } from '../../bannerbot/bannerbot_controller';
import {
  type IBannerbotBusinessDetails,
  type IBannerbotProject,
} from '../../../types/bannerbot';
import { getSampleVideoData } from '../../../modules/fp-video-service';
import {
  type IFpVideoData,
  type FpVideoTemplates,
  type IFpVideoDataV2,
} from '../../../modules/fp-video-service/types';
import {
  getTwoSceneVideoPromptsFromProduct,
  getImageAndVideoKeywordsPromptV2,
  getVideoAdDirectorPromptV4,
  getVideoDataPrompt,
  getVideoRelevancePrompt,
  getVoiceOverScriptV3Prompt,
} from '../../../prompts/bannerbot_prompts';
import { AdLanguage } from '../../../types/campaign_details';
import { type IPexelsVideoData } from '../../../types/stock_image';
import { getHdVideoUrlsFromPexelsData } from '../../../utils/index';
import { addBgMusicToAudio } from '../../../modules/ffmpeg';
import {
  generateAiVideoAndWaitForCompletion,
  POLLO_AI_VIDEO_LENGTH,
  POLLO_AI_VIDEO_RESOLUTION,
  POLLO_AI_VIDEO_ROUTES,
} from '../../../modules/pollo_ai';
import { getVideosFromStoryblocks } from '../../../modules/storyblocks';
import videoLogger from '../../../modules/video_logger';
import { getImagesFromFreepik } from '../../../modules/freepik';

// TODO: clean
// const sanitizeDevanagariText = (text: string): string => {
//   return (
//     text
//       // Remove Devanagari punctuation
//       .replace(/।/g, '') // Devanagari danda (।)
//       .replace(/॥/g, '') // Devanagari double danda (॥)

//       // Remove English punctuation
//       .replace(/[.,!?;:"'()-]/g, '') // Common punctuation
//       .replace(/\./g, '') // Periods (escaped properly)

//       // Remove extra whitespace
//       .replace(/\s+/g, ' ') // Multiple spaces to single space
//       .trim()
//   ); // Remove leading/trailing spaces
// };

// to fix the errors occured during production
const addVideoDataErrorToFirebase = async (params: object): Promise<void> => {
  const newVideoErrorsDoc = bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.NEW_VIDEO_ERRORS)
    .doc();
  await newVideoErrorsDoc.set({ ...params, created_at: Timestamp.now() }, {});
};

export const getHdVideoUrlWithDurationFromPexels = async (
  keyword: string,
  useStoryblocks = false, // testing for now
): Promise<{
  hdVideoUrls: Array<{ url: string; duration: number }>;
  relevantVideos: IPexelsVideoData[];
}> => {
  logger.info(
    `searching for keyword ${keyword} in ${
      useStoryblocks ? 'storyblocks' : 'pexels'
    }`,
  );

  if (useStoryblocks) {
    const relevantVideos = await getVideosFromStoryblocks([keyword]);
    const hdVideoUrls = relevantVideos.map((item) => ({
      url: item.preview_urls._720p,
      duration: item.duration,
    }));
    return { hdVideoUrls, relevantVideos: [] };
  }

  const relevantVideos = await getVideosFromPexels({
    queries: [keyword],
    orientation: 'portrait',
  });

  const hdVideoUrls = getHdVideoUrlsFromPexelsData(relevantVideos);

  return { hdVideoUrls, relevantVideos: [] };
};

const getBestHdVideoUrlWithDurationFromPexels = async (params: {
  keyword: string;
  businessDetails: Partial<IBannerbotBusinessDetails>;
}): Promise<{
  media: { url: string; mediaType: 'video' | 'image' };
}> => {
  const RELEVANCE_THRESHOLD = 80;

  const { keyword, businessDetails } = params;

  let videoId: string = '';
  let videoScore: number = 0;
  let relevantVideos: IPexelsVideoData[] | null = null;

  // find the video with the highest score
  // retry 3 times
  for (let i = 1; i <= 3; i++) {
    videoLogger.info(
      `searching for keyword ${keyword} in pexels for page ${i}`,
    );

    const tempRelevantVideos = await getVideosFromPexels({
      queries: [keyword],
      orientation: 'portrait',
      page: i,
    });

    videoLogger.info(`Fetched ${tempRelevantVideos.length} videos from Pexels`);

    // eg: for the url "https://www.pexels.com/video/video-of-forest-1448735/" we will get the output -> video of forest
    const idWithDescriptionArr = tempRelevantVideos.map((item) => ({
      id: item.id.toString(),
      description:
        item.url?.split('/')?.[4]?.split('-')?.slice(0, -1)?.join(' ') ?? '',
    }));

    const videoRelevancePrompt = getVideoRelevancePrompt({
      business_details: businessDetails,
      keyword,
      videos: idWithDescriptionArr,
    });

    const videoRelevancePromptResponse = (await getResponseFromOpenAi(
      videoRelevancePrompt,
    )) as Record<string, number>;

    let currScore = 0;
    let currId = '';

    for (const [key, value] of Object.entries(videoRelevancePromptResponse)) {
      if (value > currScore) {
        currId = key;
        currScore = value;
      }
    }

    if (currId > videoId) {
      videoId = currId;
      videoScore = currScore;
      relevantVideos = tempRelevantVideos;
    }

    if (videoScore >= RELEVANCE_THRESHOLD) {
      break;
    }

    videoLogger.info(
      `Failed to find relevant video for keyword ${keyword} on page ${i}. Highest score: ${currScore}`,
    );
  }

  // fallback when the Relevant video is not found
  if (videoScore < RELEVANCE_THRESHOLD) {
    const freepikResponse = await getImagesFromFreepik(
      keyword,
      '15',
      'portrait',
    );

    if (freepikResponse.length) {
      videoLogger.info(
        `used the freepik image fallback for the keyword ${keyword}`,
      );

      // since the response is sorted on the basis of relevance taking the top one
      return {
        media: {
          url: freepikResponse[0].url,
          mediaType: 'image',
        },
      };
    }
  }

  const filteredPexelResponse =
    relevantVideos?.filter((item) => item.id === parseInt(videoId)) ?? [];

  if (!filteredPexelResponse.length)
    throw new Error(`missing video with id ${videoId} in response from pexels`);

  const [hdVideoUrl] = getHdVideoUrlsFromPexelsData(filteredPexelResponse);

  videoLogger.info(
    `Selected best video with score: ${videoScore} for the keyword ${keyword}. URL -> ${hdVideoUrl.url}`,
  );

  return { media: { ...hdVideoUrl, mediaType: 'video' } };
};

const getMultipleVideoScenesData = async ({
  businessDetails,
  script,
  transcript,
  ref,
  useStoryblocks = false,
}: {
  businessDetails: Partial<IBannerbotBusinessDetails>;
  script: string;
  transcript: Array<{
    text: string;
    start: number;
    end: number;
  }>;
  ref: {
    videoScenes: IFpVideoDataV2['scenes'] | undefined;
    videoAdDirectorResponse:
      | {
          chunks: Array<{
            script: string;
            keyword: string;
          }>;
        }
      | undefined;
  };
  useStoryblocks?: boolean; // testing for now
}): Promise<void> => {
  const videoAdDirectorPrompt = getVideoAdDirectorPromptV4({
    business_details: businessDetails,
    script,
  });
  ref.videoAdDirectorResponse = (await getResponseFromAzureOpenAi(
    videoAdDirectorPrompt,
  )) as { chunks: Array<{ script: string; keyword: string }> };

  videoLogger.info(
    `Director response: ${JSON.stringify(ref.videoAdDirectorResponse)}`,
  );

  let transcriptCursor = 0;
  let globalEndTime = 0;
  const chunkTimingData: Array<{
    chunk: {
      script: string;
      keyword: string;
    };
    startTime: number;
    endTime: number | undefined;
    sceneId: number;
  }> = [];

  logger.info('adding scenes videos start');

  for (const chunk of ref.videoAdDirectorResponse.chunks) {
    if (chunk.script === '') continue;

    const scriptArr = chunk.script.split(' ');

    transcriptCursor = transcriptCursor + scriptArr.length - 1;

    if (transcriptCursor >= transcript.length)
      throw new Error(
        'transcriptCursor exceeded transcript array length in getMultipleVideoScenesData',
      );

    const endIndex = transcriptCursor;

    const startTime = globalEndTime;
    const endTime = transcript[endIndex].end ?? 0;

    // only push if the chunk is of resoanable time
    if (endTime - startTime > 1.5) {
      globalEndTime = endTime;

      chunkTimingData.push({
        chunk,
        startTime,
        endTime,
        sceneId: chunkTimingData.length + 1,
      });
    }
  }

  // Step 2: Fetch all media in parallel
  const mediaPromises = chunkTimingData.map(
    async ({ chunk, startTime, endTime, sceneId }) => {
      const mediaData = await getBestHdVideoUrlWithDurationFromPexels({
        keyword: chunk.keyword,
        businessDetails,
      });

      if (
        startTime === undefined ||
        endTime === undefined ||
        !mediaData.media.url
      ) {
        throw new Error(
          'missing data in startTime or endTime or hdVideoUrls in createVideo',
        );
      }

      return {
        scene_id: sceneId,
        assets: [{ type: mediaData.media.mediaType, url: mediaData.media.url }],
        start: startTime,
        end: endTime,
      };
    },
  );

  ref.videoScenes = await Promise.all(mediaPromises);
  videoLogger.info(`✅ All scenes processed: `, ref.videoScenes);
};

export const getVideoDataUsingDetailsV2 = async ({
  businessDetails,
  language,
  templateId,
  getMediaSuggestions = false,
  useStoryblocks = false,
  useAiVideoGeneration = false,
}: {
  templateId: FpVideoTemplates;
  businessDetails: Partial<IBannerbotBusinessDetails>;
  language: AdLanguage;
  getMediaSuggestions?: boolean;
  useStoryblocks?: boolean;
  useAiVideoGeneration?: boolean;
}): Promise<{
  videoData: IFpVideoData | null;
  mediaSuggestion?: IPexelsVideoData[]; // mediaSuggestions will be given when branding details are in the sampleVideoData
}> => {
  /**
   * overall flow of generating video data for the new videos
   * - fist generate the script
   * - then generate the voice from the script
   * - then generate the subtitle using the voice
   * - then generate different scenes using the script
   * - add the timing to the scenes using the subtitle
   */

  // making a object since the value needs to be mutated inside the function
  const ref: {
    filteredVoiceOverScript: string | undefined;
    scriptFromTranscript: string | undefined;
    videoScenes: IFpVideoDataV2['scenes'] | undefined;
    videoAdDirectorResponse:
      | {
          chunks: Array<{
            script: string;
            keyword: string;
          }>;
        }
      | undefined;
    transcript: Array<{ text: string; start: number; end: number }> | undefined;
    audioS3Url: string | undefined;
  } = {
    audioS3Url: undefined,
    transcript: undefined,
    videoAdDirectorResponse: undefined,
    videoScenes: undefined,
    scriptFromTranscript: undefined,
    filteredVoiceOverScript: undefined,
  };

  try {
    // For AI generation, use p10 template instead of the passed templateId
    const actualTemplateId = useAiVideoGeneration ? 'p10' : templateId;
    let sampleVideoData = getSampleVideoData(
      actualTemplateId as FpVideoTemplates,
    );

    if (!sampleVideoData) {
      throw new Error('sampleVideoData not found for template');
    }

    // this is to add the company details like brand name, website and logo
    if (sampleVideoData.branding) {
      const videoDataPrompt = getVideoDataPrompt({
        businessDetails,
        sampleVideoData,
        language,
      });
      const videoData = (await getResponseFromAzureOpenAi(
        videoDataPrompt,
      )) as IFpVideoData;

      // remove the logo node if the url is not present
      if (videoData?.branding?.logo?.url === '') {
        videoData.branding.logo = undefined;
      }
      sampleVideoData = videoData;
    }

    // first generating the voice over script for the product
    const voiceOverScriptPrompt = getVoiceOverScriptV3Prompt({
      business_details: businessDetails,
      language,
    });
    const voiceOverScriptResponse = (await getResponseFromOpenAi(
      voiceOverScriptPrompt,
    )) as { script: string; highlights: string[] };

    logger.info(voiceOverScriptResponse);

    ref.filteredVoiceOverScript = voiceOverScriptResponse.script.replace(
      /\n/g,
      '',
    );

    // now use the script to generate the voice for elevenlabs
    if (!existsSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR)) {
      mkdirSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR, { recursive: true });
    }
    const audioFileName = `${Date.now()}-${uuidv4()}.mp3`;
    const audioFilePath = `${TMP_BANNERBOT_AUDIO_UPLOAD_DIR}/${audioFileName}`;

    // use ffmpeg to add subtle bg music
    const audioWithBgMusicFilePath = `${TMP_BANNERBOT_AUDIO_UPLOAD_DIR}/bg-${audioFileName}`;

    const voiceOverScript = ref.filteredVoiceOverScript.replace(
      /\{([^}]+)\}\[[^\]]+\]/g,
      '$1',
    );

    logger.info(voiceOverScript);

    const speechResponse = await generateSpeechWithTimingFromText({
      text: voiceOverScript,
      outputFilePath: audioFilePath,
    });

    // try adding bg music to audio
    let finalAudioPath = audioWithBgMusicFilePath;
    try {
      await addBgMusicToAudio({
        inputAudioFilePath: audioFilePath,
        outputAudioFilePath: audioWithBgMusicFilePath,
      });
    } catch (error) {
      // adding bg music failed, use original audio path
      logger.error(error);
      finalAudioPath = audioFilePath;
    }

    ref.audioS3Url = await uploadFileToS3(
      BANNERBOT_S3_PUBLIC_BUCKET_NAME,
      finalAudioPath,
      `${S3_BANNERBOT_VIDEO_MAKER_AUDIO_UPLOAD_DIR}/${audioFileName}`,
    );

    // clean audio paths from machine after uploading to s3
    try {
      await fs.promises.unlink(audioFilePath);
      // await fs.promises.unlink(audioWithBgMusicFilePath);
    } catch (error) {
      logger.error(error);
    }

    // so we have characters alignment data but we need the word alignment data
    ref.transcript = getWordsAlignmentFromCharAlignment(
      speechResponse.alignment,
    );

    const newTranscript = removeLongFormWordsFromTranscript(
      ref.transcript,
      ref.filteredVoiceOverScript,
    );

    ref.transcript = newTranscript;

    logger.info('Audio using elevenlabs generated ', ref.audioS3Url);

    ref.scriptFromTranscript = ref.transcript.map((i) => i.text).join(' ');

    // now break the script into different part and for each part have different images/videos for specific template
    // Skip scene processing when using AI video generation
    if (
      ['p10'].includes(sampleVideoData.template_id) &&
      !useAiVideoGeneration
    ) {
      await getMultipleVideoScenesData({
        businessDetails,
        transcript: ref.transcript,
        script: ref.scriptFromTranscript,
        ref,
        useStoryblocks,
      });

      sampleVideoData.scenes = ref.videoScenes;
    }

    let mediaSuggestion: IPexelsVideoData[] | undefined;

    // Handle AI video generation or stock video processing
    if (useAiVideoGeneration && sampleVideoData.base_assets) {
      logger.info('generating AI base videos with 2 scenes');

      if (!ref.scriptFromTranscript) {
        throw new Error(
          'Script from transcript is required for AI video generation',
        );
      }

      // Generate scene prompts first
      const twoSceneVideoPromptsFromProduct =
        getTwoSceneVideoPromptsFromProduct({
          business_details: businessDetails,
          script: ref.scriptFromTranscript,
        });

      const twoSceneVideoResponse = (await getResponseFromOpenAi(
        twoSceneVideoPromptsFromProduct,
      )) as { scene1_prompt: string; scene2_prompt: string };

      logger.info('ai base video prompts -> ', twoSceneVideoResponse);

      // Generate both videos in parallel using the generated prompts
      logger.info(
        'Generating AI videos for both scenes in parallel with endpoint:',
        POLLO_AI_VIDEO_ROUTES.SEEDANCE_LITE,
      );

      const [scene1VideoUrl, scene2VideoUrl] = await Promise.all([
        generateAiVideoAndWaitForCompletion(
          POLLO_AI_VIDEO_ROUTES.SEEDANCE_LITE,
          {
            prompt: twoSceneVideoResponse.scene1_prompt,
            length: POLLO_AI_VIDEO_LENGTH.FIVE_SECONDS,
            resolution: POLLO_AI_VIDEO_RESOLUTION['480p'],
            cameraFixed: false,
          },
          'Scene 1',
        ),
        generateAiVideoAndWaitForCompletion(
          POLLO_AI_VIDEO_ROUTES.SEEDANCE_LITE,
          {
            prompt: twoSceneVideoResponse.scene2_prompt,
            length: POLLO_AI_VIDEO_LENGTH.FIVE_SECONDS,
            resolution: POLLO_AI_VIDEO_RESOLUTION['480p'],
            cameraFixed: false,
          },
          'Scene 2',
        ),
      ]);

      // const [scene1VideoUrl, scene2VideoUrl] = [
      //   'https://videocdn.pollo.ai/web-cdn/video/mp4/cmd9xxqt806fe2008ac9pr0va/video-476799cb-288f-42c7-9132-d2e3326b3045.mp4',
      //   'https://videocdn.pollo.ai/web-cdn/video/mp4/cmd9xxqt806fe2008ac9pr0va/video-604665f8-bf4d-4803-8476-c25fce4d3961.mp4',
      // ];

      logger.info('🔮 Generated Scene 1 AI video URL:', scene1VideoUrl);
      logger.info('🔮 Generated Scene 2 AI video URL:', scene2VideoUrl);

      // Generate keywords for stock videos
      const mediaKeywordsPrompt = getImageAndVideoKeywordsPromptV2({
        businessDetails,
      });
      const openAiResponseForMediaKeywords = (await getResponseFromAzureOpenAi(
        mediaKeywordsPrompt,
      )) as {
        image_keywords: string[];
        video_content_description_involving_people: string;
      };

      logger.info('Getting stock videos for opening and closing scenes...');

      // Get stock videos for opening and closing scenes
      const openingStockVideo = await getBestHdVideoUrlWithDurationFromPexels({
        keyword:
          openAiResponseForMediaKeywords.video_content_description_involving_people,
        businessDetails,
      });

      const closingStockVideo = await getBestHdVideoUrlWithDurationFromPexels({
        keyword:
          openAiResponseForMediaKeywords.video_content_description_involving_people,
        businessDetails,
      });

      logger.info('🎬 Opening stock video URL:', openingStockVideo.media.url);
      logger.info('🎬 Closing stock video URL:', closingStockVideo.media.url);

      // Create 4-scene structure: Stock (3s) + AI (5s) + AI (5s) + Stock (3s) = 16s total
      sampleVideoData.scenes = [
        {
          scene_id: 1,
          assets: [
            {
              type: openingStockVideo.media.mediaType,
              url: openingStockVideo.media.url, // Opening stock video from Pexels
            },
          ],
          start: 0,
          end: 3,
        },
        {
          scene_id: 2,
          assets: [
            {
              type: 'video',
              url: scene1VideoUrl, // AI video scene 1 from Pollo
            },
          ],
          start: 3,
          end: 8,
        },
        {
          scene_id: 3,
          assets: [
            {
              type: 'video',
              url: scene2VideoUrl, // AI video scene 2 from Pollo
            },
          ],
          start: 8,
          end: 13,
        },
        {
          scene_id: 4,
          assets: [
            {
              type: closingStockVideo.media.mediaType,
              url: closingStockVideo.media.url, // Closing stock video from Pexels
            },
          ],
          start: 13,
          end: 16,
        },
      ];

      sampleVideoData.duration_in_sec = 16;

      // Log the final video data structure for debugging
      logger.info('Final video data structure with AI videos (p12 format):', {
        template_id: sampleVideoData.template_id,
        scenes: sampleVideoData.scenes,
        base_audio: {
          url: sampleVideoData.base_audio?.url,
          subtitle_count:
            'subtitle' in (sampleVideoData.base_audio || {})
              ? (sampleVideoData.base_audio as any).subtitle?.length || 0
              : 0,
        },
        duration_in_sec: sampleVideoData.duration_in_sec,
      });
    } else if (getMediaSuggestions || sampleVideoData.base_assets) {
      // Stock video processing for non-AI video generation
      const mediaKeywordsPrompt = getImageAndVideoKeywordsPromptV2({
        businessDetails,
      });
      const openAiResponseForMediaKeywords = (await getResponseFromAzureOpenAi(
        mediaKeywordsPrompt,
      )) as {
        image_keywords: string[];
        video_content_description_involving_people: string;
      };

      const hdVideoData = await getHdVideoUrlWithDurationFromPexels(
        openAiResponseForMediaKeywords.video_content_description_involving_people,
        useStoryblocks,
      );

      if (getMediaSuggestions) {
        mediaSuggestion = hdVideoData.relevantVideos;
      }

      if (sampleVideoData.base_assets) {
        const firstHdVideo = hdVideoData.hdVideoUrls?.[0];

        sampleVideoData.base_assets.push({
          type: 'video',
          url: firstHdVideo?.url ?? '',
          video_duration: firstHdVideo?.duration,
        });
      }
    }

    // populate the data into sample
    sampleVideoData.base_audio = {
      url: ref.audioS3Url,
      subtitle: ref.transcript as IFpVideoDataV2['base_audio']['subtitle'],
    };

    // Only set duration if not already set by AI video generation
    if (!sampleVideoData.duration_in_sec) {
      sampleVideoData.duration_in_sec =
        (ref.transcript[ref.transcript.length - 1].end ?? 20) + 1;
    }

    if (
      voiceOverScriptResponse.highlights &&
      (sampleVideoData.scenes ?? []).length === 0
    ) {
      sampleVideoData.scenes ??= [];
      sampleVideoData.scenes.push({
        scene_id: 1,
        start: 0,
        end: sampleVideoData.duration_in_sec,
        texts: voiceOverScriptResponse.highlights.map((item) => ({
          value: item.replace(/\{([^}]+)\}\[[^\]]+\]/g, '$2'),
        })),
      });
    }

    return {
      videoData: sampleVideoData,
      mediaSuggestion,
    };
  } catch (error) {
    logger.error(error);
    if (error instanceof Error && !(error instanceof ValidationError)) {
      // TODO : To be removed after few days of testing
      void addVideoDataErrorToFirebase({
        success: false,
        error: error.message,
        ...ref,
      });
      throw new Error('Something went wrong'); // throwing generic message
    }
    throw error;
  }
};

export const getVideoData = async (
  params: {
    templateId: FpVideoTemplates;
    projectId: string;
  },
  user?: IAuthUser,
  useStoryblocks = false,
): Promise<IFpVideoData | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const { templateId, projectId } = params;

  const projectDetails = (
    await getSingleProject(user.uid, projectId)
  )?.data() as IBannerbotProject;

  const response = await getVideoDataUsingDetailsV2({
    businessDetails: projectDetails.details.business_details,
    templateId,
    language: AdLanguage.ENGLISH,
    useStoryblocks,
  });

  return response.videoData;
};
