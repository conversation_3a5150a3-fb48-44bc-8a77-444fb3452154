import { existsSync, mkdirSync } from 'fs';
import logger from '../../modules/logger';
import { getResponseFromAzureOpenAi } from '../../modules/openai';
import {
  BannerbotProjectStatus,
  type IBannerbotUserProfile,
  type IBannerbotProject,
  type IVideoData,
  type ITemplatizedVideo,
  BannerbotAdType,
  type IAiAdBanner,
} from '../../types/bannerbot';
import {
  type IPexelsVideoData,
  type IImageData,
} from '../../types/stock_image';
import {
  getPromptForFilteringImages,
  getPromptForFilteringVideos,
  getVideoMakerPrompt,
} from '../../prompts/groweasy_prompts';
import {
  TMP_BANNERBOT_AUDIO_UPLOAD_DIR,
  BANNERBOT_S3_PUBLIC_BUCKET_NAME,
  S3_BANNERBOT_VIDEO_MAKER_AUDIO_UPLOAD_DIR,
  BANNERBOT_FIRESTORE_COLLECTIONS,
  TMP_ADIMAGES_UPLOAD_DIR,
  S3_AI_BANNERS_DIR,
} from '../../constants';
import { convertTextToSpeech as googleTts } from '../../modules/google_tts';
import { convertTextToSpeech as elevenLabsTts } from '../../modules/eleven_labs_tts';
import { uploadFileToS3 } from '../../modules/aws/s3/index';
import { fsUnlink } from '../util';
import { getImagesFromPexels, getVideosFromPexels } from '../../modules/pexels';
import { type IAdCreativeData, type IAuthUser } from '../../types';
import { bannerbotAuth, bannerbotDb } from '../../modules/firebase_admin_sdk';
import ValidationError from '../../utils/validation_error';
import {
  type DocumentData,
  FieldValue,
  Timestamp,
} from 'firebase-admin/firestore';
import {
  getAdBannersPrompt,
  getAiBannerImageGenPrompt,
  getKeyBenefitsPrompt,
  getUspAndBannerElementDetailsPrompt,
} from '../../prompts/bannerbot_prompts';
import { getImagesFromUnsplash } from '../../modules/unsplash';
import { type IBannerTemplate } from '../../types/banner_template';
import templatesCollection1 from '../../constants/templates';
import templatesCollection2 from '../../constants/templates_set2';
import templatesCollection3 from '../../constants/templates_set3';
import { getTransformedImageUrl, deepCopy, saveFileToDisk } from '../../utils';
import { updateVariablesInTemplates } from '../openai_controller';
import { getImagesFromFreepik } from '../../modules/freepik';
import { isDesignEasyAdmin } from './util';
import {
  type IUserSubscription,
  type ISubscriptionPlan,
  UserSubscriptionSource,
} from '../../types/bannerbot/subscription';
import stripe from '../../modules/stripe';
import type Stripe from 'stripe';
import { generateAiBannerImage } from '../../modules/ideogram';
import { type AxiosError } from 'axios';

export const getVideoData = async (params: {
  query: string;
  google_tts: boolean;
  frame_type?: 'video' | 'image';
}): Promise<IVideoData> => {
  logger.info('getVideoData called for query', params.query);
  const prompt = getVideoMakerPrompt(params.query);
  const videoData: IVideoData = await getResponseFromAzureOpenAi(prompt);
  // console.dir(videoData, { depth: null });

  if (videoData?.frames?.length) {
    for (const frame of videoData.frames) {
      if (params.frame_type === 'video') {
        frame.videos = await getVideosFromPexels({
          queries: [frame.video_content_description_involving_people ?? ''],
        });
        const filterVideosPrompt = getPromptForFilteringVideos(
          frame.video_content_description_involving_people ?? '',
          frame.videos?.map((item) => ({
            id: item.id,
            url: item.url,
          })),
        );
        const filteredVideoData: {
          videos: Array<{ id: number | string }>;
        } = await getResponseFromAzureOpenAi(filterVideosPrompt);
        frame.videos = frame.videos.filter((daum: IPexelsVideoData) => {
          const match = filteredVideoData?.videos.some(
            (item) => item.id.toString() === daum.id.toString(),
          );
          return match;
        });
      } else {
        /* frame.images = await getImagesFromUnsplash(
          frame.image_keywords?.join(',') ?? '',
        );
        frame.images = await getImagesFromFreepik(frame.image_keywords?.join(',') ?? '',) */
        frame.images = await getImagesFromPexels(
          frame.video_content_description_involving_people ?? '',
        );
        const filterImagesPrompt = getPromptForFilteringImages({
          imageKeywords: [
            frame.video_content_description_involving_people ?? '',
          ], // frame.image_keywords ?? [],
          imagesData: frame.images,
        });
        const filteredImageData: {
          images: Array<Partial<IImageData>>;
        } = await getResponseFromAzureOpenAi(filterImagesPrompt);
        frame.images = frame.images.filter((daum: IImageData) => {
          const match = filteredImageData?.images.some(
            (item) => item.id === daum.id,
          );
          return match;
        });
      }
      // do not expose these in FE, secret sauce :p
      delete frame.image_keywords;
      delete frame.video_content_description_involving_people;
    }
  }

  if (videoData?.frames?.length) {
    if (!existsSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR)) {
      mkdirSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR, { recursive: true });
    }
    const audioFileName = `${Date.now()}-${videoData?.video_caption?.replace(
      / /g,
      '_',
    )}.mp3`;
    const audioFilePath = `${TMP_BANNERBOT_AUDIO_UPLOAD_DIR}/${audioFileName}`;
    let scriptDurations: Array<{
      script: string;
      duration_in_ms: number;
    }> = [];
    if (params.google_tts) {
      scriptDurations = await googleTts(
        videoData.frames.map((item) => item.script_text),
        audioFilePath,
      );
    } else {
      scriptDurations = await elevenLabsTts(
        videoData.frames.map((item) => item.script_text),
        audioFilePath,
      );
    }
    videoData.frames.forEach((frame, index) => {
      frame.duration = scriptDurations[index].duration_in_ms;
    });
    videoData.total_duration = scriptDurations.reduce((acc, current) => {
      return acc + current.duration_in_ms;
    }, 0);

    videoData.audio_url = await uploadFileToS3(
      BANNERBOT_S3_PUBLIC_BUCKET_NAME,
      audioFilePath,
      `${S3_BANNERBOT_VIDEO_MAKER_AUDIO_UPLOAD_DIR}/${audioFileName}`,
    );
    await fsUnlink(audioFilePath);
  }
  return videoData;
};

export const getSingleProject = async (
  uid: string,
  id: string,
): Promise<DocumentData | null> => {
  const snapshot = await bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.PROJECTS)
    .where('uid', '==', `${uid}`)
    .where('id', '==', id)
    .get();
  return snapshot.docs[0] ?? null;
};

export const updateBannerbotProject = async (
  firestoreDocId: string,
  project: Partial<IBannerbotProject>,
): Promise<void> => {
  await bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.PROJECTS)
    .doc(firestoreDocId)
    .set(project, {
      // caution: shallow merge only
      merge: true,
    });
};

export const addTemplatizedVideo = async (
  videoDetails: ITemplatizedVideo,
): Promise<string> => {
  const doc = bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.TEMPLATIZED_VIDEOS)
    .doc();

  videoDetails.id = doc.id;
  await doc.set(videoDetails);
  return doc.id;
};

export const updateTemplatizedVideo = async (
  templatizedVideoId: string,
  data: Partial<ITemplatizedVideo>,
): Promise<void> => {
  await bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.TEMPLATIZED_VIDEOS)
    .doc(templatizedVideoId)
    .set(data, { merge: true });
};

export async function isDailyLimitReached(
  userId: string,
  limitType: 'fp_video' | 'ai_ad_banner',
): Promise<{ isLimitReached: boolean; doesUserhaveSubscription: boolean }> {
  // first get the subscription details of the user
  const { limit: dailyLimit, doesUserhaveSubscription } =
    await getUserDailyLimit(userId, limitType);

  // const DAILY_LIMIT = 10;
  // Get today's start and end timestamps (UTC or your server's timezone)

  const usageCount =
    limitType === 'fp_video'
      ? await getUsersTodaysTemplatizedVideoUsage(userId)
      : await getUsersTodaysAiAdBannerUsage(userId);

  return { isLimitReached: usageCount >= dailyLimit, doesUserhaveSubscription };
}

async function getUsersTodaysTemplatizedVideoUsage(
  userId: string,
): Promise<number> {
  const now = new Date();
  const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const endOfDay = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate() + 1,
  );
  const snapshot = await bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.TEMPLATIZED_VIDEOS)
    .where('uid', '==', userId)
    .where('created_at', '>=', startOfDay)
    .where('created_at', '<', endOfDay)
    .count()
    .get();

  const usageCount = snapshot.data().count;
  return usageCount;
}

async function getUsersTodaysAiAdBannerUsage(userId: string): Promise<number> {
  const now = new Date();
  const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const endOfDay = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate() + 1,
  );
  const snapshot = await bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.AI_AD_BANNERS)
    .where('uid', '==', userId)
    .where('created_at', '>=', startOfDay)
    .where('created_at', '<', endOfDay)
    .count()
    .get();

  const usageCount = snapshot.data().count;
  return usageCount;
}

export const getUserDailyLimit = async (
  uid: string,
  limitType: 'fp_video' | 'ai_ad_banner',
): Promise<{ limit: number; doesUserhaveSubscription: boolean }> => {
  // get the active subscription from the userSubscription table and from there get the subscription_plan_id
  // then get the plan details which will have the respective limit based on limitType
  // if the user doesn't have any subscription then the default limit is applied

  const userSubscriptionData = await getUserActiveSubscription(uid);

  if (userSubscriptionData) {
    const subscriptionPlanId = userSubscriptionData.subscription_plan_id;

    // Fetch the subscription plan details
    const planSnapshot = await bannerbotDb
      .collection(BANNERBOT_FIRESTORE_COLLECTIONS.SUBSCRIPTION_PLANS)
      .doc(subscriptionPlanId)
      .get();

    if (planSnapshot.exists) {
      const subscriptionPlanData = planSnapshot.data() as ISubscriptionPlan;

      const limit =
        limitType === 'fp_video'
          ? Number(subscriptionPlanData.features.max_templatized_videos_per_day)
          : Number(subscriptionPlanData.features.max_ai_banners_per_day);

      return {
        limit,
        doesUserhaveSubscription: true,
      };
    }
  }

  // Default limits if no active subscription is found
  const defaultLimit = limitType === 'fp_video' ? 2 : 1;

  return {
    limit: defaultLimit,
    doesUserhaveSubscription: false,
  };
};

export const createStripeCheckoutSession = async (
  user: IAuthUser,
  params: { price_id: string; success_url: string; cancel_url: string },
): Promise<{ session_id: string; url: string | null }> => {
  if (!user.email) {
    throw new ValidationError(400, 'email id of user is not present');
  }

  try {
    // const userSubscriptionData = await getuserActiveSubscription(user.uid);

    const subscriptionPlanSnapshot = await bannerbotDb
      .collection(BANNERBOT_FIRESTORE_COLLECTIONS.SUBSCRIPTION_PLANS)
      .where('stripe_price_id', '==', params.price_id)
      .get();

    if (subscriptionPlanSnapshot.size === 0) {
      throw new Error(
        `No Subscription Plan with stripe_price_id=${params.price_id} exists`,
      );
    }

    const subscriptionPlanData =
      subscriptionPlanSnapshot.docs[0].data() as ISubscriptionPlan;

    let stripeCustomerId = '';
    // check if customer already exists in stripe
    const existingCustomers = await stripe.customers.list({
      email: user.email,
      limit: 1, // since email is unique so only 1 customer
    });

    if (existingCustomers.data.length > 0) {
      stripeCustomerId = existingCustomers.data[0].id;
    } else {
      // create a stripe customer
      const customer = await stripe.customers.create({
        name: user.name,
        email: user.email,
        metadata: {
          uid: user.uid,
        },
      });

      stripeCustomerId = customer.id;
    }

    // create a stripe subscription checkout session
    const session = await stripe.checkout.sessions.create({
      customer: stripeCustomerId,
      line_items: [
        {
          price: params.price_id,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${params.success_url}?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: params.cancel_url,
      metadata: {
        uid: user.uid,
        source: 'DESIGNEASY', // to distinguish subscritption source
      },
      custom_text: {
        submit: {
          message:
            'DesignEasy is a proud subsidiary of GrowEasy. Unlock premium features with a paid plan and elevate your creativity to the next level.',
        },
      },
    });

    const userSubscriptionDoc = bannerbotDb
      .collection(BANNERBOT_FIRESTORE_COLLECTIONS.USER_SUBSCRIPTIONS)
      .doc();

    const data: IUserSubscription = {
      id: userSubscriptionDoc.id,
      uid: user.uid,
      source: UserSubscriptionSource.STRIPE,
      stripe_customer_id: session.customer as string,
      subscription_plan_id: subscriptionPlanData.id,
      stripe_subscription_id: '',
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
    };

    await userSubscriptionDoc.set(data);

    return {
      session_id: session.id,
      url: session.url,
    };
  } catch (error) {
    logger.error(error);
    throw new Error('Error in creating checkout session');
  }
};

export const handleCheckoutSessionCompleted = async (
  session: Stripe.Checkout.Session,
): Promise<void> => {
  try {
    const uid = session.metadata?.uid;
    if (!uid)
      throw new Error(
        `metadata.uid missing in metadata of session with id=${session.id}`,
      );

    const subscriptionId = session.subscription as string;

    if (!subscriptionId) {
      throw new Error(
        `no subscription was created for session with id=${session.id}`,
      );
    }

    const userSubscriptionSnapshot = await bannerbotDb
      .collection(BANNERBOT_FIRESTORE_COLLECTIONS.USER_SUBSCRIPTIONS)
      .where('uid', '==', uid)
      .where('stripe_subscription_id', '==', '')
      .orderBy('updated_at', 'desc')
      .limit(1)
      .get();

    if (userSubscriptionSnapshot.size === 0)
      throw new Error('something went wrong');

    const userSubscriptionRef = userSubscriptionSnapshot.docs[0].ref;

    await userSubscriptionRef.update({
      stripe_subscription_id: subscriptionId,
      updated_at: Timestamp.now(),
    });
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const verifyStripeCheckout = async (
  sessionId: string,
): Promise<{ message: string }> => {
  // retrieve the session from stripe
  try {
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    // check if the payment was successful
    if (session.payment_status === 'paid' && session.status === 'complete') {
      // process the successful checkout
      await handleCheckoutSessionCompleted(session);

      return { message: 'Subscription activated successfully' };
    } else {
      throw new ValidationError(
        400,
        'Payment not completed or session not valid',
      );
    }
  } catch (error) {
    logger.error(error);
    if (error instanceof ValidationError) throw error;
    else throw new Error('Failed to verify checkout session');
  }
};

export const getUserSubscription = async (
  user: IAuthUser,
): Promise<
  | (ISubscriptionPlan & {
      status: string;
      source: UserSubscriptionSource;
      cancel_at_period_end?: boolean;
      current_period_start?: number;
      current_period_end?: number;
      stripe_subscription_id?: string;
      templatized_video_left: number;
      ai_ad_banner_left: number;
    })
  | undefined
> => {
  try {
    const uid = user.uid;

    // Fetch active subscriptions for the user
    const userSubscriptionData = await getUserActiveSubscription(uid);
    if (!userSubscriptionData) return;

    // Fetch the subscription plan details
    const planSnapshot = await bannerbotDb
      .collection(BANNERBOT_FIRESTORE_COLLECTIONS.SUBSCRIPTION_PLANS)
      .doc(userSubscriptionData.subscription_plan_id)
      .get();

    if (!planSnapshot.exists) {
      throw new Error(
        `The plan with id ${userSubscriptionData.subscription_plan_id} doesn't exist for the userSubscriptionId ${userSubscriptionData.id}`,
      );
    }

    const subscriptionPlanData = planSnapshot.data() as ISubscriptionPlan;

    const templatizedVideoUsage =
      await getUsersTodaysTemplatizedVideoUsage(uid);
    const aiAdBannerUsage = await getUsersTodaysAiAdBannerUsage(uid);

    const maxTemplatizedVideoUsage =
      Number(subscriptionPlanData.features.max_templatized_videos_per_day) ?? 0;
    const maxAiAdBannerUsage =
      Number(subscriptionPlanData.features.max_ai_banners_per_day) ?? 0;
    const templatizedVideoLeft =
      maxTemplatizedVideoUsage <= templatizedVideoUsage
        ? 0
        : maxTemplatizedVideoUsage - templatizedVideoUsage;
    const aiAdBannerLeft =
      maxAiAdBannerUsage <= aiAdBannerUsage
        ? 0
        : maxAiAdBannerUsage - aiAdBannerUsage;

    if (userSubscriptionData.stripe_subscription_id) {
      // get detail about the subscrition from stripe sdk using userSubscriptionData.stripe_subscription_id
      const subscription = await stripe.subscriptions.retrieve(
        userSubscriptionData.stripe_subscription_id,
      );

      return {
        ...subscriptionPlanData,
        status: subscription.status,
        source: userSubscriptionData.source ?? UserSubscriptionSource.STRIPE,
        cancel_at_period_end: subscription.cancel_at_period_end,
        current_period_start: subscription.current_period_start,
        current_period_end: subscription.current_period_end,
        stripe_subscription_id: subscription.id,
        templatized_video_left: templatizedVideoLeft,
        ai_ad_banner_left: aiAdBannerLeft,
      };
    } else if (userSubscriptionData.appsumo_coupon_code) {
      return {
        ...subscriptionPlanData,
        status: 'active',
        source: userSubscriptionData.source,
        templatized_video_left: templatizedVideoLeft,
        ai_ad_banner_left: aiAdBannerLeft,
      };
    }
  } catch (error) {
    logger.error(error);
    throw new Error('Failed to fetch user subscriptions.');
  }
};

export const getUserActiveSubscription = async (
  uid: string,
): Promise<IUserSubscription | undefined> => {
  const collection = bannerbotDb.collection(
    BANNERBOT_FIRESTORE_COLLECTIONS.USER_SUBSCRIPTIONS,
  );

  // Query for active Stripe subscriptions
  const stripeQuery = collection
    .where('uid', '==', uid)
    .where('stripe_subscription_id', '!=', '')
    .orderBy('updated_at', 'desc')
    .limit(1);

  // Query for active AppSumo subscriptions
  const appsumoQuery = collection
    .where('uid', '==', uid)
    .where('appsumo_coupon_code', '!=', '')
    .orderBy('updated_at', 'desc')
    .limit(1);

  const [stripeSnapshot, appsumoSnapshot] = await Promise.all([
    stripeQuery.get(),
    appsumoQuery.get(),
  ]);

  // if the user has acitve appsumo subscription return that
  if (appsumoSnapshot.size !== 0) {
    return appsumoSnapshot.docs[0].data() as IUserSubscription;
  }

  // else check for the stripe based subsctiption
  if (stripeSnapshot.size === 0) return;

  const userSubscriptionData =
    stripeSnapshot.docs[0].data() as IUserSubscription;

  if (!userSubscriptionData.stripe_subscription_id) return;

  const subscription = await stripe.subscriptions.retrieve(
    userSubscriptionData.stripe_subscription_id,
  );

  if (subscription.status === 'active') {
    return userSubscriptionData;
  }
};

export const cancelUserSubscription = async (
  subscriptionId: string,
): Promise<void> => {
  try {
    // set the cancel_at_period_end true in stripe
    await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });
  } catch (error) {
    logger.error(error);
    throw new Error('Something went wrong');
  }
};

/**
 * if the subscription status is canceled it can't be reactivated
 * only subscription with status active and cancel_at_period_end = true can be reactivated
 * @param subscriptionId
 */
export const reactivateUserSubscription = async (
  subscriptionId: string,
): Promise<void> => {
  try {
    // Reactivate the subscription in Stripe by setting cancel_at_period_end to false
    await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });
  } catch (error) {
    logger.error(error);
    throw new Error('Failed to reactivate the subscription');
  }
};

export const getProjects = async (
  user?: IAuthUser,
  projectId?: string,
): Promise<IBannerbotProject[]> => {
  try {
    const collectionQuery = bannerbotDb.collection(
      BANNERBOT_FIRESTORE_COLLECTIONS.PROJECTS,
    );

    let query =
      isDesignEasyAdmin(user) && projectId
        ? collectionQuery
        : collectionQuery.where('uid', '==', `${user?.uid}`);

    query = query.orderBy('updated_at', 'desc').limit(50);

    if (projectId) {
      query = query.where('id', '==', projectId);
    }
    const snapshot = await query.get();

    const projects: IBannerbotProject[] = [];
    snapshot.forEach((doc) => {
      projects.push(doc.data() as IBannerbotProject);
    });
    return projects;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getAdminProjects = async (
  queryParams: Record<string, any>,
  user?: IAuthUser,
  projectId?: string,
): Promise<{
  data: Array<{
    project: IBannerbotProject;
    userDetail: {
      uid: string;
      email: string | undefined;
      name: string | undefined;
    };
  }>;
  last_cursor_id: string;
}> => {
  const {
    last_cursor_id: lastCursorId,
    status = BannerbotProjectStatus.COMPLETE,
    limit: tempLimit = `50`,
  } = queryParams;
  try {
    const limit = parseInt(tempLimit);
    const lastDocRef = lastCursorId
      ? await bannerbotDb
          .collection(BANNERBOT_FIRESTORE_COLLECTIONS.PROJECTS)
          .doc(lastCursorId)
          .get()
      : null;

    let query = bannerbotDb
      .collection(BANNERBOT_FIRESTORE_COLLECTIONS.PROJECTS)
      .where('status', '==', status)
      .orderBy('updated_at', 'desc');

    if (projectId) {
      query = query.where('id', '==', projectId);
    }

    if (lastDocRef) {
      query = query.startAfter(lastDocRef);
    }

    // limit + 1 is done to check if next page is available or not
    // suppose with a limit of 15 the snapshot will fetch 16, if is is able to fetch 16
    // this means the next is avaiable and, if not then, no next is there so in that case
    // set the "lastProjectRef" to empty text
    const snapshot = await query.limit(limit + 1).get();

    const projects: IBannerbotProject[] = [];
    const userIds = new Set<string>();

    let index = 0;
    snapshot.forEach((doc) => {
      if (index !== limit) {
        projects.push(doc.data() as IBannerbotProject);
        const project = doc.data() as IBannerbotProject;
        userIds.add(project.uid);
      }
      index++;
    });

    const userDetails = await bannerbotAuth.getUsers(
      Array.from(userIds).map((item) => ({ uid: item })),
    );

    // now map project and userIds

    const data = projects.map((item) => {
      const userData = userDetails.users.find((u) => u.uid === item.uid);

      return {
        project: item,
        userDetail: {
          uid: userData?.uid ?? '',
          email: userData?.email,
          name: userData?.displayName,
        },
      };
    });

    const lastProjectRef =
      snapshot.docs.length === limit + 1
        ? snapshot.docs[snapshot.docs.length - 2]
        : undefined;
    return { data, last_cursor_id: lastProjectRef?.id ?? '' };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getAiAdBanners = async ({
  last_cursor_id: lastCursorId,
  limit: tempLimit = 24,
}: {
  limit?: number | string;
  last_cursor_id?: string;
}): Promise<{
  data: Array<{
    ai_banner: IAiAdBanner;
    user_details: {
      uid: string;
      email: string | undefined;
      name: string | undefined;
    };
  }>;
  last_cursor_id: string;
}> => {
  const limit = typeof tempLimit !== 'number' ? parseInt(tempLimit) : tempLimit;

  const lastDocRef = lastCursorId
    ? await bannerbotDb
        .collection(BANNERBOT_FIRESTORE_COLLECTIONS.AI_AD_BANNERS)
        .doc(lastCursorId)
        .get()
    : null;

  let query = bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.AI_AD_BANNERS)
    .orderBy('updated_at', 'desc');

  if (lastDocRef) {
    query = query.startAfter(lastDocRef);
  }

  const snapshot = await query.limit(limit + 1).get();

  const projects: IAiAdBanner[] = [];
  const userIds = new Set<string>();

  let index = 0;
  snapshot.forEach((doc) => {
    if (index !== limit) {
      projects.push(doc.data() as IAiAdBanner);
      const project = doc.data() as IAiAdBanner;
      userIds.add(project.uid);
    }
    index++;
  });

  const userDetails = await bannerbotAuth.getUsers(
    Array.from(userIds).map((item) => ({ uid: item })),
  );

  const data = projects.map((item) => {
    const userData = userDetails.users.find((u) => u.uid === item.uid);

    return {
      ai_banner: item,
      user_details: {
        uid: userData?.uid ?? '',
        email: userData?.email,
        name: userData?.displayName,
      },
    };
  });

  const lastProjectRef =
    snapshot.docs.length === limit + 1
      ? snapshot.docs[snapshot.docs.length - 2]
      : undefined;
  return { data, last_cursor_id: lastProjectRef?.id ?? '' };
};

export const createOrUpdateProject = async (
  body: Partial<IBannerbotProject>,
  user?: IAuthUser,
): Promise<IBannerbotProject | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    // merge data if project id is available
    // else create new project
    if (body?.id) {
      // Some fields cannot be updated from FE
      delete body.created_at;
      delete body.updated_at;
      delete body.uid;

      // get the doc
      const doc = await getSingleProject(user.uid, body.id);
      if (doc?.id) {
        /* if (body.status !== BannerbotProjectStatus.DRAFT) {
          throw new ValidationError(
            400,
            'Only project in Draft can be updated',
          );
        } */
        const project = {
          ...(body as IBannerbotProject),
          updated_at: Timestamp.now(),
        };
        // merge
        await updateBannerbotProject(doc.id, project);

        const updatedDoc = await bannerbotDb
          .collection(BANNERBOT_FIRESTORE_COLLECTIONS.PROJECTS)
          .doc(doc.id)
          .get();
        const updatedData = (updatedDoc.data() as IBannerbotProject) ?? null;
        return updatedData;
      } else {
        throw new ValidationError(400, 'This project does not exist');
      }
    } else {
      // create
      const docRef = bannerbotDb
        .collection(BANNERBOT_FIRESTORE_COLLECTIONS.PROJECTS)
        .doc();

      const project: Partial<IBannerbotProject> = {
        ...body,
        id: docRef.id,
        status: BannerbotProjectStatus.DRAFT,
        created_at: Timestamp.now(),
        updated_at: Timestamp.now(),
        uid: user.uid,
      };
      await docRef.set(project);
      const doc = await docRef.get();

      const updatedData = (doc.data() as IBannerbotProject) ?? null;
      return updatedData;
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const populateKeyBenefits = async (
  body: Partial<IBannerbotProject>,
  user?: IAuthUser,
): Promise<{
  key_benefits: string[];
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  if (!body?.id) {
    throw new ValidationError(400, 'Missing id');
  }
  if (!body?.details?.business_details?.product_or_service_description) {
    throw new ValidationError(400, 'Missing product_or_service_description');
  }
  try {
    const prompt = getKeyBenefitsPrompt({
      product_or_service_description:
        body.details.business_details.product_or_service_description,
    });
    const openAiResponse = (await getResponseFromAzureOpenAi(prompt)) as {
      key_benefits: string[];
    };
    // also save to project in db
    const projectDoc = await getSingleProject(user.uid, body.id);
    if (!projectDoc?.id) {
      throw new ValidationError(400, 'Missing project details');
    }
    const projectDetails = projectDoc.data() as IBannerbotProject;
    projectDetails.details = {
      ...projectDetails.details,
      ai_key_benefits: openAiResponse.key_benefits,
    };
    await updateBannerbotProject(body.id, {
      details: projectDetails.details,
    });

    return openAiResponse;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getAdBanners = async (
  body: Partial<IBannerbotProject>,
  user?: IAuthUser,
): Promise<{ templates: IBannerTemplate[]; images: IImageData[] }> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const params = {
    product_or_service_description:
      body?.details?.business_details?.product_or_service_description,
    key_benefits: body?.details?.business_details?.key_benefits,
  };
  if (!params?.key_benefits) {
    throw new ValidationError(400, 'Missing key_benefits');
  }
  if (!params?.product_or_service_description) {
    throw new ValidationError(400, 'Missing product_or_service_description');
  }
  try {
    let banners: IAdCreativeData[] = [];
    const prompt = getAdBannersPrompt(params);
    const openAiResponse = await getResponseFromAzureOpenAi(prompt);
    if (openAiResponse?.banners?.length) {
      banners = openAiResponse.banners;
    }
    logger.debug(banners);
    let images = [];
    const productImages: IImageData[] = [];

    // include product images if found
    if (body?.details?.business_details?.product_images?.length) {
      body.details.business_details.product_images.forEach((item, index) => {
        productImages.push({
          author_name: '',
          download_location: '',
          alt_description: 'Product image uploaded by user',
          author_profile_url: '',
          id: `product_image_${index}`,
          thumbnail_url: getTransformedImageUrl(item.url, {
            width: 200,
            height: 200,
          }),
          url: item.url,
          likes: 0,
          height: item.height,
          width: item.width,
        });
      });
    }

    const [freepikImages1, freepikImages2, freepikImages3] = await Promise.all([
      getImagesFromFreepik(banners[0].creative_image_keywords?.join(',')),
      getImagesFromFreepik(banners[1].creative_image_keywords?.join(',')),
      getImagesFromFreepik(banners[2].creative_image_keywords?.join(',')),
    ]);
    images = [...freepikImages1, ...freepikImages2, ...freepikImages3];

    // fallback just in case
    if (!images.length) {
      const [unsplashImages1, pexelsImages, unsplashImages2] =
        await Promise.all([
          getImagesFromUnsplash(banners[0].creative_image_keywords?.join(',')),
          getImagesFromPexels(banners[0].creative_image_keywords?.join(',')),
          getImagesFromUnsplash(banners[1].creative_image_keywords?.join(',')),
        ]);
      images = [...unsplashImages1, ...pexelsImages, ...unsplashImages2];
    }

    images = [...productImages, ...images];

    const squareBusinessLogo =
      body?.details?.business_details?.business_logo?.square;
    const businessName = body?.details?.business_details?.business_name;

    const templates: IBannerTemplate[] = [];
    [...templatesCollection1, ...templatesCollection2, ...templatesCollection3]
      .reverse()
      // .filter((item) => ['square', 'portrait'].includes(item.size))
      .forEach((template) => {
        // check for logo dedicated templates
        let useTemplate = true;
        if (template.withLogoOnly) {
          if (!squareBusinessLogo?.url || !businessName) {
            useTemplate = false;
          }
        }
        if (useTemplate) {
          templates.push(deepCopy(template) as IBannerTemplate);
        }
      });
    updateVariablesInTemplates({
      templates,
      banners,
      images,
      businessLogo: squareBusinessLogo,
      businessName,
    });
    return {
      images,
      templates,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const createOrUpdateUserProfile = async (
  body: Partial<IBannerbotUserProfile>,
  user?: IAuthUser,
): Promise<IBannerbotUserProfile | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  // Some fields cannot be updated from FE
  delete body.uid;
  delete body.created_at;
  delete body.email;
  delete body.name;
  try {
    const documentRef = bannerbotDb
      .collection(BANNERBOT_FIRESTORE_COLLECTIONS.USER_PROFILE)
      .doc(user.uid);
    const documentSnanpshot = await documentRef.get();
    const profileDetails = documentSnanpshot.data();
    // merge data if profile info already exists
    // else create new record
    if (profileDetails) {
      await documentRef.set(
        {
          ...body,
          updated_at: Timestamp.now(),
        },
        {
          merge: true, // caution: shallow merge only
        },
      );
      const updatedDoc = await documentRef.get();
      return (updatedDoc.data() as IBannerbotUserProfile) ?? null;
    } else {
      if (!body.acquisition_source) {
        throw new ValidationError(400, 'Missing required fields');
      }
      // create
      const data: Partial<IBannerbotUserProfile> = {
        ...body,
        uid: user.uid,
        email: user.email,
        name: user.name ?? '', // Apple signin might have no name
        created_at: Timestamp.now(),
        updated_at: Timestamp.now(),
      };

      await documentRef.set(data);
      return data as IBannerbotUserProfile;
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

const addNewAiAdBannerData = async (params: {
  projectId: string;
  uid: string;
  s3_url: string;
}): Promise<void> => {
  const aiBannerDoc = bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.AI_AD_BANNERS)
    .doc();

  await aiBannerDoc.set({
    id: aiBannerDoc.id,
    uid: params.uid,
    project_id: params.projectId,
    banner_url: params.s3_url,
    created_at: Timestamp.now(),
    updated_at: Timestamp.now(),
  });
};

const getUspAndBannerElementDetails = async (
  project: IBannerbotProject,
  user: IAuthUser,
): Promise<{
  banners: Array<{
    creative_title: string;
    call_out: string;
    call_to_action: string;
    focused_usp: string;
    imagery: string;
  }>;
}> => {
  try {
    if (!project.details?.business_details) {
      throw new ValidationError(400, 'This project has missing details');
    }

    const prompt = getUspAndBannerElementDetailsPrompt({
      business_details: project?.details?.business_details ?? {},
    });

    const openAiResponse = await getResponseFromAzureOpenAi(prompt);
    return openAiResponse;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const generateAiAdBanner = async (
  params: {
    project: IBannerbotProject;
    size: 'square' | 'portrait' | 'landscape';
  },
  user: IAuthUser,
): Promise<{
  s3_url: string;
  width: number;
  height: number;
}> => {
  const { project, size } = params;

  if (!['square', 'portrait', 'landscape'].includes(size)) {
    throw new ValidationError(
      400,
      'size must be one of square or portrait or landscape',
    );
  }

  const bannerElementsDetails = await getUspAndBannerElementDetails(
    project,
    user,
  );

  const bannerDetails = bannerElementsDetails.banners[0];

  if (
    !bannerDetails.creative_title ||
    !bannerDetails.call_out ||
    !bannerDetails.call_to_action ||
    !bannerDetails.imagery
  ) {
    throw new ValidationError(400, 'Incomplete banner_details');
  }

  try {
    if (!project.details?.business_details) {
      throw new ValidationError(400, 'This project has missing details');
    }
    const imageGenPromptGeneratorPrompt = getAiBannerImageGenPrompt({
      business_details: project.details?.business_details ?? {},
      banner_details: bannerDetails,
    });
    const openAiResponse = await getResponseFromAzureOpenAi(
      imageGenPromptGeneratorPrompt,
    );
    const imageGenPrompt = openAiResponse.prompt;

    logger.debug(imageGenPrompt);
    const aiAdBannerImageData = await generateAiBannerImage({
      prompt: imageGenPrompt,
      aspect_ratio:
        size === 'square' ? '1x1' : size === 'landscape' ? '16x9' : '9x16',
    });

    const fileName = `${Date.now()}-${user.uid}-${project.id}.png`;
    // write this image to local file
    const aiBannerImagePath = await saveFileToDisk({
      fileName,
      url: aiAdBannerImageData.data?.[0]?.url,
      directory: TMP_ADIMAGES_UPLOAD_DIR,
    });

    const s3Url = await uploadFileToS3(
      BANNERBOT_S3_PUBLIC_BUCKET_NAME,
      aiBannerImagePath,
      `${S3_AI_BANNERS_DIR}/${fileName}`,
    );

    if (!s3Url) {
      throw new Error('Failed to upload image to S3');
    }

    const [width, height] = aiAdBannerImageData.data[0].resolution.split('x');

    return {
      s3_url: s3Url,
      width: Number(width),
      height: Number(height),
    };
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const generateAiAdBannerAndSaveToProject = async (
  params: {
    project_id: string;
    size: 'square' | 'portrait' | 'landscape';
  },
  user: IAuthUser,
): Promise<{
  s3_url: string;
  width: number;
  height: number;
}> => {
  const { project_id: projectId, size } = params;

  // first check the limit of the user

  const { doesUserhaveSubscription, isLimitReached } =
    await isDailyLimitReached(user.uid, 'ai_ad_banner');

  if (isLimitReached && doesUserhaveSubscription) {
    throw new ValidationError(
      402,
      'You have exhausted your daily credits. Please try again tomorrow.',
    );
  } else if (isLimitReached) {
    throw new ValidationError(
      403,
      'You have reached your daily limit. Please subscribe to continue.',
    );
  }

  const projectDoc = await getSingleProject(user.uid, projectId);

  if (!projectDoc?.id) {
    throw new ValidationError(400, 'Missing project details');
  }

  const projectDetails = projectDoc.data() as IBannerbotProject;

  const {
    s3_url: url,
    height,
    width,
  } = await generateAiAdBanner({ project: projectDetails, size }, user);

  const newAsset = {
    height,
    url,
    width,
    created_at: Date.now(),
  };

  await bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.PROJECTS)
    .doc(projectDoc.id)
    .update({
      updated_at: Timestamp.now(),
      [`details.saved_assets.${BannerbotAdType.AI_BANNER}`]:
        FieldValue.arrayUnion(newAsset),
    });

  void addNewAiAdBannerData({ projectId, s3_url: url, uid: user.uid });

  return {
    height,
    s3_url: url,
    width,
  };
};
