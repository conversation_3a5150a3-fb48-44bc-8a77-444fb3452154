import { Timestamp } from 'firebase-admin/firestore';
import {
  FIRESTORE_COLLECTIONS,
  GROWEASY_WHATSAPP_ID,
  GROWEASY_WHATSAPP_NO,
} from '../constants';
import { db } from '../modules/firebase_admin_sdk';
import {
  type ICtwaChatMessage,
  type IGrowEasyWaConversationDetails,
  type IWaButtonMessage,
  WaMessageType,
} from '../types/whatsapp';
import { sendWhatsappMessage } from './util';
import logger from '../modules/logger';
import { getUserProfile } from './user_controller';
import { type IUserProfile } from '../types';
import { appendDataToSheet } from '../modules/google_sheet';

export const sendWhatsAppComm = async (
  payload: ICtwaChatMessage,
  userProfile: IUserProfile,
): Promise<void> => {
  // do not process for whitelisted partners
  if (userProfile?.partner) {
    logger.info(
      `Did not send WhatsApp comm to ${payload?.to} for partner ${userProfile?.partner}`,
    );
    return;
  }
  logger.info(`Sending WhatsApp comm to ${payload?.to}`, userProfile);
  if (!payload?.to) {
    return;
  }
  const userWhatsappId = payload.to;
  const sentMessageId = await sendWhatsappMessage(
    GROWEASY_WHATSAPP_ID,
    payload,
  );
  if (sentMessageId) {
    // check if conversation details already exist for this user
    const conversationDocRef = db
      .collection(`wa_${GROWEASY_WHATSAPP_NO}`)
      .doc(userWhatsappId);
    const conversationDoc = await conversationDocRef.get();
    const conversationDetails = conversationDoc.data();
    if (conversationDetails) {
      // update
      await conversationDocRef.set(
        {
          updated_at: Timestamp.now(),
          last_message: payload,
        },
        {
          merge: true,
        },
      );
    } else {
      // create
      const createPayload: IGrowEasyWaConversationDetails = {
        updated_at: Timestamp.now(),
        created_at: Timestamp.now(),
        last_message: payload,
        mobile: userProfile.mobile ?? '',
        profile_name: userProfile.name,
      };
      await conversationDocRef.set(createPayload);
    }
    // store in messages collection
    await conversationDocRef
      .collection(`messages`)
      .doc(sentMessageId)
      .set({
        ...payload,
        time: Timestamp.now(),
      });
  }
};

const getUserProfileForSendingComms = async (
  uid: string,
): Promise<IUserProfile | null> => {
  const userProfile = await getUserProfile({
    uid,
    name: '',
  });

  // check if WhatsApp is opted in
  if (!userProfile?.whatsapp_opt_in) {
    logger.info(
      `User ${userProfile?.mobile} with uid ${uid} has not opted-in for WhatsApp communication`,
    );
    return null;
  }
  return userProfile;
};

const getUserProfileFromWhatsAppId = async (
  whatsAppId: string,
): Promise<IUserProfile | null> => {
  try {
    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.USER_PROFILE) // update this if your user collection name differs
      .where('whatsapp_id', '==', whatsAppId)
      .orderBy('updated_at', 'desc')
      .limit(1)
      .get();

    if (snapshot.empty) {
      logger.info(`No user found with WhatsApp ID: ${whatsAppId}`);
      return null;
    }

    const userDoc = snapshot.docs[0];
    const userProfile = userDoc.data() as IUserProfile;

    return userProfile;
  } catch (error) {
    logger.error(`Error fetching user by WhatsApp ID ${whatsAppId}:`, error);
    return null;
  }
};

export const sendWelcomeFollowupComm = async (params: {
  uid: string;
}): Promise<void> => {
  try {
    const { uid } = params;

    const userProfile = await getUserProfileForSendingComms(uid);
    if (!userProfile) {
      return;
    }

    const userMobile = `${userProfile.mobile_dial_code}${userProfile.mobile}`;
    const userWhatsappId = userMobile.replace('+', '');

    // until user doesn't engage into conversation (24 hours window), we can only send templates
    const replyPayload: ICtwaChatMessage = {
      messaging_product: 'whatsapp',
      to: userWhatsappId,
      /* type: WaMessageType.interactive,
      interactive: {
        type: 'button',
        header: {
          type: 'text',
          text: 'Welcome to GrowEasy!',
        },
        body: {
          text: `Hi ${userProfile.name},\n\nWe help businesses like yours get leads effortlessly. Can we share a quick demo on how to launch your first campaign?`,
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: `welcome_followup_yes|${uid}`,
                title: 'Yes, Please',
              },
            },
            {
              type: 'reply',
              reply: {
                id: `welcome_followup_no|${uid}`,
                title: 'No, Thanks',
              },
            },
          ],
        },
      }, */
      type: WaMessageType.template,
      template: {
        name: 'welcome_followup_v1',
        language: {
          code: 'en',
        },
        components: [
          {
            type: 'body',
            parameters: [
              {
                type: 'text',
                text: userProfile?.name ?? 'Guest', // ios users might have missing name
                parameter_name: 'name',
              },
            ],
          },
        ],
      },
    };

    await sendWhatsAppComm(replyPayload, userProfile);

    // Interactive message with 2 CTAs, Interested & Not interested
  } catch (error) {
    logger.error(error);
  }
};

export const sendWelcomeFollowupPositiveCtaComm = async (params: {
  whatsapp_id: string;
}): Promise<void> => {
  try {
    const userProfile = await getUserProfileFromWhatsAppId(params.whatsapp_id);
    if (!userProfile) {
      return;
    }

    // save this entry into google sheet
    await appendDataToSheet({
      sheetName: 'GrowEasyDemo',
      rowData: [
        [
          new Date().toDateString(),
          userProfile.email ?? '',
          `+${userProfile.whatsapp_id}`,
        ],
      ],
    });

    const userMobile = `${userProfile.mobile_dial_code}${userProfile.mobile}`;
    const userWhatsappId = userMobile.replace('+', '');

    const replyPayload: ICtwaChatMessage = {
      messaging_product: 'whatsapp',
      to: userWhatsappId,
      type: WaMessageType.text,
      text: {
        body: `Awesome, ${userProfile.name}! 🎉\n\nOur team will call you shortly to guide you through your first campaign setup. Let’s grow your business!`,
      },
    };

    await sendWhatsAppComm(replyPayload, userProfile);
  } catch (error) {
    logger.error(error);
  }
};

export const sendGenericNegativeCtaComm = async (params: {
  whatsapp_id: string;
}): Promise<void> => {
  try {
    const userProfile = await getUserProfileFromWhatsAppId(params.whatsapp_id);
    if (!userProfile) {
      return;
    }

    const userMobile = `${userProfile.mobile_dial_code}${userProfile.mobile}`;
    const userWhatsappId = userMobile.replace('+', '');

    const replyPayload: ICtwaChatMessage = {
      messaging_product: 'whatsapp',
      to: userWhatsappId,
      type: WaMessageType.text,
      text: {
        body: `Thanks, ${userProfile.name}. We’ve noted your response. You can start anytime on GrowEasy when you’re ready. 🚀`,
      },
    };

    await sendWhatsAppComm(replyPayload, userProfile);
  } catch (error) {
    logger.error(error);
  }
};

export const sendCampaignNotLaunchedCheckinComm = async (params: {
  uid: string;
}): Promise<void> => {
  try {
    const { uid } = params;

    const userProfile = await getUserProfileForSendingComms(uid);
    if (!userProfile) {
      return;
    }

    const userMobile = `${userProfile.mobile_dial_code}${userProfile.mobile}`;
    const userWhatsappId = userMobile.replace('+', '');

    // until user doesn't engage into conversation (24 hours window), we can only send templates
    const replyPayload: ICtwaChatMessage = {
      messaging_product: 'whatsapp',
      to: userWhatsappId,
      type: WaMessageType.template,
      template: {
        name: 'campaign_not_launched_checkin_v1',
        language: {
          code: 'en',
        },
        components: [
          {
            type: 'body',
            parameters: [
              {
                type: 'text',
                text: userProfile?.name ?? 'Guest', // ios users might have missing name
                parameter_name: 'name',
              },
            ],
          },
        ],
      },
    };
    await sendWhatsAppComm(replyPayload, userProfile);

    // Interactive message with 2 CTAs, Yes, I need Help & Not interested
  } catch (error) {
    logger.error(error);
  }
};

export const sendCampaignNotLaunchedCheckinPositiveCtaComm = async (params: {
  whatsapp_id: string;
}): Promise<void> => {
  try {
    const userProfile = await getUserProfileFromWhatsAppId(params.whatsapp_id);
    if (!userProfile) {
      return;
    }

    // save this entry into google sheet
    await appendDataToSheet({
      sheetName: 'CampaignHelpNeeded',
      rowData: [
        [
          new Date().toDateString(),
          userProfile.email ?? '',
          `+${userProfile.whatsapp_id}`,
        ],
      ],
    });

    const userMobile = `${userProfile.mobile_dial_code}${userProfile.mobile}`;
    const userWhatsappId = userMobile.replace('+', '');

    const replyPayload: ICtwaChatMessage = {
      messaging_product: 'whatsapp',
      to: userWhatsappId,
      type: WaMessageType.text,
      text: {
        body: `Thank you, ${userProfile.name}! 🎉\n\nOur team will call you shortly to help you in your campaign setup. Let’s grow your business!`,
      },
    };

    await sendWhatsAppComm(replyPayload, userProfile);
  } catch (error) {
    logger.error(error);
  }
};

export const handleButtonMessageReply = async (
  message: IWaButtonMessage,
): Promise<void> => {
  const buttonText = message?.button?.text;
  // caution, same button text in any other template will cause undesired behaviour
  if (buttonText?.includes('Yes, Please')) {
    await sendWelcomeFollowupPositiveCtaComm({ whatsapp_id: message.from });
  } else if (buttonText?.includes('No, Thanks')) {
    await sendGenericNegativeCtaComm({ whatsapp_id: message.from });
  }
  if (buttonText?.includes('Yes, I Need Help')) {
    await sendCampaignNotLaunchedCheckinPositiveCtaComm({
      whatsapp_id: message.from,
    });
  }
};
