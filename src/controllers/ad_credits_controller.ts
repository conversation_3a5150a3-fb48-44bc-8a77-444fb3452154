import { Timestamp } from 'firebase-admin/firestore';
import {
  FIRESTORE_COLLECTIONS,
  GROW_EASY_PLATFORM_FEE_PERCENTAGE,
} from '../constants';
import { db } from '../modules/firebase_admin_sdk';
import {
  AdPlatforms,
  type IAdCreditsBalance,
  type IAdCreditsTransaction,
} from '../types';
import {
  Currency,
  type ICampaign,
  type ICampaignInsightDetails,
} from '../types/campaign_details';
import { getCampaignInsights } from './meta_controller';
import {
  getCampaignCurrencyBudgetNode,
  getGoogleCampaignInsightDetails,
  getUserPerceivedCampaignSpendInInr,
} from './util';
import logger from '../modules/logger';
import { getAdCreditBalance } from './db_controller';
import { getCampaignReportingFromGoogle } from './google/google_controller';
import { getTiktokCampaignInsights } from './tiktok/tiktok_controller';

export const createAdCreditTransaction = async (
  uid: string,
  transaction: IAdCreditsTransaction,
): Promise<string> => {
  logger.info(`createTransaction for ${uid}`, transaction);
  const creditDetailsRef = db
    .collection(FIRESTORE_COLLECTIONS.AD_CREDITS)
    .doc(uid);
  // get balance
  const creditDetailsDoc = await creditDetailsRef.get();
  const creditDetails = (creditDetailsDoc.data() as IAdCreditsBalance) ?? {
    inr: 0,
    usd: 0,
    idr: 0,
    php: 0,
    thb: 0,
    vnd: 0,
    myr: 0,
    created_at: Timestamp.now(),
  };
  const key = transaction.currency.toLowerCase() as
    | 'usd'
    | 'inr'
    | 'idr'
    | 'php'
    | 'thb'
    | 'vnd'
    | 'myr';
  if (key in creditDetails) {
    const amount =
      transaction.type === 'CREDIT' ? transaction.value : -transaction.value;
    creditDetails[key] += amount;
  } else {
    // first time transaction for a given currency
    creditDetails[key] = transaction.value;
  }
  // update balance after checks
  if (creditDetails[key] < 0) {
    throw new Error('Updated balance cannot be negative');
  }
  // create transaction
  const docRef = await db
    .collection(FIRESTORE_COLLECTIONS.AD_CREDITS)
    .doc(uid)
    .collection('transactions')
    .add({
      ...transaction,
      created_at: Timestamp.now(),
    });
  // save updated balance
  await creditDetailsRef.set(
    {
      ...creditDetails,
      updated_at: Timestamp.now(),
    },
    {
      merge: true,
    },
  );

  // return transaction id
  return docRef.id;
};

export const convertRemainingBudgetToAdCredits = async (
  campaignDetails: ICampaign,
): Promise<IAdCreditsTransaction> => {
  const budgetAndScheduling = campaignDetails?.details?.budget_and_scheduling;
  const campaignCurrencyBudgetNode =
    getCampaignCurrencyBudgetNode(budgetAndScheduling);
  const platformFeePercentage = budgetAndScheduling?.platform_fee_percentage;

  let insights: {
    spend: string;
  } | null = null;
  // Meta
  if (campaignDetails.meta_id) {
    const campaignInsightsResponse = await getCampaignInsights({
      campaign_id: campaignDetails.meta_id,
    });
    insights = campaignInsightsResponse?.data
      ?.data?.[0] as ICampaignInsightDetails;
  } else if (campaignDetails?.google_ads_data?.campaign_resource) {
    // Google
    const insightsArr = await getCampaignReportingFromGoogle(
      campaignDetails.google_ads_data?.campaign_resource,
    );
    insights = getGoogleCampaignInsightDetails({ insightsArr });
  } else if (campaignDetails?.tiktok_ads_data?.campaign_id) {
    const campaignInsights = await getTiktokCampaignInsights({
      campaign_id: campaignDetails?.tiktok_ads_data?.campaign_id,
    });
    insights = {
      spend: campaignInsights?.metrics?.spend ?? '0',
    };
  }

  let spendInCampaignCurrency = 0;
  const currency = budgetAndScheduling?.currency ?? Currency.INR;
  const currencyConversionFactor = [Currency.IDR, Currency.VND].includes(
    currency,
  )
    ? 1
    : 100;

  if (campaignDetails.platform === AdPlatforms.TIKTOK) {
    spendInCampaignCurrency = getUserPerceivedCampaignSpendInInr(
      insights?.spend ?? '0',
      platformFeePercentage ?? GROW_EASY_PLATFORM_FEE_PERCENTAGE,
    );
  } else {
    const spendInInr = getUserPerceivedCampaignSpendInInr(
      insights?.spend ?? '0',
      platformFeePercentage ?? GROW_EASY_PLATFORM_FEE_PERCENTAGE,
    );

    spendInCampaignCurrency =
      spendInInr / (campaignCurrencyBudgetNode?.exchange_rate ?? 1);

    // Meta / Google will keep spending for some time even after campaign is paused, so deduct 2% of daily budget as adjustment
    spendInCampaignCurrency +=
      ((campaignCurrencyBudgetNode?.daily_budget ?? 0) /
        currencyConversionFactor) *
      0.0; // 0.02
  }

  let lifetimeBudgetInCampaignCurrency =
    campaignCurrencyBudgetNode?.lifetime_budget ?? 0;

  // convert from paise to INR or cents to USD
  lifetimeBudgetInCampaignCurrency =
    lifetimeBudgetInCampaignCurrency / currencyConversionFactor;

  const transaction: IAdCreditsTransaction = {
    type: 'CREDIT',
    description: `Credit for campaign ${
      campaignDetails?.friendly_name ??
      campaignDetails?.details?.business_details?.business_category
    }`,
    campaign_id: campaignDetails.id,
    currency,
    value: lifetimeBudgetInCampaignCurrency - spendInCampaignCurrency,
  };
  await createAdCreditTransaction(campaignDetails.uid, transaction);
  return transaction;
};

// it returns in INR/USD/IDR so multiple with currency conversion factor
export const getUsersAdCreditBalanceForACurrency = async (
  uid: string,
  currency: Currency,
): Promise<number> => {
  const adCredits = await getAdCreditBalance(uid);
  if (!adCredits) return 0;

  const key = currency.toLowerCase() as keyof IAdCreditsBalance;

  // Avoid returning timestamp fields by mistake
  const isValidCurrencyKey = [
    'usd',
    'inr',
    'idr',
    'php',
    'thb',
    'vnd',
    'myr',
  ].includes(key);

  return isValidCurrencyKey ? (adCredits[key] as number) ?? 0 : 0;
};
