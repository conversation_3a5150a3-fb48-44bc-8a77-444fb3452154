import { tool } from '@langchain/core/tools';
import {
  type ICplInputParams,
  LeadChannel,
  LeadCityCategory,
  LeadCurrency,
  LeadIndustry,
  ProductPriceRange,
} from './types';
import { calculateCpl } from './utils';

export const leadCalculatorTool = tool(
  async (params) => {
    try {
      const cpl = calculateCpl(params as ICplInputParams);

      return JSON.stringify({
        cpl,
        currency: (params as ICplInputParams).currency,
        parameters: params,
        cpl_range_lower: Math.round(cpl * 0.9),
        cpl_range_upper: Math.round(cpl * 1.1),
      });
    } catch (error) {
      throw new Error('Error calculating CPL: ' + (error as Error).message);
    }
  },
  {
    name: 'lead_cost_calculator',
    description: `
      Calculates Cost Per Lead (CPL) based on:
      - industry: ${Object.values(LeadIndustry).join(', ')}
      - city tier: ${Object.values(LeadCityCategory).join(', ')}
      - channel: ${Object.values(LeadChannel).join(', ')}
      - currency: ${Object.values(LeadCurrency).join(', ')}
      - price range: ${Object.values(ProductPriceRange).join(', ')}
    `,
    schema: {
      type: 'object',
      properties: {
        industry: { enum: Object.values(LeadIndustry) },
        city_category: { enum: Object.values(LeadCityCategory) },
        channel: { enum: Object.values(LeadChannel) },
        currency: { enum: Object.values(LeadCurrency) },
        price_range: {
          enum: Object.values(ProductPriceRange),
          description: `Product price range:
            - For INR: low (<₹5K), medium (₹5K-50K), high (₹50K-2L), premium (>₹2L)
            - For USD: low (<$60), medium ($60-$600), high ($600-$2.4K), premium (>$2.4K)`,
        },
      },
      required: [
        'industry',
        'city_category',
        'channel',
        'currency',
        'price_range',
      ],
    },
  },
);
