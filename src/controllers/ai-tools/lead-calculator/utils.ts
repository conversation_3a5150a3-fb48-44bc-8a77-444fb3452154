import {
  type ICplInputParams,
  LeadChannel,
  LeadCityCategory,
  LeadCurrency,
  LeadIndustry,
  ProductPriceRange,
} from './types';

const INDUSTRY_CPL_MAPPING = {
  [LeadCurrency.INR]: {
    [LeadIndustry.real_estate]: 500,
    [LeadIndustry.edtech]: 300,
    [LeadIndustry.ecommerce]: 250,
    [LeadIndustry.healthcare]: 350,
    [LeadIndustry.saas]: 400,
    [LeadIndustry.fitness]: 320,
    [LeadIndustry.automobile]: 450,
    [LeadIndustry.others]: 300,
  },
  [LeadCurrency.USD]: {
    [LeadIndustry.real_estate]: 80,
    [LeadIndustry.edtech]: 45,
    [LeadIndustry.ecommerce]: 35,
    [LeadIndustry.healthcare]: 55,
    [LeadIndustry.saas]: 60,
    [LeadIndustry.fitness]: 40,
    [LeadIndustry.automobile]: 70,
    [LeadIndustry.others]: 45,
  },
};

const CITY_CATEGORY_CPL_MODIFIER = {
  [LeadCurrency.INR]: {
    [LeadCityCategory.tier_1]: 1.2,
    [LeadCityCategory.tier_2]: 1.0,
    [LeadCityCategory.tier_3]: 0.85,
  },
  [LeadCurrency.USD]: {
    [LeadCityCategory.tier_1]: 1.3, // e.g. NYC, SF, LA – more expensive
    [LeadCityCategory.tier_2]: 1.1, // e.g. Austin, Denver – moderately expensive
    [LeadCityCategory.tier_3]: 0.9, // e.g. smaller towns – cheaper CPL
  },
};

const LEAD_CHANNEL_CPL_MODIFIER = {
  [LeadCurrency.INR]: {
    [LeadChannel.google]: 1.2,
    [LeadChannel.facebook]: 1.0,
    [LeadChannel.instagram]: 0.95,
    [LeadChannel.linkedin]: 1.3,
    [LeadChannel.youtube]: 1.1,
    [LeadChannel.tiktok]: 0.9,
  },
  [LeadCurrency.USD]: {
    [LeadChannel.google]: 1.3, // Highly competitive, high-intent
    [LeadChannel.facebook]: 1.1, // Broad targeting, decent cost
    [LeadChannel.instagram]: 1.0, // Slightly cheaper than Facebook
    [LeadChannel.linkedin]: 1.6, // Very expensive, niche B2B targeting
    [LeadChannel.youtube]: 1.2, // Mid-range cost with high engagement
    [LeadChannel.tiktok]: 0.8, // Cheapest, good for top-funnel B2C
  },
};

const PRICE_RANGE_CPL_MODIFIER = {
  [LeadCurrency.INR]: {
    [ProductPriceRange.LOW]: 0.9, // < ₹5,000
    [ProductPriceRange.MEDIUM]: 1.0, // ₹5,000 – ₹50,000
    [ProductPriceRange.HIGH]: 1.1, // ₹50,000 – ₹2,00,000
    [ProductPriceRange.PREMIUM]: 1.2, // > ₹2,00,000
  },
  [LeadCurrency.USD]: {
    [ProductPriceRange.LOW]: 0.9, // < $60
    [ProductPriceRange.MEDIUM]: 1.0, // $60 – $600
    [ProductPriceRange.HIGH]: 1.1, // $600 – $2,400
    [ProductPriceRange.PREMIUM]: 1.2, // > $2,400
  },
};

export const calculateCpl = (params: ICplInputParams): number => {
  // first calculate the base CPL by industry
  const baseCpl = INDUSTRY_CPL_MAPPING[params.currency]?.[params.industry];
  const cityCategoryModifier =
    CITY_CATEGORY_CPL_MODIFIER[params.currency]?.[params.city_category];
  const channelModifier =
    LEAD_CHANNEL_CPL_MODIFIER[params.currency]?.[params.channel];
  const priceRangeModifier =
    PRICE_RANGE_CPL_MODIFIER[params.currency]?.[params.price_range];

  const finalCpl =
    baseCpl * cityCategoryModifier * channelModifier * priceRangeModifier;
  return Math.round(finalCpl);
};
