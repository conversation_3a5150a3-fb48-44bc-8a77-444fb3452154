export interface ILeadCalculatorAgentResponse {
  message: string;
  cpl_data?: object;
}

export enum LeadIndustry {
  real_estate = 'real_estate',
  edtech = 'edtech',
  ecommerce = 'ecommerce',
  healthcare = 'healthcare',
  saas = 'saas',
  fitness = 'fitness',
  automobile = 'automobile',
  others = 'others',
}

export enum LeadCityCategory {
  tier_1 = 'tier_1',
  tier_2 = 'tier_2',
  tier_3 = 'tier_3',
}

export enum LeadChannel {
  google = 'google',
  facebook = 'facebook',
  instagram = 'instagram',
  linkedin = 'linkedin',
  youtube = 'youtube',
  tiktok = 'tiktok',
}

export enum LeadCurrency {
  USD = 'USD',
  INR = 'INR',
}

export enum ProductPriceRange {
  LOW = 'low', // < ₹5,000
  MEDIUM = 'medium', // ₹5,000 – ₹50,000
  HIGH = 'high', // ₹50,000 – ₹2,00,000
  PREMIUM = 'premium', // > ₹2,00,000
}

export interface ICplInputParams {
  industry: LeadIndustry;
  city_category: LeadCityCategory;
  channel: LeadChannel;
  currency: LeadCurrency;
  price_range: ProductPriceRange;
}
