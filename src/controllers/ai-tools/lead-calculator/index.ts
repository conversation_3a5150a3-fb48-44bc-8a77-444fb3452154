import { ChatOpenAI } from '@langchain/openai';
import { AgentExecutor, createToolCallingAgent } from 'langchain/agents';

import {
  type ILeadCalculatorAgentResponse,
  LeadChannel,
  LeadIndustry,
} from './types';
import logger from '../../../modules/logger';
import { leadCalculatorTool } from './tool';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { getMemory } from '../memory';
import { FIRESTORE_COLLECTIONS } from '../../../constants';

const initChatChain = async (payload: {
  session_id: string;
  email: string;
}): Promise<AgentExecutor> => {
  const model = new ChatOpenAI({
    model: 'gpt-4o-mini',
    temperature: 0.3,
    openAIApiKey: process.env.OPEN_AI_API_KEY,
  });

  const tools = [leadCalculatorTool];

  const prompt = ChatPromptTemplate.fromMessages([
    [
      'system',
      `
    You are an expert Cost Per Lead (CPL) calculator assistant. Your role is to:
    1. Professionally guide users through providing required inputs
    2. Calculate accurate CPL based on industry standards
    3. Maintain a helpful, patient, and expert demeanor
    4. Politely refuse any unrelated requests and always bring the user back to the topic of CPL estimation

    Interaction Rules:
    - Speak concisely and professionally
    - Never mention internal classifications logics or processes
    - Politely enforce USD/INR currency requirement
    - Guide users one step at a time

    Required Input Collection:
    1. Industry: ${Object.values(LeadIndustry).join(', ')}
    2. Target City: Ask for city name only
    3. Marketing Channel: ${Object.values(LeadChannel).join(', ')}
    4. Currency: Only accept USD or INR
    5. Product Price (approximate): Use numeric input or describe the price range

    Product Price Range Mapping:
    - For INR:
      - low_inr: Under ₹5,000
      - medium_inr: ₹5,000 to ₹50,000
      - high_inr: ₹50,000 to ₹2,00,000
      - premium_inr: Over ₹2,00,000

    - For USD:
      - low_usd: Under $60
      - medium_usd: $60 to $600
      - high_usd: $600 to $2,400
      - premium_usd: Over $2,400

    If user provides a numeric value, match it to the appropriate range based on selected currency.
    If product price is missing or invalid, assume standard modifier (×1.0).

    Response Guidelines:
    - For cities: "Which city are you targeting for your campaign?"
    - For invalid currency: "We currently only support USD and INR calculations. Which would you prefer to use?"
    - For product price: "What’s the typical selling price of your product or service?"
    - Present results clearly: "Based on your inputs, your estimated CPL is [amount]"

    Tone:
    - Always professional and patient
    - Avoid technical jargon
    - Never reveal internal mappings
    - Maintain expert authority
  `,
    ],
    ['placeholder', '{chat_history}'],
    ['human', '{input}'],
    ['placeholder', '{agent_scratchpad}'],
  ]);

  const agent = createToolCallingAgent({
    llm: model,
    prompt,
    tools,
  });

  const executor = new AgentExecutor({
    agent,
    memory: await getMemory({
      ...payload,
      collections: [FIRESTORE_COLLECTIONS.AGENTS, 'sessions'],
      docs: ['lead-calculator', payload.session_id],
    }),
    verbose: false, // DEV,
    tools,
    returnIntermediateSteps: true, // to access tool results
  });

  return executor;
};

export const handleAgentRequest = async (payload: {
  session_id: string;
  message: string;
  email: string;
}): Promise<ILeadCalculatorAgentResponse> => {
  try {
    const chain = await initChatChain(payload);
    const result = await chain.invoke({ input: payload.message });
    let cplData = {};

    if (result.intermediateSteps?.length > 0) {
      const toolOutput = result.intermediateSteps[0].observation;
      cplData =
        typeof toolOutput === 'string' ? JSON.parse(toolOutput) : toolOutput;
    }

    return {
      message: result.output,
      cpl_data: cplData,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};
