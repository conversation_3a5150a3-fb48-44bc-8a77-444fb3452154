import { FirestoreChatMessageHistory } from '@langchain/community/stores/message/firestore';
import { BufferMemory, type MemoryVariables } from 'langchain/memory';
import {
  db,
  growEasyLangchainMemoryConfig,
} from '../../modules/firebase_admin_sdk';
import { Timestamp } from 'firebase-admin/firestore';

// Custom memory class to limit history
class LimitedBufferMemory extends BufferMemory {
  async loadMemoryVariables(values: any): Promise<MemoryVariables> {
    const result = await super.loadMemoryVariables(values);
    if (result.chat_history && Array.isArray(result.chat_history)) {
      result.chat_history = result.chat_history.slice(-20); // Keep last 20 messages
    }
    return result;
  }
}

const storeSessionMetadataOnce = async (path: string): Promise<void> => {
  const sessionRef = db.doc(path);
  const sessionSnap = await sessionRef.get();

  if (!sessionSnap.exists) {
    // id & user_id will be written by <PERSON><PERSON><PERSON><PERSON>
    await sessionRef.set(
      {
        created_at: Timestamp.now(),
      },
      { merge: true }, // Important to avoid overwriting Langchain's messages
    );
  }
};

export const getMemory = async (payload: {
  session_id: string;
  email: string;
  collections: string[];
  docs: string[];
}): Promise<LimitedBufferMemory> => {
  const { collections, docs, session_id: sessionId, email } = payload;

  // by default langchain is only storing user_id & session_id
  // we also want to store created_at
  await storeSessionMetadataOnce(
    `${collections[0]}/${docs[0]}/${collections[1]}/${docs[1]}`,
  );

  const chatHistory = new FirestoreChatMessageHistory({
    sessionId,
    userId: email,
    collections,
    docs,
    config: growEasyLangchainMemoryConfig,
  });

  // Configure memory with proper keys
  const memory = new LimitedBufferMemory({
    chatHistory,
    memoryKey: 'chat_history', // Key used in the prompt template
    inputKey: 'input', // Matches the chain input key
    outputKey: 'output', // Matches the chain output key
    returnMessages: true, // Required for chat models
  });

  return memory;
};
