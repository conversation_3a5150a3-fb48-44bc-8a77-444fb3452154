import { ChatOpenAI } from '@langchain/openai';
import { AgentExecutor, createToolCallingAgent } from 'langchain/agents';
import { generateAdCopiesTool } from './tool';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { AdChannel } from './types';
import { getMemory } from '../memory';
import logger from '../../../modules/logger';
import { FIRESTORE_COLLECTIONS } from '../../../constants';

export const initChatChain = async (payload: {
  session_id: string;
  email: string;
}): Promise<AgentExecutor> => {
  const model = new ChatOpenAI({
    model: 'gpt-4o-mini',
    temperature: 0.3,
    openAIApiKey: process.env.OPEN_AI_API_KEY,
  });

  const tools = [generateAdCopiesTool];

  const prompt = ChatPromptTemplate.fromMessages([
    [
      'system',
      `
You are an expert ad copywriter assistant. Your job is to help businesses generate compelling ad copy tailored to the right platform. Follow these responsibilities:

Responsibilities:
1. Understand user’s product or service
2. Identify the appropriate ad channel (${Object.values(AdChannel).join(', ')})
3. Collect essential business information
4. Use generate_ad_copies tool to return smart, marketing-friendly ad copy
5. Always guide users patiently and clearly

Interaction Rules:
- Always ask: "Which platform are you running ads on – ${Object.values(
        AdChannel,
      ).join(', ')}?"
- Ask for a clear description: "Can you describe your product or service in two or three lines?"
- Ask about business category if it is not getting identified from product/service description: "Which category does your business fall under?"
  - Examples: Advertising and Marketing, Health and Wellness, Information Technology (IT), Real Estate and Property Management, Travel and Tourism, Education and E-Learning
- For Facebook/Instagram: Ask for ad language (e.g., English, Hindi)
- For Google: Default language to English, don’t ask
- If the user doesn't specify language on Facebook/Instagram, ask before continuing

Guidelines:
- Don’t mention the word ‘tool’ or ‘AI’
- Output should look like marketing copy, not robotic suggestions
- Never show internal processes or enum values
- Maintain a helpful, creative, and professional tone

Final Output Format:
- Provide 2–3 ad copy options with a mix of headlines and body text
- Keep each copy short and effective

Always redirect user to focus on ad generation if they go off-topic.
`,
    ],
    ['placeholder', '{chat_history}'],
    ['human', '{input}'],
    ['placeholder', '{agent_scratchpad}'],
  ]);

  const agent = createToolCallingAgent({
    llm: model,
    prompt,
    tools,
  });

  const executor = new AgentExecutor({
    agent,
    memory: await getMemory({
      ...payload,
      collections: [FIRESTORE_COLLECTIONS.AGENTS, 'sessions'],
      docs: ['generate-ad-copies', payload.session_id],
    }),
    verbose: false,
    tools,
    returnIntermediateSteps: true,
  });

  return executor;
};

export const handleAgentRequest = async (payload: {
  session_id: string;
  message: string;
  email: string;
}): Promise<{
  message: string;
  ad_copies?: object;
}> => {
  try {
    const chain = await initChatChain(payload);
    const result = await chain.invoke({ input: payload.message });
    let adCopies = {};

    if (result.intermediateSteps?.length > 0) {
      const toolOutput = result.intermediateSteps[0].observation;
      adCopies =
        typeof toolOutput === 'string' ? JSON.parse(toolOutput) : toolOutput;
    }

    return {
      message: result.output,
      ad_copies: adCopies,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};
