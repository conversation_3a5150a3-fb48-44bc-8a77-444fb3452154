import { tool } from '@langchain/core/tools';
import {
  AdLanguage,
  GROWEASY_CAMPAIGN_TYPE,
  type IBusinessDetails,
} from '../../../types/campaign_details';
import { AdChannel } from './types';
import { getAdCopies as generateAdCopiesForMeta } from '../../openai_controller';
import { generateAdCopies as generateAdCopiesForGoogle } from '../../google/google_controller';

export interface IGenerateAdCopiesInput {
  channel: AdChannel;
  ad_language?: AdLanguage;
  business_details: IBusinessDetails;
}

export const generateAdCopiesTool = tool(
  async (params) => {
    try {
      const input = params as IGenerateAdCopiesInput;

      // Apply default ad_language if needed
      const shouldDefaultLanguage =
        (input.channel === AdChannel.GOOGLE && input.ad_language != null) ||
        (input.channel !== AdChannel.GOOGLE && input.ad_language == null);

      if (shouldDefaultLanguage) {
        input.ad_language = AdLanguage.ENGLISH;
      }
      const generateAdCopiesMethod = [
        AdChannel.FACEBOOK,
        AdChannel.INSTAGRAM,
      ].includes(input.channel)
        ? generateAdCopiesForMeta
        : generateAdCopiesForGoogle;
      const result = await generateAdCopiesMethod({
        ...input,
        targeting: {
          age_min: 21,
          age_max: 65,
          genders: [1, 2],
          geo_locations: {},
          flexible_spec: [],
          age_range: [],
        },
        type: GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
      });

      return JSON.stringify({
        ad_copies: result,
        parameters: input,
      });
    } catch (error) {
      throw new Error(
        'Error generating ad copies: ' + (error as Error).message,
      );
    }
  },
  {
    name: 'generate_ad_copies',
    description: `
      Generates ad copy suggestions based on:
      - channel: facebook, instagram, or google
      - ad_language: required only for facebook/instagram (defaults to English for google)
      - business_details: includes business name (optional), product/service description (required), and category (required)
    `,
    schema: {
      type: 'object',
      properties: {
        channel: { enum: ['facebook', 'instagram', 'google'] },
        ad_language: {
          enum: Object.values(AdLanguage),
          description:
            'Required for Facebook/Instagram. Defaults to English for Google.',
        },
        business_details: {
          type: 'object',
          properties: {
            business_name: { type: 'string' },
            product_or_service_description: { type: 'string' },
            business_category: { type: 'string' },
          },
          required: ['product_or_service_description', 'business_category'],
        },
      },
      required: ['channel', 'business_details'],
    },
  },
);
