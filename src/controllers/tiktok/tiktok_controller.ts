import axios, { AxiosError } from 'axios';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { type Express } from 'express';

import logger from '../../modules/logger';
import {
  type ITiktokAdsData,
  type IAdVideo,
  type ITiktokLocationDetails,
  type ICampaign,
  GrowEasyCampaignStatus,
  Currency,
  type ITiktokCampaignInsights,
} from '../../types/campaign_details';
import ValidationError from '../../utils/validation_error';
import {
  getCampaignLifetimeBudget,
  getTiktokApiHeaders,
  getTiktokApiUrl,
  updateFirestoreCampaign,
} from '../util';
import {
  AD_GLOBAL_AI_PLATFORM_FEE_PERCENTAGE,
  FIRESTORE_COLLECTIONS,
  GROWEASY_S3_PUBLIC_BUCKET_NAME,
  S3_GROWEASY_META_VIDEOS_UPLOAD_DIR,
  S3_UPLOADED_ASSETS_DIR,
  TIKTOK_AD_ACCOUNT_ID,
  TMP_ADIMAGES_UPLOAD_DIR,
} from '../../constants';
import { uploadFileToS3 } from '../../modules/aws/s3';
import { generateThumbnailFromVideo } from '../../modules/ffmpeg';
import {
  getCampaignInsightsFromTiktok,
  launchCampaignOnTiktok,
  updateAdGroupOnTiktok,
  updateCampaignOnTiktok,
  uploadImageToTikTok,
  uploadVideoToTiktok,
} from '../../modules/groweasy_tiktok';
import { db } from '../../modules/firebase_admin_sdk';
import { createRazorpayOrder } from '../payment_controller';
import { GrowEasyOrderType } from '../../types/payments_invoices';
import { AdPlatforms } from '../../types';

// https://business-api.tiktok.com/portal/docs?id=****************
export const getLocationSearch = async (
  queryParams: Record<string, string>,
): Promise<{
  parent_tags: ITiktokLocationDetails[];
  targeting_tag_list: ITiktokLocationDetails[];
}> => {
  const { query } = queryParams;
  if (!query) {
    throw new ValidationError(400, 'Missing query');
  }

  try {
    const url = getTiktokApiUrl('tool/targeting/search/');
    const headers = getTiktokApiHeaders();

    const payload = {
      advertiser_id: TIKTOK_AD_ACCOUNT_ID,
      objective_type: 'LEAD_GENERATION',
      placements: ['PLACEMENT_TIKTOK'],
      promotion_type: 'LEAD_GENERATION',
      search_type: 'FUZZY_SEARCH',
      keywords: [query], // user’s search term
      region_codes: ['ID'], // only for Indonesia for now
    };

    const response = await axios.post(url.href, payload, { headers });

    // code is non 0 means error, TikTok still responds with 200
    if (response.data?.code) {
      throw new ValidationError(500, response.data.message);
    }
    return response.data?.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data || error);
    throw error;
  }
};

export const uploadAdVideo = async (params: {
  file?: Express.Multer.File;
  queryParams?: Record<string, any>;
  body?: Record<string, string>;
}): Promise<IAdVideo> => {
  const { file, body } = params;

  let videoUrl = body?.file_url;
  let thumbnailUrl = '';

  try {
    if (file) {
      videoUrl = await uploadFileToS3(
        GROWEASY_S3_PUBLIC_BUCKET_NAME,
        file.path,
        `${S3_GROWEASY_META_VIDEOS_UPLOAD_DIR}/${file.filename}`,
      );
      // clean file from disk
      await fs.promises.unlink(file.path);
    }
    if (!videoUrl) {
      throw new ValidationError(400, 'Missing url');
    }
    logger.info('uploadAdVideo: videoUrl', videoUrl);
    // generate thumbnail
    const thumbnailFileName = `${uuidv4()}-thumbnail.png`;
    const thumbnailPath = `${TMP_ADIMAGES_UPLOAD_DIR}/${thumbnailFileName}`;
    await generateThumbnailFromVideo({
      inputVideoUrlOrPath: videoUrl,
      outputImageFilePath: thumbnailPath,
    });
    thumbnailUrl = await uploadFileToS3(
      GROWEASY_S3_PUBLIC_BUCKET_NAME,
      thumbnailPath,
      `${S3_UPLOADED_ASSETS_DIR}/${thumbnailFileName}`,
    );
    await fs.promises.unlink(thumbnailPath);
    logger.info('uploadAdVideo: thumbnailUrl', thumbnailUrl);
    const videoId = (await uploadVideoToTiktok(videoUrl)).video_id;
    const thumbnailData = await uploadImageToTikTok(thumbnailUrl);
    return {
      video_url: videoUrl,
      tiktok: {
        video_id: videoId,
        thumbnail: {
          ...thumbnailData,
          image_url: thumbnailUrl,
        },
      },
    };
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const updateTiktokAdsDataToCampaign = async (
  campaignDocId: string,
  tiktokAdsData: Partial<ITiktokAdsData>,
): Promise<void> => {
  await db
    .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
    .doc(campaignDocId)
    .collection(FIRESTORE_COLLECTIONS.PLATFORM_DATA)
    .doc('tiktok')
    .set(tiktokAdsData, {
      // caution: shallow merge only
      merge: true,
    });
};

export const startTiktokCampaign = async (
  firestoreDocId: string,
  campaign: ICampaign,
): Promise<object> => {
  let response: object = {
    id: campaign.id,
    campaign_id: '',
    advertiser_id: '',
    ad_ids: [] as string[],
    adgroup_id: '',
    platform_fee_percentage: AD_GLOBAL_AI_PLATFORM_FEE_PERCENTAGE,
    order_id: '',
  };
  if (
    !campaign.name ||
    !campaign.details?.business_details ||
    !campaign.details?.targeting ||
    !campaign.details?.budget_and_scheduling ||
    !campaign.details?.ad_copies ||
    !campaign.details?.ad_banners
  ) {
    throw new ValidationError(400, 'Incomplete campaign details');
  }
  if (campaign.details?.budget_and_scheduling?.currency !== Currency.IDR) {
    throw new ValidationError(
      400,
      'For TikTok, the only supported currency is IDR.',
    );
  }
  try {
    if (campaign.details?.budget_and_scheduling) {
      campaign.details.budget_and_scheduling.platform_fee_percentage =
        AD_GLOBAL_AI_PLATFORM_FEE_PERCENTAGE;
    }
    // create campaign, ad group & ads on Tiktok
    const tiktokResponse = await launchCampaignOnTiktok(campaign);

    // create order
    const amount = getCampaignLifetimeBudget(
      campaign?.details?.budget_and_scheduling,
    );

    const orderCreationResponseData = await createRazorpayOrder({
      amount,
      currency: campaign?.details?.budget_and_scheduling?.currency,
      campaign_id: campaign.id,
      uid: campaign.uid,
      type: GrowEasyOrderType.LAUNCH,
      platform: AdPlatforms.TIKTOK,
    });
    logger.debug(
      `orderCreationResponseData for ${campaign.id}`,
      orderCreationResponseData,
    );
    campaign.order_id = orderCreationResponseData.razorpay_order_id;

    // campaign will be published after payment completion
    // check /db/verify-payment route

    // update back to Firestore
    await updateFirestoreCampaign(firestoreDocId, {
      order_id: campaign.order_id,
      status: GrowEasyCampaignStatus.PAUSED, // make it ACTIVE on payment
    });
    await updateTiktokAdsDataToCampaign(firestoreDocId, tiktokResponse);

    response = {
      ...response,
      order_id: campaign.order_id,
      ...tiktokResponse,
    };
  } catch (error) {
    if (error instanceof AxiosError) {
      logger.error(error.response?.data);
      throw new ValidationError(
        400,
        error.response?.data?.error?.message ?? 'Tiktok API error',
      );
    } else {
      logger.error(error);
      throw error;
    }
  }
  return response;
};

export const updateTiktokCampaign = async (params: {
  campaign_id: string;
  operation_status?: 'ENABLE' | 'DISABLE';
  campaign_name?: string;
  budget?: number;
}): Promise<void> => {
  try {
    await updateCampaignOnTiktok(params);
  } catch (error) {
    if (error instanceof AxiosError) {
      logger.error(error.response?.data);
      throw new ValidationError(
        400,
        error.response?.data?.error?.message ?? 'Tiktok API error',
      );
    } else {
      logger.error(error);
      throw error;
    }
  }
};

export const updateTiktokAdGroup = async (params: {
  adgroup_id: string;
  schedule_end_time?: string;
}): Promise<void> => {
  try {
    await updateAdGroupOnTiktok(params);
  } catch (error) {
    if (error instanceof AxiosError) {
      logger.error(error.response?.data);
      throw new ValidationError(
        400,
        error.response?.data?.error?.message ?? 'Tiktok API error',
      );
    } else {
      logger.error(error);
      throw error;
    }
  }
};

export const getTiktokCampaignInsights = async (params: {
  campaign_id: string;
}): Promise<ITiktokCampaignInsights> => {
  try {
    const insights = await getCampaignInsightsFromTiktok(params);
    return insights;
  } catch (error) {
    if (error instanceof AxiosError) {
      logger.error(error.response?.data);
      throw new ValidationError(
        400,
        error.response?.data?.error?.message ?? 'Tiktok API error',
      );
    } else {
      logger.error(error);
      throw error;
    }
  }
};
