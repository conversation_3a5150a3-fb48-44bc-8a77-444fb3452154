import axios, { type AxiosError, type AxiosResponse } from 'axios';
import { type Express } from 'express';
// import crypto from 'crypto';
import { createReadStream, existsSync, mkdirSync } from 'fs';
import FormData from 'form-data';
import logger from '../modules/logger';
import {
  type ICampaign,
  MetaCampaignEffectiveStatus,
  type ITargeting,
  type ILocationDetails,
  type IGeoLocations,
  GROWEASY_CAMPAIGN_TYPE,
  type ICampaignConfig,
  type ICampaignInsightDetails,
  Currency,
} from '../types/campaign_details';
import {
  type IFbLoginStatusResult,
  type IAuthUser,
  type IProcessedFbLoginResponse,
  type IProcessedFbLoginDbData,
  type IMetaAdCreativeDetails,
  type IProcessedWabaOnboardingResponse,
  type IMetaTokenDebugDetails,
  type IWabaPhoneNumberDetails,
  AdPlatforms,
} from '../types';
import ValidationError from '../utils/validation_error';
import {
  addGrowEasyCampaignUtmParams,
  fsUnlink,
  getAllCorrespondingPersonalisedTemplates,
  getCampaignCurrencyBudgetNode,
  getMetaUrl,
  getSingleCampaign,
  uploadAssetToS3,
} from './util';
import {
  TMP_ADIMAGES_UPLOAD_DIR,
  BANNERBOT_S3_PUBLIC_BUCKET_NAME,
  FIRESTORE_COLLECTIONS,
  GROWEASY_ADMIN_SYSTEM_USER_ID,
  GROWEASY_BUSINESS_ID,
  GROWEASY_META_DATASET_ID,
  S3_GROWEASY_META_VIDEOS_UPLOAD_DIR,
  GROW_EASY_PLATFORM_FEE_PERCENTAGE,
  META_BASE_URL,
  GROWEASY_SALES_CAMPAIGNS_DATASET_ID,
} from '../constants';
import { type ICampaignExtensionDetails } from '../types/payments_invoices';
import { type IMetaConversionApiEventPayload } from '../types/conversion_api';
import { type IBannerTemplate } from '../types/banner_template';
import { createImageFromTemplate } from '../modules/canvas';
import { createMulterFile } from '../utils';
import { db } from '../modules/firebase_admin_sdk';
import { Timestamp } from 'firebase-admin/firestore';
import { uploadFileToS3 } from '../modules/aws/s3/index';
import { type IMetaLead } from '../types/leads';
import { getCampaignDetails } from './db_controller';
import { initiateWabaOnboarding } from '../modules/interakt';

const appSecret = process.env.GROWEASY_APP_SECRET ?? '';
/* const hmac = crypto.createHmac('sha256', appSecret);
hmac.update(accessToken);

// https://developers.facebook.com/docs/graph-api/securing-requests%20/
const appSecretProof = hmac.digest('hex'); */

const BCN_FB_PAGE_ID = process.env.GROWEASY_PAGE_ID ?? ''; // process.env.BANNERBOT_PAGE_ID ?? '';
const GROWEASY_APP_ID = process.env.GROWEASY_APP_ID ?? '';
const SAMPLE_LEADGEN_FORM_ID =
  process.env.BANNERBOT_SAMPLE_LEADGEN_FORM_ID ?? '';
// process.env.GROWEASY_SAMPLE_LEADGEN_FORM_ID ?? '';
// const BCN_INSTAGRAM_ACTOR_ID = process.env.GROWEASY_INSTAGRAM_ID ?? ''; // process.env.BANNERBOT_INSTAGRAM_ID ?? '';
// const BCN_WHATSAPP_PH_NO = process.env.BCN_WHATSAPP_PH_NO ?? '';

const DEGREES_OF_FREEDOM_SPEC = {
  creative_features_spec: {
    /* standard_enhancements: {
      enroll_status: 'OPT_IN',
    }, */
    image_enhancement: {
      enroll_status: 'OPT_IN',
    },
    image_touchups: {
      enroll_status: 'OPT_IN',
    },
    text_optimizations: {
      enroll_status: 'OPT_IN',
    },
    inline_comment: {
      enroll_status: 'OPT_IN',
    },
    advantage_plus_creative: {
      enroll_status: 'OPT_IN',
    },
    image_templates: {
      enroll_status: 'OPT_IN',
    },
    // to prevent error in case of special_category campaign:
    // Ads in Regulated Campaigns are not eligible for Advantage+ Creative
    /* image_uncrop: {
      enroll_status: 'OPT_IN',
    }, */
  },
};

const getAdAccountId = (adAccountId?: string): string => {
  // eslint-disable-next-line
  const id = adAccountId || process.env.GROWEASY_AD_ACCOUNT_ID;
  return `act_${id}`;
};

export const getTargetingSearch = async (
  queryParams: Record<string, any>,
): Promise<AxiosResponse> => {
  const url = getMetaUrl(`${META_BASE_URL}/search`);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  return await axios.get(url.href);
};

// axios-retry cannot be used here
// because this is a multipart request with a file stream, the stream cannot be re-used
export const uploadAdImage = async (
  file: Express.Multer.File,
  queryParams?: Record<string, any>,
): Promise<AxiosResponse> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${getAdAccountId(queryParams?.ad_account_id)}/adimages`,
  );
  // logger.debug(url.toString())
  const filePath = file.path;

  // Wrap the request in a retry loop
  const maxRetries = 5;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.info(`uploadAdImage attempt #${attempt}`);

      const data = new FormData();
      data.append('filename', createReadStream(filePath));
      logger.info('uploadAdImage.file.path', file.path);

      const response = await axios({
        method: 'post',
        url: url.toString(),
        data,
        headers: {
          'Content-Type': 'multipart/form-data',
          // using this as a retry skip flag
          'x-no-retry': 'true',
        },
      });

      const imageHash = (
        Object.values(response.data?.images ?? {})?.[0] as { hash: string }
      )?.hash;
      logger.debug('Uploaded image hash:', imageHash);

      // Upload to S3
      const s3UploadResponse = await uploadAssetToS3(file);
      response.data.s3_url = s3UploadResponse.s3_url;

      await fsUnlink(filePath);
      return response;
    } catch (error) {
      logger.error(
        `uploadAdImage error on attempt #${attempt}:`,
        (error as AxiosError)?.response?.data,
      );

      const status = (error as AxiosError)?.response?.status;
      if (attempt === maxRetries || status !== 500) {
        await fsUnlink(filePath);
        throw error;
      }

      // wait before retrying
      await new Promise((resolve) => setTimeout(resolve, attempt * 2500));
    }
  }

  // Should never reach here
  throw new Error('Unexpected uploadAdImage flow exit');
};

export const getAdImages = async (
  queryParams: Record<string, any>,
): Promise<AxiosResponse> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${getAdAccountId(queryParams?.ad_account_id)}/adimages`,
  );
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  return await axios.get(url.href);
};

export const generatePreviews = async (
  queryParams: Record<string, any>,
): Promise<AxiosResponse> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${getAdAccountId(
      queryParams?.ad_account_id,
    )}/generatepreviews`,
  );
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  return await axios.get(url.href);
};

const getLifetimeBudgetToBeUsed = (campaign: ICampaign): number => {
  const budgetAndScheduling = campaign?.details?.budget_and_scheduling;
  const campaignCurrencyBudgetNode =
    getCampaignCurrencyBudgetNode(budgetAndScheduling);

  // exchange rate will be populated by db_controller
  let lifetimeBudget =
    (campaignCurrencyBudgetNode?.lifetime_budget ?? 0) *
    (campaignCurrencyBudgetNode?.exchange_rate ?? 1);
  const currency = budgetAndScheduling?.currency ?? Currency.INR;

  if ([Currency.IDR, Currency.VND].includes(currency)) {
    // for 0 decimal currencies, multiply by 100 since Meta accepts budget in paise
    lifetimeBudget = lifetimeBudget * 100;
  }
  // deduct platform fee
  lifetimeBudget =
    lifetimeBudget -
    (lifetimeBudget *
      (budgetAndScheduling?.platform_fee_percentage ??
        GROW_EASY_PLATFORM_FEE_PERCENTAGE)) /
      100;
  return Math.floor(lifetimeBudget); // has to be an integer
};

// https://developers.facebook.com/docs/marketing-api/guides/lead-ads/create
// Response { "id": "YOUR_CAMPAIGN_ID" }
export const createCampaign = async (
  campaign: ICampaign,
): Promise<{ id: string }> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${getAdAccountId(
      campaign?.details?.config?.ad_account_id,
    )}/campaigns`,
  );
  const objectiveMapping: Record<string, string> = {
    [GROWEASY_CAMPAIGN_TYPE.CTWA]: 'OUTCOME_ENGAGEMENT',
    [GROWEASY_CAMPAIGN_TYPE.LEAD_FORM]: 'OUTCOME_LEADS',
    [GROWEASY_CAMPAIGN_TYPE.META_SALES]: 'OUTCOME_SALES',
  };
  const body: Record<string, string | number | string[]> = {
    buying_type: 'AUCTION',
    name: campaign.name,
    objective:
      objectiveMapping[campaign.type ?? GROWEASY_CAMPAIGN_TYPE.LEAD_FORM],
    special_ad_categories: campaign.special_ad_categories,

    // set it active once all campaign elements are created (adset, ads etc)
    status: MetaCampaignEffectiveStatus.PAUSED,
  };
  if (campaign?.details?.config?.advantage_campaign_budget) {
    body.lifetime_budget = getLifetimeBudgetToBeUsed(campaign);
    body.bid_strategy = 'LOWEST_COST_WITHOUT_CAP';
  }
  const response = await axios.post(url.href, body);
  return response.data;
};

export const getTargetingFromCampaign = (
  campaign: ICampaign,
  advantageAudienceEnabled: boolean,
): ITargeting => {
  const isCtwa = campaign.type === GROWEASY_CAMPAIGN_TYPE.CTWA;
  const specialCategoryAd = campaign.special_ad_categories?.length;
  const campaignTargeting = JSON.parse(
    JSON.stringify(campaign.details?.targeting ?? {}),
  ) as ITargeting;

  // safety check in case FE misses validation
  if (!campaignTargeting.age_min) {
    campaignTargeting.age_min = 18;
  }
  if (!campaignTargeting.age_max) {
    campaignTargeting.age_max = 65;
  }

  if (specialCategoryAd) {
    // no advantage_audience for special category ad -> Bad
    // no restrictions on min & max age -> Good
    advantageAudienceEnabled = false;
  }
  if (advantageAudienceEnabled) {
    // Admin can populate flexible_spec, consume it, currently we are not populating it in onboarding
    // campaignTargeting.flexible_spec = [];

    // in advantage+, age range is just a suggestion to Meta, targeting can fluctuate
    campaignTargeting.age_range = [
      campaignTargeting.age_min,
      campaignTargeting.age_max,
    ];

    // advantage+ audience restrictions
    if (campaignTargeting.age_min > 25) {
      campaignTargeting.age_min = 25;
    }
    campaignTargeting.age_max = 65; // cannot be lower than 65
  }
  const targeting: Partial<ITargeting> = {
    ...campaignTargeting,
    targeting_optimization: isCtwa ? undefined : 'expansion_all',
    // Advantage+ audience is not supported for special ad categories
    targeting_automation: {
      advantage_audience: advantageAudienceEnabled ? 1 : 0,
    },
    // not including audience_network & messenger
    publisher_platforms: ['facebook', 'instagram' /* 'messenger' */],
  };
  // pass geo_locations without type to meta but do store it in db, hence creating a copy
  targeting.geo_locations = JSON.parse(JSON.stringify(targeting.geo_locations));
  // delete type from targeting geo_locations
  const objectKeys: Array<keyof IGeoLocations> = [
    'regions',
    'cities',
    'neighborhoods',
    'places',
  ];
  objectKeys.forEach((objectKey) => {
    let locationDetails: ILocationDetails[] = targeting.geo_locations?.[
      objectKey
    ] as ILocationDetails[];
    if (locationDetails) {
      locationDetails = locationDetails.map((item: ILocationDetails) => {
        // to avoid meta adset validation error: normalisation does not allow type
        delete item.type;
        return item;
      });
    }
  });
  return targeting as ITargeting;
};

export const createAdset = async (
  campaign: ICampaign,
  advantageAudienceEnabled: boolean,
): Promise<{ id: string }> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${getAdAccountId(
      campaign?.details?.config?.ad_account_id,
    )}/adsets`,
  );
  const campaignType = campaign.type ?? GROWEASY_CAMPAIGN_TYPE.LEAD_FORM;

  if (campaignType === GROWEASY_CAMPAIGN_TYPE.META_SALES) {
    if (
      !campaign.details?.config?.meta_sales_purchase_event_name ||
      !campaign.details?.business_details?.website
    ) {
      throw new ValidationError(
        400,
        'Missing website or meta_sales_purchase_event_name',
      );
    }
  }

  /**
    type = LEAD_FORM
    QUALITY_LEAD: Optimize for people who are likely to have a deeper conversation with advertisers after lead submission.
    LEAD_GENERATION: Optimize for people more likely to fill out a lead generation form.

    type = CTWA
    CONVERSATIONS: Directs ads to people more likely to have a conversation with the business.
    LINK_CLICKS: Optimize for people more likely to click in the link of the ad.

    typ = META_SALES
    OFFSITE_CONVERSIONS: Will optimize for people more likely to make a conversion in the site
  */
  const optimizationGoalMapping: Record<string, string> = {
    [GROWEASY_CAMPAIGN_TYPE.CTWA]: 'CONVERSATIONS',
    [GROWEASY_CAMPAIGN_TYPE.LEAD_FORM]: campaign.details?.config
      ?.meta_capi_enabled
      ? 'QUALITY_LEAD'
      : 'LEAD_GENERATION',
    [GROWEASY_CAMPAIGN_TYPE.META_SALES]: 'OFFSITE_CONVERSIONS',
  };

  const destinationTypeMapping: Record<string, string> = {
    [GROWEASY_CAMPAIGN_TYPE.CTWA]: 'WHATSAPP',
    [GROWEASY_CAMPAIGN_TYPE.LEAD_FORM]: 'ON_AD',
    [GROWEASY_CAMPAIGN_TYPE.META_SALES]: 'WEBSITE',
  };

  // https://developers.facebook.com/docs/marketing-api/reference/ad-promoted-object/
  const getPromotedObject = (): Record<string, string> => {
    if (
      [GROWEASY_CAMPAIGN_TYPE.CTWA, GROWEASY_CAMPAIGN_TYPE.LEAD_FORM].includes(
        campaignType,
      )
    ) {
      return {
        page_id: campaign.details?.config?.fb_page_id ?? BCN_FB_PAGE_ID,
      };
    } else if ([GROWEASY_CAMPAIGN_TYPE.META_SALES].includes(campaignType)) {
      /**
       * application_id: If tracking in an app
       * object_store_url: If applicable (mainly for app ads)
       * custom_event_type: One of Meta’s standard events or OTHERS
       * custom_event_str: Custom event name, as defined in Meta Events Manager
       */
      return {
        pixel_id: GROWEASY_SALES_CAMPAIGNS_DATASET_ID,
        /* custom_event_type: 'OTHER',
        custom_event_str:
          campaign.details?.config?.meta_sales_purchase_event_name ?? '', */
        custom_event_type: 'PURCHASE',
      };
    }
    return {};
  };
  const isCtwa = campaign.type === GROWEASY_CAMPAIGN_TYPE.CTWA;

  if (isCtwa) {
    // page_whatsapp_number_id was causing permission issue
    // also linking this no to page was causing 500, weird
    // WhatsApp business no will be picked from Page, WhatsApp business account & FB page linking is prerequisite for CTWA
    // promotedObject.whatsapp_phone_number = BCN_WHATSAPP_PH_NO;
  }
  const body: Record<string, string | number | object | undefined | null> = {
    billing_event: 'IMPRESSIONS',
    campaign_id: campaign.meta_id,
    // daily_budget: campaign.details?.budget_and_scheduling?.daily_budget,
    name: `Adset_${Date.now()}`,
    optimization_goal: optimizationGoalMapping[campaignType],
    status: 'ACTIVE',
    start_time: campaign.details?.budget_and_scheduling?.start_time,
    end_time: campaign.details?.budget_and_scheduling?.end_time,
    targeting: getTargetingFromCampaign(campaign, advantageAudienceEnabled),
    promoted_object: getPromotedObject(),
    destination_type: destinationTypeMapping[campaignType],
  };
  if (campaign?.details?.config?.advantage_campaign_budget) {
    // campaign will have budget settings
  } else {
    // adset will have budget settings
    body.lifetime_budget = getLifetimeBudgetToBeUsed(campaign);
    body.bid_strategy = 'LOWEST_COST_WITHOUT_CAP';
  }
  if (campaignType === GROWEASY_CAMPAIGN_TYPE.META_SALES) {
    body.attribution_spec = [
      {
        event_type: 'CLICK_THROUGH',
        window_days: 7,
      },
      {
        event_type: 'VIEW_THROUGH',
        window_days: 1,
      },
      {
        event_type: 'ENGAGED_VIDEO_VIEW',
        window_days: 1,
      },
    ];
  }
  const response = await axios.post(url.href, body);
  return response.data;
};

// https://developers.facebook.com/docs/marketing-api/guides/lead-ads/create#associate-creative
export const createLeadgenForm = async (
  campaign: ICampaign,
): Promise<{ id: string }> => {
  const fbPageId = campaign?.details?.config?.fb_page_id ?? BCN_FB_PAGE_ID;
  const url = getMetaUrl(`${META_BASE_URL}/${fbPageId}/leadgen_forms`);
  const body = {
    ...campaign.details?.leadgen_form,
    name: `${campaign.details?.leadgen_form?.name}_${Date.now()}`,
  };
  const response = await axios.post(url.href, body);
  return response.data;
};

const getWebsiteUrlForAdCreative = (params: {
  campaignType: GROWEASY_CAMPAIGN_TYPE;
  businessWebsite: string;
  campaignId: string;
}): string => {
  const { campaignType, businessWebsite, campaignId } = params;

  const linkMapping: Record<string, string> = {
    [GROWEASY_CAMPAIGN_TYPE.LEAD_FORM]: 'http://fb.me/',
    [GROWEASY_CAMPAIGN_TYPE.CTWA]: 'https://api.whatsapp.com/send',
    [GROWEASY_CAMPAIGN_TYPE.META_SALES]: addGrowEasyCampaignUtmParams({
      url: businessWebsite,
      campaignId,
      platform: AdPlatforms.META,
    }),
  };
  return linkMapping[campaignType];
};

const getCallToActionForAdCreative = (params: {
  campaignType: GROWEASY_CAMPAIGN_TYPE;
  leadgenFormId?: string;
}): object => {
  const { campaignType, leadgenFormId } = params;

  if (campaignType === GROWEASY_CAMPAIGN_TYPE.CTWA) {
    return {
      type: 'WHATSAPP_MESSAGE',
      value: {
        app_destination: 'WHATSAPP',
      },
    };
  } else if (campaignType === GROWEASY_CAMPAIGN_TYPE.LEAD_FORM) {
    return {
      type: 'LEARN_MORE',
      value: {
        lead_gen_form_id: leadgenFormId,
      },
    };
  } else if (campaignType === GROWEASY_CAMPAIGN_TYPE.META_SALES) {
    return {
      type: 'LEARN_MORE', // SHOP_NOW
      // passing lead_gen_form_id will appear as addon CTA
      value: {},
    };
  }
  return {};
};

const getAdCreativeWithoutPlacementOptimisation = (payload: {
  campaign: ICampaign;
  index: number;
  leadgenFormId: string;
  type: 'VIDEO' | 'IMAGE';
}): Record<string, any> => {
  const { campaign, index, leadgenFormId, type } = payload;
  const adBanners = campaign.details?.ad_banners ?? [];
  const adCopies = campaign.details?.ad_copies ?? [];
  const adBodies = adCopies.map((item) => item.primary_text);
  const adDescriptions = adCopies.map((item) => item.description);
  const adTitles = adCopies.map((item) => item.headline);
  const adImageHashes = adBanners.map((item) => item.image?.hash);
  const campaignType = campaign.type ?? GROWEASY_CAMPAIGN_TYPE.LEAD_FORM;
  const isVideoAd = type === 'VIDEO';

  const promotedObject: Record<string, string | object> = {
    page_id: campaign.details?.config?.fb_page_id ?? BCN_FB_PAGE_ID,
  };
  if (promotedObject.page_id === BCN_FB_PAGE_ID) {
    // promotedObject.instagram_actor_id = BCN_INSTAGRAM_ACTOR_ID;
  }

  const imageHash = adBanners?.[index]?.image?.hash;
  const link = getWebsiteUrlForAdCreative({
    campaignType,
    businessWebsite: campaign.details?.business_details?.website ?? '',
    campaignId: campaign.id,
  });

  const callToAction = getCallToActionForAdCreative({
    campaignType,
    leadgenFormId,
  });

  if (isVideoAd) {
    promotedObject.video_data = {
      video_id: campaign?.details?.ad_videos?.[index]?.id,
      image_hash: imageHash,
      video_thumbnail_source: 'generated_default',
      call_to_action:
        campaignType === GROWEASY_CAMPAIGN_TYPE.META_SALES
          ? {
              ...callToAction,
              value: {
                link,
              },
            }
          : callToAction,
    };
  }

  const adCreative = {
    adset_id: campaign.meta_adset_id,
    name: `${isVideoAd ? 'V_' : ''}Ad_${index}_${Date.now()}`,
    status: 'ACTIVE',
    creative: {
      object_story_spec: {
        ...promotedObject,
        link_data:
          campaignType === GROWEASY_CAMPAIGN_TYPE.CTWA
            ? {
                call_to_action: callToAction,
                image_hash: imageHash,
                link,
                name: 'Chat with us',
                page_welcome_message: {
                  type: 'VISUAL_EDITOR',
                  version: 2,
                  landing_screen_type: 'welcome_message',
                  media_type: 'text',
                  text_format: {
                    customer_action_type: 'autofill_message',
                    message: {
                      text: 'Thank you for your interest. Please share your details.',
                      autofill_message: {
                        content: 'Hello! Can I get more info on this?',
                      },
                    },
                  },
                },
              }
            : {
                call_to_action: callToAction,
                image_hash: imageHash,
                link,
              },
      },
      degrees_of_freedom_spec: DEGREES_OF_FREEDOM_SPEC,
      asset_feed_spec: {
        // Set to remove duplicates
        bodies: [...new Set(adBodies)].map((item) => ({
          text: item,
        })),
        descriptions: [...new Set(adDescriptions)].map((item) => ({
          text: item,
        })),
        optimization_type: 'DEGREES_OF_FREEDOM',
        titles: [...new Set(adTitles)].map((item) => ({
          text: item,
        })),
        audios: [
          {
            type: 'random',
          },
        ],
        // limit is 5
        images: [...new Set(adImageHashes)]
          .filter((_, index) => index < 5)
          .map((item) => ({
            hash: item,
          })),
      },
      // https://www.facebook.com/business/help/****************
      contextual_multi_ads: {
        eligibility: [
          'POST_AD_ENGAGEMENT_FEED',
          'POST_AD_ENGAGEMENT_SEED_AD',
          'STANDALONE_FEED',
        ],
        enroll_status: 'OPT_IN',
      },
    },
  };

  return adCreative;
};

const getAdCreativeWithPlacementOptimisation = (
  campaign: ICampaign,
  index: number, // start from 0
  leadgenFormId: string,
): Record<string, any> => {
  const adBanners = campaign.details?.ad_banners ?? [];
  const adCopies = campaign.details?.ad_copies ?? [];
  const adBodies = adCopies.map((item) => item.primary_text);
  const adDescriptions = adCopies.map((item) => item.description);
  const adTitles = adCopies.map((item) => item.headline);
  const campaignType = campaign.type ?? GROWEASY_CAMPAIGN_TYPE.LEAD_FORM;
  const isCtwa = campaign.type === GROWEASY_CAMPAIGN_TYPE.CTWA;
  const squareImages = adBanners
    .filter((item) => item.image?.width === item.image?.height)
    .map((item, index) => ({
      hash: item.image?.hash,
      label: `square_${index}`,
    }));
  const portraitImages = adBanners
    .filter((item) => (item.image?.width ?? 0) < (item.image?.height ?? 0))
    .map((item, index) => ({
      hash: item.image?.hash,
      label: `portrait_${index}`,
    }));
  const landscapeImages = adBanners
    .filter((item) => (item.image?.width ?? 0) > (item.image?.height ?? 0))
    .map((item, index) => ({
      hash: item.image?.hash,
      label: `landscape_${index}`,
    }));
  const allImages = [...squareImages, ...portraitImages, ...landscapeImages];
  const fallbackImage = allImages[index % allImages.length];
  // incremental square image as per index if it exists or fallback to any incremental image
  const preferredSquareImage = squareImages.length
    ? squareImages[index % squareImages.length]
    : fallbackImage;
  const preferredPortraitImage = portraitImages.length
    ? portraitImages[index % portraitImages.length]
    : fallbackImage;

  const promotedObject: Record<string, string> = {
    page_id: campaign.details?.config?.fb_page_id ?? BCN_FB_PAGE_ID,
  };
  if (promotedObject.page_id === BCN_FB_PAGE_ID) {
    // promotedObject.instagram_actor_id = BCN_INSTAGRAM_ACTOR_ID;
  }

  const adCreative = {
    adset_id: campaign.meta_adset_id,
    name: `Ad_${index}_${Date.now()}`,
    status: 'ACTIVE',
    creative: {
      object_story_spec: {
        ...promotedObject,
      },
      degrees_of_freedom_spec: DEGREES_OF_FREEDOM_SPEC,
      asset_feed_spec: {
        link_urls: [
          {
            website_url: getWebsiteUrlForAdCreative({
              campaignType,
              businessWebsite:
                campaign.details?.business_details?.website ?? '',
              campaignId: campaign.id,
            }),
            display_url: '',
            adlabels: [
              {
                name: 'placement_asset_link_url_1',
              },
              {
                name: 'placement_asset_link_url_2',
              },
            ],
          },
        ],
        call_to_actions: [
          getCallToActionForAdCreative({ campaignType, leadgenFormId }),
        ],
        call_to_action_types: [isCtwa ? 'WHATSAPP_MESSAGE' : 'LEARN_MORE'],
        ad_formats: ['SINGLE_IMAGE'],
        optimization_type: 'PLACEMENT',
        // Set to remove duplicates
        bodies: [...new Set(adBodies)].map((item) => ({
          text: item,
          adlabels: [
            {
              // need not to be unique,
              // same label for different texts means Meta can pick any text depending upon performance
              name: 'placement_asset_body_1',
            },
            {
              name: 'placement_asset_body_2',
            },
          ],
        })),
        descriptions: [...new Set(adDescriptions)].map((item) => ({
          text: item,
          adlabels: [
            {
              name: 'placement_asset_description_1',
            },
            {
              name: 'placement_asset_description_2',
            },
          ],
        })),
        titles: [...new Set(adTitles)].map((item) => ({
          text: item,
          adlabels: [
            {
              name: 'placement_asset_title_1',
            },
            {
              name: 'placement_asset_title_2',
            },
          ],
        })),
        images: allImages.map((item) => {
          return {
            hash: item.hash,
            adlabels: [
              {
                name: item.label,
              },
            ],
          };
        }),
        // fb: feed, instant_article, instream_video, marketplace, right_hand_column, search, story, suggested_video, video_feeds, story_sticker, facebook_reels_overlay, biz_disco_feed, facebook_reels, profile_feed
        // instagram: reels, reels_overlay, stream, story, explore, explore_home, ig_search, shop, profile_feed, profile_reels, effect_tray
        // https://www.facebook.com/business/ads-guide/update/image
        asset_customization_rules: [
          {
            image_label: {
              name: preferredSquareImage.label,
            },
            customization_spec: {
              // empty customization_spec will take care of rest of the placements
              /* publisher_platforms: ['facebook', 'instagram', 'messenger'],
              facebook_positions: [
                'feed',
                'profile_feed',
                'instream_video',
                'facebook_reels_overlay',
                'marketplace',
                'video_feeds',
                'search',
                'biz_disco_feed',
              ],
              instagram_positions: [
                'explore',
                'explore_home',
                'stream',
                'profile_feed',
                'ig_search',
                'shop',
              ],
              messenger_positions: ['messenger_home'], */
            },
            body_label: {
              name: 'placement_asset_body_1',
            },
            title_label: {
              name: 'placement_asset_title_1',
            },
            description_label: {
              name: 'placement_asset_description_1',
            },
            link_url_label: {
              name: 'placement_asset_link_url_1',
            },
          },
          {
            image_label: {
              name: preferredPortraitImage.label,
            },
            customization_spec: {
              publisher_platforms: [
                'facebook',
                'instagram',
                'audience_network',
              ],
              facebook_positions: ['story', 'facebook_reels'],
              instagram_positions: ['story', 'reels'],
              audience_network_positions: ['classic', 'rewarded_video'],
            },
            body_label: {
              name: 'placement_asset_body_2',
            },
            title_label: {
              name: 'placement_asset_title_2',
            },
            description_label: {
              name: 'placement_asset_description_2',
            },
            link_url_label: {
              name: 'placement_asset_link_url_2',
            },
          },
        ],
        additional_data: isCtwa
          ? {
              page_welcome_message: JSON.stringify({
                type: 'VISUAL_EDITOR',
                version: 2,
                landing_screen_type: 'welcome_message',
                media_type: 'text',
                text_format: {
                  customer_action_type: 'autofill_message',
                  message: {
                    text: 'Thank you for your interest. Please share your details.',
                    autofill_message: {
                      content: 'Hello! Can I get more info on this?',
                    },
                  },
                },
              }),
            }
          : {},
        audios: [
          {
            type: 'random',
          },
        ],
      },
      // https://www.facebook.com/business/help/****************
      contextual_multi_ads: {
        eligibility: [
          'POST_AD_ENGAGEMENT_FEED',
          'POST_AD_ENGAGEMENT_SEED_AD',
          'STANDALONE_FEED',
        ],
        enroll_status: 'OPT_IN',
      },
    },
  };

  return adCreative;
};

export const createAd = async (
  campaign: ICampaign,
  index: number,
  leadgenFormId: string,
): Promise<{ id: string }> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${getAdAccountId(
      campaign?.details?.config?.ad_account_id,
    )}/ads`,
  );
  // first ad will not be optimised for placement
  const body =
    index === 0
      ? getAdCreativeWithoutPlacementOptimisation({
          campaign,
          index,
          leadgenFormId,
          type: 'IMAGE',
        })
      : getAdCreativeWithPlacementOptimisation(campaign, index, leadgenFormId);
  const response = await axios.post(url.href, body);
  return response.data;
};

export const createVideoAd = async (payload: {
  campaign: ICampaign;
  leadgenFormId: string;
  index: number;
}): Promise<{ id: string }> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${getAdAccountId(
      payload.campaign?.details?.config?.ad_account_id,
    )}/ads`,
  );
  const body = getAdCreativeWithoutPlacementOptimisation({
    ...payload,
    type: 'VIDEO',
  });
  const response = await axios.post(url.href, body);
  return response.data;
};

// Response: { success: true }
export const updateMetaCampaign = async (
  campaignId: string,
  body: Partial<{
    status: 'PAUSED' | 'ACTIVE';
    lifetime_budget: number;
    name: string;
  }>,
): Promise<{ id: string }> => {
  const url = getMetaUrl(`${META_BASE_URL}/${campaignId}`);
  try {
    const response = await axios.post(url.href, body);
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const getCampaignInsights = async (
  queryParams: Record<string, any>,
): Promise<AxiosResponse> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${queryParams.campaign_id}/insights`,
  );
  if (!queryParams.date_preset) {
    queryParams.date_preset = 'maximum';
  }
  if (!queryParams.fields) {
    queryParams.fields =
      'clicks,impressions,spend,reach,date_start,date_stop,account_id,cpc,ctr,cost_per_action_type,conversions,cost_per_conversion,conversion_values';
  }
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  try {
    return await axios.get(url.href);
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const getAdLeads = async (
  queryParams: Record<string, any>,
  user: IAuthUser,
): Promise<{ data: IMetaLead[] }> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const { campaign_id: campaignId, limit = '500' } = queryParams;
  const campaign = await getCampaignDetails(campaignId, user);
  const leadgenFormId = campaign?.meta_leadgen_form_id;

  /**
   * First priority to retrieve leads is Meta API since Webhook can go wrong (due to bugs like Meta etc) making CRM data unreliable
   * but Meta only retain data for 90 days hence falling back to CRM in that case
   */
  // check if campaign creation date is beyond 90 days
  /* const nintyDaysAgo = Timestamp.fromDate(
    new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
  );
  const campaignCreationDate = campaign.created_at; */
  const leadsSource = 'DB'; // campaignCreationDate.toMillis() < nintyDaysAgo.toMillis() ? 'DB' : 'META';
  if (leadsSource === 'DB') {
    const result = await db
      .collection(FIRESTORE_COLLECTIONS.META_WEBHOOK_LEADS)
      .where('form_id', '==', leadgenFormId)
      .orderBy('created_time', 'desc')
      .limit(parseInt(limit))
      .get();
    const leads = result.docs.map((doc) => doc.data() as IMetaLead);
    return { data: leads };
  }
  const url = getMetaUrl(`${META_BASE_URL}/${leadgenFormId}/leads`);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = await axios.get(url.href);
  return response.data;
};

export const getAdPreview = async (
  queryParams: Record<string, any>,
  user: IAuthUser,
): Promise<AxiosResponse> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const { campaign_id: campaignId, ad_format: adFormat } = queryParams;
  const doc = await getSingleCampaign(user.uid, campaignId);
  if (doc?.id) {
    const campaign: ICampaign = doc.data();
    if (
      campaign?.details?.targeting &&
      campaign?.details?.business_details &&
      campaign?.details?.ad_banners &&
      campaign.details?.ad_copies
    ) {
      const adCreative = getAdCreativeWithPlacementOptimisation(
        campaign,
        0,
        SAMPLE_LEADGEN_FORM_ID,
      );
      const response = await generatePreviews({
        creative: JSON.stringify(adCreative.creative),
        ad_format: adFormat,
      });
      return response.data;
    } else {
      throw new ValidationError(400, 'This campaign has missing details');
    }
  } else {
    throw new ValidationError(
      400,
      'This campaign does not exist or you do not have permission to access it.',
    );
  }
};

export const getLeadDetails = async (
  leadgenId: string,
): Promise<AxiosResponse> => {
  const url = getMetaUrl(`${META_BASE_URL}/${leadgenId}`);
  try {
    const response = await axios.get(url.href);
    return response;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const getAudienceSearch = async (
  queryParams: Record<string, any>,
): Promise<AxiosResponse> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${getAdAccountId(
      queryParams?.ad_account_id,
    )}/targetingsearch`,
  );
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  return await axios.get(url.href);
};

export const updateAdset = async (
  adsetId: string,
  payload: ICampaignExtensionDetails,
): Promise<{ id: string }> => {
  const url = getMetaUrl(`${META_BASE_URL}/${adsetId}`);
  const body: Record<string, string | number> = {
    end_time: payload.end_time ?? '',
  };
  if (payload.lifetime_budget) {
    // this will have platform fee adjusted
    body.lifetime_budget = Math.floor(payload.lifetime_budget); // has to be integer
  }
  try {
    const response = await axios.post(url.href, body);
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const logConversionEvent = async (
  data: IMetaConversionApiEventPayload,
  datasetId = GROWEASY_META_DATASET_ID,
): Promise<{
  events_received: 0 | 1;
  fbtrace_id: string;
}> => {
  const url = getMetaUrl(`${META_BASE_URL}/${datasetId}/events`);
  const response = await axios.post(url.href, data);
  return response.data;
};

export const uploadAdImageV2 = async (
  template: IBannerTemplate,
  user?: IAuthUser,
  queryParams?: Record<string, any>,
): Promise<AxiosResponse> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  if (!existsSync(TMP_ADIMAGES_UPLOAD_DIR)) {
    mkdirSync(TMP_ADIMAGES_UPLOAD_DIR, { recursive: true });
  }
  const fileName = `${Date.now()}-${user.uid}-${template.id}.png`;
  const filePath = `${TMP_ADIMAGES_UPLOAD_DIR}/${fileName}`;
  try {
    await createImageFromTemplate(filePath, template);
    const file = await createMulterFile(filePath);
    const response = await uploadAdImage(file, queryParams);
    return response;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const getMetaOAuthAccessToken = async (params: {
  code: string;
}): Promise<string> => {
  const url = getMetaUrl(`${META_BASE_URL}/oauth/access_token`);
  const queryParams: Record<string, string> = {
    client_id: GROWEASY_APP_ID,
    client_secret: appSecret,
    code: params.code,
  };
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = await axios.post(url.href);
  return response?.data?.access_token;
};

const getMetaTokenDetails = async (params: {
  token: string;
}): Promise<IMetaTokenDebugDetails> => {
  const url = getMetaUrl(`https://graph.facebook.com/debug_token`);
  const queryParams: Record<string, string> = {
    input_token: params.token,
  };
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = await axios.get(url.href);

  return response.data?.data as IMetaTokenDebugDetails;
};

const subscribeToWebhook = async (params: {
  objectId: string;
  queryParams?: Record<string, string>;
}): Promise<void> => {
  const { objectId, queryParams = {} } = params;

  const webhookSubscriptionUrl = getMetaUrl(
    `https://graph.facebook.com/${objectId}/subscribed_apps`,
  );
  Object.keys(queryParams).forEach((key) => {
    webhookSubscriptionUrl.searchParams.set(key, queryParams[key]);
  });
  await axios.post(webhookSubscriptionUrl.href);
};

export const processFbLoginResult = async (
  body: {
    result: IFbLoginStatusResult;
  },
  user?: IAuthUser,
): Promise<IProcessedFbLoginResponse> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const loginResult = body.result;
  if (
    !loginResult?.authResponse?.code &&
    !loginResult?.authResponse?.accessToken
  ) {
    throw new ValidationError(
      400,
      'Missing authorization code or access token',
    );
  }

  try {
    const fbLoginRef = db
      .collection(FIRESTORE_COLLECTIONS.FB_LOGINS)
      .doc(user.uid);
    const fbLoginDoc = await fbLoginRef.get();
    const fbLoginDetails = fbLoginDoc.data() as IProcessedFbLoginDbData | null;

    const payloadToSave: Partial<IProcessedFbLoginDbData> = {
      login_result: loginResult,
    };
    if (loginResult?.authResponse?.code) {
      // if token for this authorization code has already been generated, return the response from db
      if (
        loginResult.authResponse.code ===
        fbLoginDetails?.login_result?.authResponse?.code
      ) {
        return {
          account_details: fbLoginDetails.account_details,
          assigned_pages: fbLoginDetails.assigned_pages,
        };
      } else {
        // get SUAT
        payloadToSave.access_token = await getMetaOAuthAccessToken({
          code: loginResult.authResponse.code,
        });
        payloadToSave.access_token_type = 'SUAT';
      }
    } else if (loginResult?.authResponse?.accessToken) {
      // this is UAT flow
      payloadToSave.access_token = loginResult?.authResponse?.accessToken;
      payloadToSave.access_token_type = 'UAT';
    }

    // get user details
    const accountDetailsUrl = getMetaUrl(`${META_BASE_URL}/me`);
    const accountDetailsQueryParams: Record<string, string> = {
      access_token: payloadToSave.access_token ?? '',
    };
    Object.keys(accountDetailsQueryParams).forEach((key) => {
      accountDetailsUrl.searchParams.set(key, accountDetailsQueryParams[key]);
    });
    const accountDetailsResponse = await axios.get(accountDetailsUrl.href);
    const accountDetails: {
      name: string;
      id: string;
    } = accountDetailsResponse.data;
    payloadToSave.account_details = accountDetails;

    // Get Pages list, using path `accounts` instead of `assigned_pages` since the former return page access token as well
    const assignedPagesUrl = getMetaUrl(
      `${META_BASE_URL}/${accountDetails?.id}/accounts`,
    );
    const assignedPagesQueryParams: Record<string, string> = {
      access_token: payloadToSave.access_token ?? '',
    };
    Object.keys(assignedPagesQueryParams).forEach((key) => {
      assignedPagesUrl.searchParams.set(key, assignedPagesQueryParams[key]);
    });
    const assignedPagesResponse = await axios.get(assignedPagesUrl.href);
    const assignedPages = assignedPagesResponse?.data?.data;
    payloadToSave.assigned_pages = assignedPages;

    // save token to db
    if (fbLoginDetails) {
      // update
      await fbLoginRef.set(
        {
          updated_at: Timestamp.now(),
          ...payloadToSave,
        },
        {
          merge: true,
        },
      );
    } else {
      // create
      await fbLoginRef.set({
        created_at: Timestamp.now(),
        updated_at: Timestamp.now(),
        ...payloadToSave,
      });
    }

    return {
      account_details: accountDetails,
      assigned_pages: assignedPages,
    };
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const grantFbPageAccess = async (
  body: {
    fb_page_id: string;
  },
  user?: IAuthUser,
): Promise<void> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const fbPageId = body?.fb_page_id;
  if (!fbPageId) {
    throw new ValidationError(400, 'Missing fb_page_id');
  }

  try {
    const fbLoginRef = db
      .collection(FIRESTORE_COLLECTIONS.FB_LOGINS)
      .doc(user.uid);
    const fbLoginDoc = await fbLoginRef.get();
    const fbLoginDetails = fbLoginDoc.data() as IProcessedFbLoginDbData | null;

    // get Page access token for this page
    const pageAccessToken = fbLoginDetails?.assigned_pages?.find(
      (item) => item.id === fbPageId,
    )?.access_token;
    if (!pageAccessToken) {
      throw new ValidationError(
        400,
        'No token found for this Page, Please reconnect your Facebook Page.',
      );
    }

    // subscribe to leadgen Webhook
    await subscribeToWebhook({
      objectId: fbPageId,
      queryParams: {
        subscribed_fields: 'leadgen',
        access_token: pageAccessToken,
      },
    });
    logger.info('Leadgen webhook subscription successful', fbPageId);

    // Check if GrowEasy (Agency) already access to this page
    const listAgenciesWithPageAccessUrl = getMetaUrl(
      `${META_BASE_URL}/${fbPageId}/agencies`,
    );
    listAgenciesWithPageAccessUrl.searchParams.set(
      'access_token',
      pageAccessToken,
    );
    const listAgenciesWithPageAccessResponse = await axios.get(
      listAgenciesWithPageAccessUrl.href,
    );
    const agencyAccessAlreadyExists =
      !!listAgenciesWithPageAccessResponse?.data?.data?.find(
        (item: Record<string, string>) => item.id === GROWEASY_BUSINESS_ID,
      );
    logger.info('Agency access exists:', agencyAccessAlreadyExists);

    // grant agency access if not already
    if (!agencyAccessAlreadyExists) {
      const agencyAccessQueryParams: Record<string, string> = {
        business: GROWEASY_BUSINESS_ID,
        permitted_tasks: JSON.stringify(['ADVERTISE', 'ANALYZE']),
      };
      Object.keys(agencyAccessQueryParams).forEach((key) => {
        listAgenciesWithPageAccessUrl.searchParams.set(
          key,
          agencyAccessQueryParams[key],
        );
      });
      await axios.post(listAgenciesWithPageAccessUrl.href);
      logger.info('GrowEasy as agency has now access to', fbPageId);
    }

    // Within GrowEasy (Agency), assign this asset to Admin System User so that all APIs work
    const assignedUsersToPageUrl = getMetaUrl(
      `${META_BASE_URL}/${fbPageId}/assigned_users`,
    );
    const assignedUsersToPageBody = {
      business: GROWEASY_BUSINESS_ID,
      tasks: JSON.stringify(['ADVERTISE', 'ANALYZE']),
      user: GROWEASY_ADMIN_SYSTEM_USER_ID,
    };
    await axios.post(assignedUsersToPageUrl.href, assignedUsersToPageBody);
    logger.info('Assigned asset to GrowEasy Admin System User', fbPageId);
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const createPostOnFbPage = async (
  body: {
    fb_page_id: string;
    url: string;
    message: string;
  },
  user?: IAuthUser,
): Promise<Partial<ICampaignConfig>> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const fbPageId = body?.fb_page_id;
  if (!fbPageId) {
    throw new ValidationError(400, 'Missing fb_page_id');
  }

  try {
    const fbLoginRef = db
      .collection(FIRESTORE_COLLECTIONS.FB_LOGINS)
      .doc(user.uid);
    const fbLoginDoc = await fbLoginRef.get();
    const fbLoginDetails = fbLoginDoc.data() as IProcessedFbLoginDbData | null;

    // get Page access token for this page
    const pageAccessToken = fbLoginDetails?.assigned_pages?.find(
      (item) => item.id === fbPageId,
    )?.access_token;
    if (!pageAccessToken) {
      throw new ValidationError(
        400,
        'No token found for this Page, Please reconnect your Facebook Page.',
      );
    }

    // Publish a media Post
    const url = getMetaUrl(`${META_BASE_URL}/${fbPageId}/photos`);
    const queryParams: Record<string, string> = {
      access_token: pageAccessToken,
    };
    Object.keys(queryParams).forEach((key) => {
      url.searchParams.set(key, queryParams[key]);
    });
    const response = await axios.post(url.href, body);
    const responseData = {
      fb_page_id: fbPageId,
      page_post_id: response.data?.post_id,
    };
    return responseData;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const uploadAdVideo = async (
  file: Express.Multer.File,
  queryParams?: Record<string, any>,
): Promise<{
  id: string;
  video_url?: string;
}> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${getAdAccountId(queryParams?.ad_account_id)}/advideos`,
  );
  const filePath = file.path;
  const data = new FormData();
  data.append('filename', createReadStream(file.path));
  logger.info('uploadAdVideo.file.path', file.path);
  try {
    const promises = [
      axios({
        method: 'post',
        url: url.toString(),
        data,
        headers: { 'Content-Type': 'multipart/form-data' },
      }),
      uploadFileToS3(
        BANNERBOT_S3_PUBLIC_BUCKET_NAME,
        filePath,
        `${S3_GROWEASY_META_VIDEOS_UPLOAD_DIR}/${file.filename}`,
      ),
    ];
    const [axiosResponse, s3Response] = await Promise.all(promises);
    const responseData: {
      id: string;
      video_url?: string;
    } = {
      id: (axiosResponse as AxiosResponse)?.data?.id,
      video_url: s3Response as string,
    };
    await fsUnlink(filePath);
    return responseData;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    // always clean file even in case of error
    await fsUnlink(filePath);
    throw error;
  }
};

export const getAdVideosDetails = async (
  queryParams: Record<string, any>,
): Promise<AxiosResponse> => {
  const url = getMetaUrl(`${META_BASE_URL}`);
  queryParams.fields =
    'id,title,permalink_url,length,updated_time,thumbnails,format,description';
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  return await axios.get(url.href);
};

export const uploadAdVideoFromUrl = async (
  data: {
    file_url: string;
  },
  queryParams?: Record<string, any>,
): Promise<{
  id: string;
  video_url?: string;
}> => {
  const url = getMetaUrl(
    `${META_BASE_URL}/${getAdAccountId(queryParams?.ad_account_id)}/advideos`,
  );
  try {
    const response = await axios({
      method: 'post',
      url: url.toString(),
      data,
    });
    const responseData: {
      id: string;
      video_url?: string;
    } = {
      id: response.data?.id,
      video_url: data.file_url,
    };
    return responseData;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const getDeliveryEstimate = async (
  queryParams: Record<string, any>,
): Promise<AxiosResponse> => {
  // Any valid Ad set Id will work. this AdSet Id belongs to campaign WK2ct3AeKMMIKuFU2z9jREolRxr2_1726172563086
  // varunon99 LaunchEasy
  const url = getMetaUrl(
    `${META_BASE_URL}/120210699455210187/delivery_estimate`,
  );
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  return await axios.get(url.href);
};

// take 1 template, create all 3 images using it
export const uploadAdImagesFromSquareTemplate = async (
  squareTemplate: IBannerTemplate,
  user?: IAuthUser,
  queryParams?: Record<string, any>,
): Promise<AxiosResponse[]> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  if (squareTemplate.size !== 'square') {
    throw new ValidationError(400, 'Missing square template');
  }
  if (!existsSync(TMP_ADIMAGES_UPLOAD_DIR)) {
    mkdirSync(TMP_ADIMAGES_UPLOAD_DIR, { recursive: true });
  }
  // get all corresponding templates, populating with user input data
  const allRelevantTemplates =
    getAllCorrespondingPersonalisedTemplates(squareTemplate);
  try {
    const promises: Array<Promise<AxiosResponse>> = [];
    allRelevantTemplates.forEach((template) => {
      promises.push(uploadAdImageV2(template, user, queryParams));
    });
    const responses = await Promise.all(promises);
    return responses;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const getAdCreativeDetails = async (
  queryParams: Record<string, any>,
): Promise<IMetaAdCreativeDetails> => {
  const adDetailsUrl = getMetaUrl(`${META_BASE_URL}/${queryParams.ad_id}`);
  if (!queryParams.fields) {
    queryParams.fields = 'creative';
  }
  Object.keys(queryParams).forEach((key) => {
    adDetailsUrl.searchParams.set(key, queryParams[key]);
  });
  try {
    const adDetails = await axios.get(adDetailsUrl.href);
    const creativeId = adDetails.data?.creative?.id;
    const creativeDetailsUrl = getMetaUrl(`${META_BASE_URL}/${creativeId}`);
    creativeDetailsUrl.searchParams.set(
      'fields',
      'image_hash,thumbnail_url,video_id,object_story_id,object_type,object_story_spec,asset_feed_spec',
    );
    const response = await axios.get(creativeDetailsUrl.href);
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

// Meta api /insights is not returning data for Ad0 & Video (non placement optimised)
// hence using mix of two approaches
export const getAdCreativeInsights = async (
  queryParams: Record<string, any>,
): Promise<ICampaignInsightDetails[]> => {
  const insertItemIntoInsightDetails = (
    arr: ICampaignInsightDetails[],
    item: ICampaignInsightDetails,
  ): void => {
    let low = 0;
    let high = arr.length;

    // Perform binary search to find the right position for the item
    while (low < high) {
      const mid = Math.floor((low + high) / 2);

      // Compare clicks (assuming they are numeric, but they're currently strings so we convert to numbers for comparison)
      if (parseInt(arr[mid].clicks) > parseInt(item.clicks)) {
        low = mid + 1;
      } else {
        high = mid;
      }
    }

    // Insert the item at the found position
    arr.splice(low, 0, item);
  };

  try {
    const campaignId = queryParams.meta_id;
    const adIds = queryParams.meta_ad_ids?.split(',');
    const insightsDetailsArr = (
      await getCampaignInsights({
        sort: 'clicks_descending',
        limit: 50,
        breakdowns: 'media_asset',
        campaign_id: campaignId,
      })
    ).data?.data;

    if (adIds?.length >= 3) {
      // ad0 is not placement optimised and Meta API doesn't contain its data, so get it separately
      const ad0CreativeDeatils = await getAdCreativeDetails({
        ad_id: adIds[0],
      });
      // image_hash will only come for non-placement optimised ad so making double sure here
      if (ad0CreativeDeatils.image_hash) {
        const ad0InsightsRespopnse = await getCampaignInsights({
          sort: 'clicks_descending',
          limit: 50,
          campaign_id: adIds[0],
        });
        const ad0InsightDetails = ad0InsightsRespopnse.data
          ?.data?.[0] as ICampaignInsightDetails;

        if (ad0InsightDetails) {
          ad0InsightDetails.media_asset = {
            asset_type: 'image_asset',
            hash: ad0CreativeDeatils.image_hash,
            url: ad0CreativeDeatils.thumbnail_url,
          };

          // insert this data maintaining sorting order
          insertItemIntoInsightDetails(insightsDetailsArr, ad0InsightDetails);
        }
      }
    }

    if (adIds?.length === 4) {
      // get data separately for video
      const videoAdCreativeDeatils = await getAdCreativeDetails({
        ad_id: adIds[3],
      });
      // video_id will only come for non-placement optimised video ad so making double sure here
      if (videoAdCreativeDeatils.video_id) {
        const videoAdInsightsRespopnse = await getCampaignInsights({
          sort: 'clicks_descending',
          limit: 50,
          campaign_id: adIds[3],
        });
        const videoAdInsightDetails = videoAdInsightsRespopnse.data
          ?.data?.[0] as ICampaignInsightDetails;

        if (videoAdInsightDetails) {
          videoAdInsightDetails.media_asset = {
            asset_type: 'video_asset',
            hash:
              videoAdCreativeDeatils.object_story_spec?.video_data
                ?.image_hash ?? '',
            url:
              videoAdCreativeDeatils?.object_story_spec?.video_data
                ?.image_url ?? '',
          };

          // insert this data maintaining sorting order
          insertItemIntoInsightDetails(
            insightsDetailsArr,
            videoAdInsightDetails,
          );
        }
      }
    }
    return insightsDetailsArr;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const processWabaOnboardingResult = async (
  body: {
    code: string;
  },
  user?: IAuthUser,
): Promise<IProcessedWabaOnboardingResponse> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  if (!body?.code) {
    throw new ValidationError(400, 'Missing authorization code');
  }
  try {
    // get access token and token details
    const accessToken = await getMetaOAuthAccessToken({ code: body.code });
    const accessTokenDetails = await getMetaTokenDetails({
      token: accessToken,
    });
    const wabaId = accessTokenDetails?.granular_scopes?.[0]?.target_ids?.[0];
    logger.info(accessTokenDetails);

    // subscribe to messaging Webhook
    await subscribeToWebhook({
      objectId: wabaId,
    });
    logger.info('WhatsApp webhook subscription successful', wabaId);

    // get all phone numbers
    const phoneNumbersUrl = getMetaUrl(
      `${META_BASE_URL}/${wabaId}/phone_numbers`,
    );
    const response = await axios.get(phoneNumbersUrl.href);
    const phoneNumberDetails = response?.data
      ?.data as IWabaPhoneNumberDetails[];

    const payloadToSave: IProcessedWabaOnboardingResponse = {
      access_token: accessToken,
      token_details: accessTokenDetails,
      waba_id: wabaId,
      phone_numbers: phoneNumberDetails,
      pin: '123456', // hardcoded Two step verification PIN, mandatory in register API
    };

    // register to cloud API so that WhatsApp messages can be sent
    // https://developers.facebook.com/docs/whatsapp/cloud-api/reference/registration/
    const phoneNumberId = phoneNumberDetails?.[0]?.id;
    const registerToCloudApiUrl = getMetaUrl(
      `${META_BASE_URL}/${phoneNumberId}/register`,
    );
    logger.info(
      `Trying to register ${registerToCloudApiUrl.href} with PIN ${payloadToSave.pin}`,
    );
    try {
      await axios.post(registerToCloudApiUrl.href, {
        messaging_product: 'whatsapp',
        pin: payloadToSave.pin,
      });
      logger.info(
        `Registration success for ${phoneNumberId} with PIN ${payloadToSave.pin}`,
      );
    } catch (error) {
      logger.error((error as AxiosError)?.response?.data ?? error);
    }

    const wabaOnboardingRef = db
      .collection(FIRESTORE_COLLECTIONS.WABA_ONBOARDING)
      .doc(user.uid);
    const docSnapshot = await wabaOnboardingRef.get();

    if (docSnapshot.exists) {
      await wabaOnboardingRef.set(
        {
          updated_at: Timestamp.now(),
          ...payloadToSave,
        },
        {
          merge: true,
        },
      );
    } else {
      await wabaOnboardingRef.set({
        created_at: Timestamp.now(),
        updated_at: Timestamp.now(),
        ...payloadToSave,
      });
    }

    return payloadToSave;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const processWabaOnboardingResultAsSolutionPartner = async (
  body: {
    code: string;
  },
  user?: IAuthUser,
): Promise<{ phone_numbers: IWabaPhoneNumberDetails[] }> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  if (!body?.code) {
    throw new ValidationError(400, 'Missing authorization code');
  }
  try {
    // get access token and token details
    const accessToken = await getMetaOAuthAccessToken({ code: body.code });
    const accessTokenDetails = await getMetaTokenDetails({
      token: accessToken,
    });
    const wabaId = accessTokenDetails?.granular_scopes?.[0]?.target_ids?.[0];
    logger.info(accessTokenDetails);
    const interaktResponse = await initiateWabaOnboarding({ waba_id: wabaId });
    logger.info(interaktResponse);
    // get all phone numbers
    const phoneNumbersUrl = getMetaUrl(
      `${META_BASE_URL}/${wabaId}/phone_numbers`,
    );
    const response = await axios.get(phoneNumbersUrl.href);
    const phoneNumberDetails = response?.data
      ?.data as IWabaPhoneNumberDetails[];
    return {
      phone_numbers: phoneNumberDetails,
    };
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

// update the ad0 creative
export const updateAdImage = async (
  campaign: ICampaign,
  payload: {
    image: {
      hash: string;
    };
  },
): Promise<void> => {
  const adIdToBeUpdated = campaign.meta_ad_ids?.[0];
  if (!adIdToBeUpdated) {
    throw new Error('Missing ad id to update creative');
  }

  try {
    // 1. Create a new creative with the updated image
    const creativeUrl = getMetaUrl(
      `${META_BASE_URL}/${getAdAccountId(
        campaign?.details?.config?.ad_account_id,
      )}/adcreatives`,
    );

    // Recreate the creative spec similar to original ad creation
    const creativePayload = getAdCreativeWithoutPlacementOptimisation({
      campaign,
      index: 0, // We're updating the first ad
      leadgenFormId: campaign?.meta_leadgen_form_id ?? '',
      type: 'IMAGE',
    });
    // Update with new image hash
    creativePayload.creative.object_story_spec.link_data.image_hash =
      payload.image.hash;
    const formData = new FormData();
    formData.append('name', creativePayload.name);
    formData.append(
      'object_story_spec',
      JSON.stringify(creativePayload.creative.object_story_spec),
    );
    formData.append(
      'degrees_of_freedom_spec',
      JSON.stringify(creativePayload.creative.degrees_of_freedom_spec),
    );
    formData.append(
      'asset_feed_spec',
      JSON.stringify(creativePayload.creative.asset_feed_spec),
    );
    formData.append(
      'contextual_multi_ads',
      JSON.stringify(creativePayload.creative.contextual_multi_ads),
    );
    const newCreativeResponse = await axios.post(creativeUrl.href, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    const newCreativeId = newCreativeResponse.data.id;

    // 3. Update the ad to use the new creative
    const adUpdateUrl = getMetaUrl(`${META_BASE_URL}/${adIdToBeUpdated}`);
    const updatePayload = {
      creative: {
        creative_id: newCreativeId,
      },
    };

    await axios.post(adUpdateUrl.href, updatePayload);
  } catch (error) {
    logger.error(
      'Failed to update ad image',
      (error as AxiosError)?.response?.data,
    );
    throw new ValidationError(500, (error as Error)?.message);
  }
};
