import { AxiosError } from 'axios';
import {
  type DocumentData,
  type Query,
  Timestamp,
} from 'firebase-admin/firestore';
import {
  AD_GLOBAL_AI_PLATFORM_FEE_PERCENTAGE,
  FIRESTORE_COLLECTIONS,
  GOOGLE_AD_ACCOUNT_ID,
  GROW_EASY_PLATFORM_FEE_PERCENTAGE,
} from '../constants';
import { db } from '../modules/firebase_admin_sdk';
import logger from '../modules/logger';
import {
  type IBusinessCategory,
  type IAuthUser,
  AdPlatforms,
  type IProcessedFbLoginResponse,
  type IProcessedFbLoginDbData,
  type IAdCreditsBalance,
  type IAdCreditsTransaction,
  GrowEasyPartners,
  type ISelfAdAccountConfigs,
} from '../types';
import {
  GrowEasyCampaignStatus,
  type IMetaWebhookLeadEventBody,
  type ICampaign,
  type IWebhookLeadEventEntryChange,
  GROWEASY_CAMPAIGN_TYPE,
  type IGoogleAdsData,
  Currency,
  type ITiktokAdsData,
} from '../types/campaign_details';
import { type ILeadsNotificationPayload, type IMetaLead } from '../types/leads';
import { type ILeadsCrmDetails } from '../types/leads_crm';
import {
  GrowEasyOrderType,
  type ICampaignExtensionDetails,
  type IBillingDetails,
  type IOrderDetails,
  type IInvoiceDetails,
} from '../types/payments_invoices';
import ValidationError from '../utils/validation_error';
import {
  getCampaignDetailsForAdGlobalAiAdmin,
  getCampaignDetailsForGrowEasyAdmin,
  writeCampaignLaunchedToSheet,
} from './admin_controller';
import {
  sendCampaignLaunchedEmail,
  sendCampaignStoppedEmail,
  sendNewLeadsEmail,
} from './email_controller';
import {
  createAd,
  createAdset,
  createCampaign,
  createLeadgenForm,
  createVideoAd,
  getLeadDetails,
  updateMetaCampaign,
} from './meta_controller';
import { createRazorpayOrder } from './payment_controller';
import { sendLeadToPartnerViaWebhook } from './sqs_controller';
import {
  chunkArray,
  getParseLead,
  getSingleCampaign,
  getOrderDetails,
  isGrowEasyAdmin,
  updateFirestoreCampaign,
  isTargetingContainsEuCountries,
  addCampaignToRunningList,
  getCampaignLifetimeBudget,
  getCampaignCurrencyBudgetNode,
  currencyToInrExchangeRate,
  isAdGlobalAiAdmin,
  getAdAccountIdBasedOnBusinessCategory,
} from './util';
import { sendNewInstantFormLeadWhatsappNoti } from './whatsapp/bcn_wa_controller';
import { generateInvoices } from './invoice_controller';
import { convertRemainingBudgetToAdCredits } from './ad_credits_controller';
import {
  startGoogleCampaign,
  updateGoogleAdsDataToCampaign,
  updateGoogleCampaign,
} from './google/google_controller';
import { sendCapiLeadEventToMeta } from './conversion_api_controller';
import { CapiLeadFunnelEvents } from '../types/conversion_api';
import { initAgentEasyChatToLead } from './agenteasy';
import {
  startTiktokCampaign,
  updateTiktokAdsDataToCampaign,
  updateTiktokCampaign,
} from './tiktok/tiktok_controller';

const SAMPLE_LEADGEN_FORM_ID =
  process.env.BANNERBOT_SAMPLE_LEADGEN_FORM_ID ?? ''; // process.env.GROWEASY_SAMPLE_LEADGEN_FORM_ID ?? '';

// todo: support for status filter
export const getCampaigns = async (
  queryParams: Record<string, any>,
  user?: IAuthUser,
): Promise<ICampaign[]> => {
  try {
    const campaigns: ICampaign[] = [];
    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
      .where('uid', '==', `${user?.uid}`)
      // .orderBy('created_at', 'desc')
      .orderBy('updated_at', 'desc')
      .limit(50)
      .get();
    snapshot.forEach((doc) => {
      const data = doc.data() as ICampaign;
      campaigns.push(data);
    });
    return campaigns;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getV2Campaigns = async (
  queryParams: Record<string, any>,
  user?: IAuthUser,
): Promise<{
  campaigns: ICampaign[];
  last_cursor_id: string;
}> => {
  try {
    const { last_cursor_id: lastCursorId, limit = `20` } = queryParams;

    const lastDocRef = lastCursorId
      ? await db
          .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
          .doc(lastCursorId)
          .get()
      : null;

    const campaigns: ICampaign[] = [];
    let query = db
      .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
      .where('uid', '==', `${user?.uid}`)
      .orderBy('updated_at', 'desc');

    if (lastDocRef) {
      query = query.startAfter(lastDocRef);
    }
    // for optimisation, todo
    /* const fieldsToSelect = [
      'created_at',
      'details.budget_and_scheduling',
      'details.business_details',
      'id',
      'name',
      'platform',
      'status',
      'type',
      'uid',
      'updated_at'
    ]; */
    const querySnapshot = await query.limit(parseInt(limit)).get();
    querySnapshot.forEach((doc) => {
      const data = doc.data() as ICampaign;
      campaigns.push(data);
    });
    const newLastDocRef = querySnapshot.docs[querySnapshot.docs.length - 1];

    return {
      campaigns,
      last_cursor_id: newLastDocRef?.id ?? '',
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getGoogleAdsDataFromSubCollection = async (
  campaignDocId: string,
): Promise<IGoogleAdsData> => {
  const campaignGoogleAdsData = (
    await db
      .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
      .doc(campaignDocId)
      .collection(FIRESTORE_COLLECTIONS.PLATFORM_DATA)
      .doc('google')
      .get()
  )?.data() as IGoogleAdsData;
  return campaignGoogleAdsData;
};

export const getTiktokAdsDataFromSubCollection = async (
  campaignDocId: string,
): Promise<ITiktokAdsData> => {
  const campaignTiktokAdsData = (
    await db
      .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
      .doc(campaignDocId)
      .collection(FIRESTORE_COLLECTIONS.PLATFORM_DATA)
      .doc('tiktok')
      .get()
  )?.data() as ITiktokAdsData;
  return campaignTiktokAdsData;
};

export const getCampaignDetails = async (
  campaignId: string,
  user?: IAuthUser,
): Promise<ICampaign> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    let campaignDetailsDocSnapshot;
    if (isGrowEasyAdmin(user)) {
      campaignDetailsDocSnapshot =
        await getCampaignDetailsForGrowEasyAdmin(campaignId);
    } else if (isAdGlobalAiAdmin(user)) {
      campaignDetailsDocSnapshot =
        await getCampaignDetailsForAdGlobalAiAdmin(campaignId);
    } else {
      campaignDetailsDocSnapshot = await getSingleCampaign(
        user?.uid,
        campaignId,
      );
    }
    const campaignDetails = campaignDetailsDocSnapshot?.data() as ICampaign;

    // include google & tiktok data too
    if (campaignDetailsDocSnapshot?.id) {
      if (campaignDetails.platform === AdPlatforms.GOOGLE) {
        campaignDetails.google_ads_data =
          await getGoogleAdsDataFromSubCollection(
            campaignDetailsDocSnapshot.id,
          );
      } else if (campaignDetails.platform === AdPlatforms.TIKTOK) {
        campaignDetails.tiktok_ads_data =
          await getTiktokAdsDataFromSubCollection(
            campaignDetailsDocSnapshot.id,
          );
      }
    }
    return campaignDetails;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

const updateCampaign = async (
  body: Partial<ICampaign>,
  user: IAuthUser,
): Promise<DocumentData | null> => {
  if (!body?.id) {
    throw new Error('Missing campaign id');
  }
  const businessCategory = body?.details?.business_details?.business_category;

  // Some fields cannot be updated from FE
  delete body.created_at;
  delete body.updated_at;
  delete body.order_id;
  delete body.uid;
  delete body.meta_id;
  delete body.meta_leadgen_form_id;
  delete body.meta_adset_id;
  delete body.meta_ad_ids;

  // google_ads_data will be updated in subcollection
  const googleAdsData = body.google_ads_data;
  delete body.google_ads_data;

  // tiktok_ads_data will be updated in subcollection
  const tiktokAdsData = body.tiktok_ads_data;
  delete body.tiktok_ads_data;

  // get the doc
  const doc = await getSingleCampaign(user.uid, body.id);
  if (doc?.id) {
    if (body.status !== GrowEasyCampaignStatus.DRAFT) {
      throw new ValidationError(400, 'Only campaign in Draft can be updated');
    }
    const campaign = {
      ...(body as ICampaign),
      updated_at: Timestamp.now(),
    };
    // in case category is changed, update name
    if (businessCategory) {
      campaign.name = `${user?.email}_${businessCategory}_${Date.now()}`;
      // update ad account ID only when images or videos are not uploaded - GrowEasy only, no self ad accounts
      if (
        !campaign.details?.ad_banners?.length &&
        !campaign.details?.ad_videos?.length &&
        user?.partner !== GrowEasyPartners.AD_GLOBAL_AI &&
        !campaign.details?.config?.self_ad_account_configs
      ) {
        if (campaign.details?.config?.ad_account_id) {
          campaign.details.config.ad_account_id =
            getAdAccountIdBasedOnBusinessCategory(businessCategory);
        }
      }
    }
    // merge
    await updateFirestoreCampaign(doc.id, campaign);

    if (googleAdsData) {
      // FE might delete lead_form_url when user toggles b/w GrowEasy hosted & custom radio options
      // so use fallback
      /* if (!googleAdsData.lead_form_url) {
            googleAdsData.lead_form_url = getGrowEasyHostedLeadFormUrl(
              body?.details?.business_details?.business_name ?? '',
              doc.id,
            );
          } */
      await updateGoogleAdsDataToCampaign(doc.id, googleAdsData);
    }

    if (tiktokAdsData) {
      await updateTiktokAdsDataToCampaign(doc.id, tiktokAdsData);
    }

    const updatedDoc = await db
      .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
      .doc(doc.id)
      .get();
    const updatedData = (updatedDoc.data() as ICampaign) ?? null;
    if (updatedData) {
      updatedData.google_ads_data = googleAdsData;
      updatedData.tiktok_ads_data = tiktokAdsData;
    }
    return updatedData;
  } else {
    throw new ValidationError(
      400,
      'This campaign does not exist or you do not have permission to access it.',
    );
  }
};

const createNewCampaign = async (
  body: Partial<ICampaign>,
  user: IAuthUser,
): Promise<DocumentData | null> => {
  const businessCategory = body?.details?.business_details?.business_category;
  // google_ads_data will be created in subcollection
  const googleAdsData = body.google_ads_data;
  delete body.google_ads_data;

  // tiktok_ads_data will be updated in subcollection
  const tiktokAdsData = body.tiktok_ads_data;
  delete body.tiktok_ads_data;

  // create
  const campaign: ICampaign = {
    special_ad_categories: [],
    name: `${user?.email}_${body?.details?.business_details
      ?.business_category}_${Date.now()}`,
    platform: AdPlatforms.META,
    type: GROWEASY_CAMPAIGN_TYPE.LEAD_FORM, // by default
    ...body,
    id: `${user.uid}_${Date.now()}`,
    status: GrowEasyCampaignStatus.DRAFT,
    created_at: Timestamp.now(),
    updated_at: Timestamp.now(),
    uid: user.uid,
  };
  // assign default Ad Account ID & Page Id
  if (campaign.details) {
    campaign.details.config = {
      ad_account_id:
        user?.partner === GrowEasyPartners.AD_GLOBAL_AI
          ? process.env.GROWEASY_AD_GLOBAL_AI_AD_ACCOUNT_ID
          : getAdAccountIdBasedOnBusinessCategory(businessCategory ?? ''),
      // fb_page_id: process.env.BANNERBOT_PAGE_ID ?? '',
      fb_page_id:
        user?.partner === GrowEasyPartners.AD_GLOBAL_AI
          ? process.env.AD_GLOBAL_AI_PAGE_ID ?? ''
          : process.env.GROWEASY_PAGE_ID ?? '',
      google_ad_account_id: GOOGLE_AD_ACCOUNT_ID,
      advantage_campaign_budget: true,
      google_custom_conversion_action_doc_id:
        campaign.details?.config?.google_custom_conversion_action_doc_id ?? '',
      meta_sales_purchase_event_name:
        campaign.details?.config?.meta_sales_purchase_event_name ?? '',
      partner: user?.partner ?? null, // extract from auth user, not FE payload
    };

    // check for self ad account flow and override ad account id
    const selfAdAccountConfigs = await getSelfAdAccountConfigs({
      user,
      platform: campaign.platform ?? AdPlatforms.META,
    });
    if (selfAdAccountConfigs) {
      logger.info(
        `Self ad account campaign flow, ad account id: ${selfAdAccountConfigs.ad_account_id}`,
      );
      campaign.details.config.self_ad_account_configs = selfAdAccountConfigs;
      if (campaign.platform === AdPlatforms.META) {
        // todo check GrowEasy account ids
        campaign.details.config.ad_account_id =
          selfAdAccountConfigs.ad_account_id;
      } else if (
        campaign.platform === AdPlatforms.GOOGLE &&
        selfAdAccountConfigs.ad_account_id !== GOOGLE_AD_ACCOUNT_ID
      ) {
        campaign.details.config.google_ad_account_id =
          selfAdAccountConfigs.ad_account_id;
      }
    }
  }
  const ref = await db
    .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
    .add(campaign);
  const doc = await ref.get();

  if (googleAdsData) {
    await updateGoogleAdsDataToCampaign(doc.id, googleAdsData);
  }
  if (tiktokAdsData) {
    await updateTiktokAdsDataToCampaign(doc.id, tiktokAdsData);
  }

  const updatedData = (doc.data() as ICampaign) ?? null;
  if (updatedData) {
    updatedData.google_ads_data = googleAdsData;
    updatedData.tiktok_ads_data = tiktokAdsData;
  }
  return updatedData;
};

export const createOrUpdateCampaign = async (
  body: Partial<ICampaign>,
  user?: IAuthUser,
): Promise<DocumentData | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    // merge data if campaign id is available
    // else create new campaign
    if (body?.id) {
      return await updateCampaign(body, user);
    } else {
      return await createNewCampaign(body, user);
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

const startMetaCampaign = async (
  firestoreDocId: string,
  campaign: ICampaign,
): Promise<object> => {
  const response = {
    id: campaign.id,
    meta_id: '',
    meta_adset_id: '',
    meta_ad_ids: [] as string[],
    meta_leadgen_form_id: '',
    platform_fee_percentage: 0,
    order_id: '',
  };
  if (
    !campaign.name ||
    !campaign.details?.business_details ||
    !campaign.details?.targeting ||
    !campaign.details?.budget_and_scheduling ||
    !campaign.details?.ad_copies ||
    !campaign.details?.ad_banners
  ) {
    throw new ValidationError(400, 'Incomplete campaign details');
  }
  try {
    // override campaign.type to LEAD_FORM in case of EU countries
    // since adset optimization goal CONVERSATIONS is not available
    if (
      isTargetingContainsEuCountries(
        campaign.details.targeting?.geo_locations,
      ) ||
      !campaign.type
    ) {
      campaign.type = GROWEASY_CAMPAIGN_TYPE.LEAD_FORM;
    }

    // Take care of currency & exchange rate
    const currency =
      campaign?.details?.budget_and_scheduling?.currency ?? Currency.INR;
    if (currency !== Currency.INR) {
      const campaignCurrencyBudgetNode = getCampaignCurrencyBudgetNode(
        campaign?.details?.budget_and_scheduling,
      );
      if (campaignCurrencyBudgetNode) {
        // calculation will be made in createAdset and this rate will be saved in Firestore
        campaignCurrencyBudgetNode.exchange_rate =
          await currencyToInrExchangeRate(currency);
      }
    }

    // create campaign
    const campaignCreationResponseData = await createCampaign(campaign);
    logger.info(
      `campaignCreationResponseData for ${campaign.id}`,
      campaignCreationResponseData,
    );
    campaign.meta_id = campaignCreationResponseData.id;
    response.meta_id = campaignCreationResponseData.id;

    // create 1st adset
    const adsetCreationResponseData = await createAdset(campaign, true);
    logger.info(
      `adsetCreationResponseData for ${campaign.id}`,
      adsetCreationResponseData,
    );
    campaign.meta_adset_id = adsetCreationResponseData.id;
    if (campaign.details?.budget_and_scheduling) {
      campaign.details.budget_and_scheduling.platform_fee_percentage =
        campaign.details?.config?.partner === GrowEasyPartners.AD_GLOBAL_AI
          ? AD_GLOBAL_AI_PLATFORM_FEE_PERCENTAGE
          : GROW_EASY_PLATFORM_FEE_PERCENTAGE;
      response.platform_fee_percentage =
        campaign.details?.budget_and_scheduling?.platform_fee_percentage;
    }
    response.meta_adset_id = adsetCreationResponseData.id;

    // create leadgen form if form has been populated
    let leadgenFormId = SAMPLE_LEADGEN_FORM_ID;
    if (
      campaign.details.leadgen_form?.questions?.length &&
      campaign.type === GROWEASY_CAMPAIGN_TYPE.LEAD_FORM
    ) {
      const leadgenFormCreationResponseData = await createLeadgenForm(campaign);
      logger.info(
        `leadgenFormCreationResponseData for ${campaign.id}`,
        leadgenFormCreationResponseData,
      );
      leadgenFormId = leadgenFormCreationResponseData.id;
      campaign.meta_leadgen_form_id = leadgenFormId;
      response.meta_leadgen_form_id = leadgenFormId;
    }

    const createAds = async (campaign: ICampaign): Promise<void> => {
      // create 3 ads
      for (
        let i = 0;
        i < (campaign.details?.ad_banners?.length ?? 0) &&
        i < (campaign.details?.ad_copies?.length ?? 0) &&
        i < 3;
        i++
      ) {
        const adCreationResponseData = await createAd(
          campaign,
          i,
          leadgenFormId,
        );
        logger.info(
          `adCreationResponseData #${i} for ${campaign.id}`,
          adCreationResponseData,
        );
        if (campaign.meta_ad_ids?.length) {
          campaign.meta_ad_ids.push(adCreationResponseData.id);
        } else {
          campaign.meta_ad_ids = [adCreationResponseData.id];
        }
      }
      // create 1 video ad if video id is available
      if (campaign.details?.ad_videos?.[0]?.id) {
        const videoAdCreationResponseData = await createVideoAd({
          campaign,
          leadgenFormId,
          index: 0,
        });
        logger.info(
          `videoAdCreationResponseData for ${campaign.id}`,
          videoAdCreationResponseData,
        );
        if (campaign.meta_ad_ids?.length) {
          campaign.meta_ad_ids.push(videoAdCreationResponseData.id);
        } else {
          campaign.meta_ad_ids = [videoAdCreationResponseData.id];
        }
      }
    };
    // create ads in 1st adset, synchronous with advantage+ audience
    await createAds(campaign);
    response.meta_ad_ids = campaign.meta_ad_ids ?? [];

    // create order
    const amount = getCampaignLifetimeBudget(
      campaign?.details?.budget_and_scheduling,
    );
    const orderCreationResponseData = await createRazorpayOrder({
      amount,
      currency: campaign?.details?.budget_and_scheduling?.currency,
      campaign_id: campaign.id,
      uid: campaign.uid,
      type: GrowEasyOrderType.LAUNCH,
      platform: AdPlatforms.META,
    });
    logger.debug(
      `orderCreationResponseData for ${campaign.id}`,
      orderCreationResponseData,
    );
    campaign.order_id = orderCreationResponseData.razorpay_order_id;
    response.order_id = campaign.order_id;

    // campaign will be published after payment completion
    // check /db/verify-payment route

    // update back to Firestore
    await updateFirestoreCampaign(firestoreDocId, {
      meta_id: campaign.meta_id,
      meta_adset_id: campaign.meta_adset_id,
      meta_ad_ids: campaign.meta_ad_ids,
      order_id: campaign.order_id,
      status: GrowEasyCampaignStatus.PAUSED, // make it ACTIVE on payment
      meta_leadgen_form_id: campaign.meta_leadgen_form_id ?? '',
      details: campaign.details,
      type: campaign.type,
    });

    // asynchronously create 2nd adset and ads when campaign does not fall into special categories
    // todo: store ads ids as well, imp for creative analytics
    /* if (!campaign?.special_ad_categories?.length) {
      createAdset(campaign, false).then(adsetCreationResponseData => {
        const campaignCopy: ICampaign = {
          ...campaign,
          meta_adset_id: adsetCreationResponseData.id
        }
        void createAds(campaignCopy);
      }).catch(error => {
        logger.error(error)
      })
    } */
  } catch (error) {
    if (error instanceof AxiosError) {
      logger.error(error.response?.data);
      throw new ValidationError(
        400,
        error.response?.data?.error?.error_user_msg ??
          error.response?.data?.error?.message ??
          'Meta API error',
      );
    } else {
      logger.error(error);
      throw error;
    }
  }
  return response;
};

export const updateCampaignStatus = async (
  body: Record<string, any>,
  user?: IAuthUser,
): Promise<object | null> => {
  const { id, status } = body;
  if (!user?.uid || !id || !status) {
    throw new ValidationError(400, 'Missing id/status/uid');
  }
  if (
    ![
      GrowEasyCampaignStatus.ACTIVE,
      GrowEasyCampaignStatus.PAUSED,
      GrowEasyCampaignStatus.ARCHIVED,
    ].includes(status)
  ) {
    throw new ValidationError(400, 'Valid status are: ACTIVE/PAUSED/ARCHIVED');
  }
  const doc = await getSingleCampaign(user.uid, body.id);
  if (doc) {
    const campaign: ICampaign = await getCampaignDetails(id, user);

    let response;
    switch (status) {
      case GrowEasyCampaignStatus.ACTIVE: {
        if (campaign.status === GrowEasyCampaignStatus.DRAFT) {
          // start (launch) campaign
          if (campaign.platform === AdPlatforms.GOOGLE) {
            response = await startGoogleCampaign(campaign, user);
          } else if (campaign.platform === AdPlatforms.TIKTOK) {
            response = await startTiktokCampaign(doc.id, campaign);
          } else {
            response = await startMetaCampaign(doc.id, campaign);
          }
          // trigger email
          void sendCampaignLaunchedEmail(campaign, user);

          if (
            campaign.details?.config?.self_ad_account_configs?.ad_account_id
          ) {
            // do nothing
          } else {
            // write to sheet so that CX team can followup for payment
            void writeCampaignLaunchedToSheet(campaign, user);
          }
        } else if (
          campaign.status === GrowEasyCampaignStatus.PAUSED &&
          campaign.meta_id
        ) {
          // launch order, not extend
          if (!campaign.order_id) {
            throw new ValidationError(
              400,
              'Please contact support to complete payment',
            );
          }
          // check for payment success
          const orderDetails = await getOrderDetails(campaign.order_id);
          if (orderDetails?.status !== 'paid') {
            throw new ValidationError(
              400,
              'Missing payment details. Please contact support!',
            );
          }
          // resume the campaign
          response = await updateMetaCampaign(campaign.meta_id, {
            status: GrowEasyCampaignStatus.ACTIVE,
          });
          // update back to Firestore
          await updateFirestoreCampaign(doc.id, {
            status: GrowEasyCampaignStatus.ACTIVE,
          });
          void addCampaignToRunningList(campaign);
        } else {
          throw new ValidationError(400, 'Invalid campaign status');
        }
        break;
      }
      case GrowEasyCampaignStatus.PAUSED: {
        if (
          campaign.status === GrowEasyCampaignStatus.ACTIVE &&
          campaign.meta_id
        ) {
          // pause the campaign
          response = await updateMetaCampaign(campaign.meta_id, {
            status: GrowEasyCampaignStatus.PAUSED,
          });
          // update back to Firestore
          await updateFirestoreCampaign(doc.id, {
            status: GrowEasyCampaignStatus.PAUSED,
          });
        } else {
          throw new ValidationError(400, 'Only Live campaigns can be Paused.');
        }
        break;
      }
      case GrowEasyCampaignStatus.ARCHIVED: {
        if (
          [
            GrowEasyCampaignStatus.ACTIVE,
            // GrowEasyCampaignStatus.PAUSED,
          ].includes(campaign.status) &&
          (campaign.meta_id ??
            campaign.google_ads_data?.campaign_resource ??
            campaign.tiktok_ads_data?.campaign_id) &&
          campaign.order_id
        ) {
          // pause the campaign in ads manager, in GrowEasy, it will be treated as archived
          if (campaign.meta_id) {
            response = await updateMetaCampaign(campaign.meta_id, {
              status: GrowEasyCampaignStatus.PAUSED,
            });
          } else if (campaign.google_ads_data?.campaign_resource) {
            // pause campaign on Google
            await updateGoogleCampaign(
              campaign.google_ads_data.campaign_resource,
              {
                status: 'PAUSED',
              },
              campaign.details?.config?.google_ad_account_id,
            );
          } else if (campaign.tiktok_ads_data?.campaign_id) {
            await updateTiktokCampaign({
              campaign_id: campaign.tiktok_ads_data.campaign_id,
              operation_status: 'DISABLE',
            });
          }
          // update back to Firestore
          await updateFirestoreCampaign(doc.id, {
            status: GrowEasyCampaignStatus.ARCHIVED,
          });
          let adCreditsTransaction;
          if (
            campaign?.details?.config?.self_ad_account_configs?.ad_account_id
          ) {
            // do nothing
          } else {
            // credit remaining balance to wallet
            adCreditsTransaction =
              await convertRemainingBudgetToAdCredits(campaign);
          }
          void sendCampaignStoppedEmail({
            campaign,
            authUser: user,
            transaction: adCreditsTransaction,
          });
        } else {
          throw new ValidationError(
            400,
            'Only Live campaigns can be Archived.',
          );
        }
        break;
      }
    }
    return response ?? null;
  } else {
    throw new ValidationError(
      400,
      'This campaign does not exist or you do not have permission to access it.',
    );
  }
};

const checkAndStoreLeadFromWebhook = async (
  change: IWebhookLeadEventEntryChange,
): Promise<{ id: string; created: boolean }> => {
  const leadgenId = change?.value?.leadgen_id;
  const snapshot = await db
    .collection(FIRESTORE_COLLECTIONS.META_WEBHOOK_LEADS)
    .where('id', '==', leadgenId)
    .get();
  if (snapshot.docs[0]) {
    // already exists in Firestore
    return {
      id: leadgenId,
      created: false,
    };
  }
  try {
    // get lead details
    const leadDetailsResponse = await getLeadDetails(leadgenId);
    const leadDetails = leadDetailsResponse.data;
    // save to Firestore
    await db.collection(FIRESTORE_COLLECTIONS.META_WEBHOOK_LEADS).add({
      ...leadDetails,
      ...change.value,
    });
    return {
      id: leadgenId,
      created: true,
    };
  } catch (error) {
    const axiosError = (error as AxiosError)?.response?.data as {
      error: {
        code: number;
        error_subcode: number;
      };
    };
    // We still get leads webhook from pages which are running ads somewhere else, ignore those errors
    if (
      axiosError?.error?.code === 100 &&
      axiosError?.error?.error_subcode === 33
    ) {
      // ignore or else Meta will keep retrying
      return {
        id: leadgenId,
        created: false,
      };
    }
    throw error;
  }
};

export const updateLeadFromWebhook = async (
  leadDetails: Partial<IMetaLead>,
): Promise<void> => {
  try {
    const leadgenId = leadDetails?.id;

    if (!leadgenId) {
      logger.error('Lead details missing ID', leadDetails);
      return;
    }

    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.META_WEBHOOK_LEADS)
      .where('id', '==', leadgenId)
      .limit(1)
      .get();

    if (snapshot.empty) {
      // This should never happen if webhook handling is correct
      logger.error('Missing Meta webhook lead', leadDetails);
      return;
    }

    const docRef = snapshot.docs[0].ref;

    // Merge leadDetails into existing document
    await docRef.set(
      {
        ...leadDetails,
      },
      { merge: true }, // keep existing fields, only update the passed ones
    );
  } catch (error) {
    logger.error('Error updating lead from webhook', error);
  }
};

export const storeLeadsFromWebhook = async (
  body: IMetaWebhookLeadEventBody,
): Promise<Array<{ id: string; created: boolean }>> => {
  const changes: IWebhookLeadEventEntryChange[] = [];
  const responses: Array<{ id: string; created: boolean }> = [];
  // club all entries from across pages
  body?.entry?.forEach((entry) => {
    entry.changes?.forEach((change) => {
      changes.push(change);
    });
  });

  // check and update in batch of 5
  const batchedChanges = chunkArray(changes, 5);
  for (let i = 0; i < batchedChanges.length; i++) {
    const changes = batchedChanges[i];
    const promises: Array<Promise<{ id: string; created: boolean }>> = [];
    changes.forEach((change) =>
      promises.push(checkAndStoreLeadFromWebhook(change)),
    );
    const batchResponses = await Promise.all(promises);
    responses.push(...batchResponses);
  }
  return responses;
};

// limitation: 'IN' supports up to 30 comparison values.
export const getAllFormLeads = async (
  queryParams: Record<string, any>,
  user?: IAuthUser,
): Promise<Array<IMetaLead & { crm_details?: ILeadsCrmDetails }>> => {
  try {
    // get all forms of this user
    /* const leadgenFormIds: string[] = [];
    const campaignsSnapshot = await db
      .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
      .where('uid', '==', `${user?.uid}`) // todo
      .where('status', 'in', [
        GrowEasyCampaignStatus.ACTIVE,
        GrowEasyCampaignStatus.PAUSED,
      ])
      .orderBy('created_at', 'desc')
      .select('meta_leadgen_form_id')
      .limit(30) // firestore limit for in operator
      .get();
    campaignsSnapshot.forEach((doc) => {
      const data = doc.data() as Partial<ICampaign>;
      if (data.meta_leadgen_form_id) {
        leadgenFormIds.push(data.meta_leadgen_form_id);
      }
    });
    logger.debug(leadgenFormIds);
    if (!leadgenFormIds.length) {
      return [];
    } */

    // get all leads of these forms
    // https://firebase.google.com/docs/firestore/query-data/queries
    const limit = Math.min(parseInt(queryParams.limit) || 30); // max is 30, limitation of 'IN' operator
    const leads: Array<
      IMetaLead & {
        crm_details?: ILeadsCrmDetails;
        campaign_details?: Partial<ICampaign>;
      }
    > = [];

    let queryRef: Query = db
      .collection(FIRESTORE_COLLECTIONS.META_WEBHOOK_LEADS)
      .where('uid', '==', user?.uid);

    // Filter: campaign_ids (pass comma separated)
    if (queryParams.campaign_ids) {
      const campaignIds = queryParams.campaign_ids.split(',');
      queryRef = queryRef.where('campaign_id', 'in', campaignIds.slice(0, 30));
    }

    // Filter: lead_category
    if (queryParams.lead_category) {
      queryRef = queryRef.where(
        'agenteasy.lead_category',
        '==',
        queryParams.lead_category,
      );
    }

    // Filter: created_time range
    const createdFrom = queryParams.created_time_from
      ? parseInt(queryParams.created_time_from)
      : undefined;
    const createdTo = queryParams.created_time_to
      ? parseInt(queryParams.created_time_to)
      : undefined;
    if (createdFrom) {
      queryRef = queryRef.where('created_time', '>=', createdFrom);
    }
    if (createdTo) {
      queryRef = queryRef.where('created_time', '<=', createdTo);
    }

    // Sorting and pagination
    queryRef = queryRef.orderBy('created_time', 'desc');

    if (queryParams.start_after) {
      queryRef = queryRef.startAfter(parseInt(queryParams.start_after));
    }

    queryRef = queryRef.limit(limit);

    const leadsSnapshot = await queryRef.get();
    const leadgenIds: string[] = [];
    const campaignIds: string[] = [];

    leadsSnapshot.forEach((doc) => {
      const data = doc.data() as IMetaLead;
      leads.push(data);
      leadgenIds.push(data.id);
      if (data.campaign_id) {
        campaignIds.push(data.campaign_id);
      }
    });

    if (leadgenIds.length) {
      // get all leads Crm for these leads
      const leadsCrmSnapshot = await db
        .collection(FIRESTORE_COLLECTIONS.LEADS_CRM)
        .where('leadgen_id', 'in', leadgenIds)
        .get();
      // match lead CRM and corresponding leads
      leadsCrmSnapshot.forEach((doc) => {
        for (let i = 0; i < leads.length; i++) {
          const leadCrm = doc.data() as ILeadsCrmDetails;
          if (leads[i].id === leadCrm.leadgen_id) {
            leads[i].crm_details = leadCrm;
            break;
          }
        }
      });
    }

    if (campaignIds.length) {
      // get minimal campaign details
      const campaignsSnapshot = await db
        .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
        .where('id', 'in', campaignIds)
        .select(
          'id',
          'details.business_details',
          'friendly_name',
          'details.leadgen_form.questions',
        )
        .get();
      // match campaign and corresponding leads
      campaignsSnapshot.forEach((doc) => {
        for (let i = 0; i < leads.length; i++) {
          const campaignDetails = doc.data() as ICampaign;
          if (leads[i].campaign_id === campaignDetails.id) {
            leads[i].campaign_details = campaignDetails;
            break;
          }
        }
      });
    }

    return leads;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

// trigger point is meta leads webhook
export const triggerNotificationsForNewLeads = async (
  body: IMetaWebhookLeadEventBody,
  createdResponse: Array<{ id: string; created: boolean }>,
): Promise<ILeadsNotificationPayload[]> => {
  const notificationPayloads: ILeadsNotificationPayload[] = [];
  try {
    const changes: IWebhookLeadEventEntryChange[] = [];
    // club all entries from across pages
    body?.entry?.forEach((entry) => {
      entry.changes?.forEach((change) => {
        // only when lead is freshly created
        // e.g., to avoid duplicate noti when webhook is retriggered
        if (
          createdResponse.find((item) => item.id === change.value?.leadgen_id)
            ?.created
        ) {
          changes.push(change);
        }
      });
    });

    // for each change, get lead details & corresponding campaign details
    for (let i = 0; i < changes.length; i++) {
      const leadgenId = changes[i]?.value?.leadgen_id;
      const formId = changes[i]?.value?.form_id;
      const promises = [
        db
          .collection(FIRESTORE_COLLECTIONS.META_WEBHOOK_LEADS)
          .where('id', '==', leadgenId)
          .get(),
        db
          .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
          .where('meta_leadgen_form_id', '==', formId)
          .get(),
      ];
      const [leadDetailsSnapshot, campaignDetailsSnapshot] =
        await Promise.all(promises);
      const leadDetails = leadDetailsSnapshot.docs[0]?.data() as IMetaLead;
      const campaignDetails =
        campaignDetailsSnapshot.docs[0]?.data() as ICampaign;

      if (leadDetails && campaignDetails) {
        const parsedLead = {
          ...getParseLead(
            leadDetails,
            campaignDetails?.details?.leadgen_form?.questions ?? [],
          ),
          campaign_name:
            campaignDetails.friendly_name ??
            campaignDetails.details?.business_details?.business_category ??
            '',
          campaign_id: campaignDetails.meta_id ?? '',
          leadgen_id: leadDetails.id ?? '',
          business_mobile: campaignDetails.details?.business_details?.mobile,
          business_product_or_service_description:
            campaignDetails.details?.business_details
              ?.product_or_service_description,
        };
        // trigger conversion event, not checking campaignDetails?.details?.config?.meta_capi_enabled
        // because we have to send all leads to CAPI, lead coverage must be at least 60%
        void sendCapiLeadEventToMeta({
          lead_id: leadgenId,
          event_name: CapiLeadFunnelEvents.INITIAL_LEAD,
          campaign_id: campaignDetails.id,
        });
        notificationPayloads.push({
          uid: campaignDetails.uid,
          parsed_leads: [parsedLead],
        });
        // update campaign_id & uid in metaWebhookLeads for easy retrieval & filtering
        const updatedLeadDetails = {
          ...leadDetails,
          uid: campaignDetails.uid,
          campaign_id: campaignDetails.id,
        };
        void updateLeadFromWebhook(updatedLeadDetails);
        void initAgentEasyChatToLead(updatedLeadDetails);
      }
    }
  } catch (error) {
    logger.error(error);
  }
  notificationPayloads.forEach((payload) => {
    void sendNewLeadsEmail(payload);
    void sendLeadToPartnerViaWebhook(payload);
    void sendNewInstantFormLeadWhatsappNoti(payload);
  });
  return notificationPayloads;
};

export const getBusinessCategories = async (): Promise<string[]> => {
  try {
    const categories: string[] = [];
    const categoriesSnapshot = await db
      .collection(FIRESTORE_COLLECTIONS.BUSINESS_CATEGORIES)
      .get();
    categoriesSnapshot.forEach((doc) => {
      const data = doc.data() as Partial<IBusinessCategory>;
      if (data['business-category']) {
        categories.push(data['business-category']);
      }
    });
    return categories;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getOrders = async (
  queryParams: Record<string, string>,
  user?: IAuthUser,
): Promise<IOrderDetails[] | null> => {
  try {
    const campaignId = queryParams.campaign_id;
    const startDate = queryParams.start_date;
    const endDate = queryParams.end_date;

    let query = db
      .collection(FIRESTORE_COLLECTIONS.ORDERS)
      .where('status', '==', 'paid');

    if (campaignId) {
      // Campaign Invoices flow
      query = query.where('campaign_id', '==', campaignId);

      if (isGrowEasyAdmin(user) || isAdGlobalAiAdmin(user)) {
        // no uid filter, so that Admin can also see campaign orders of other users
      } else {
        query = query.where('uid', '==', `${user?.uid}`);
      }
    } else {
      // payment-history flow, all orders
      query = query.where('uid', '==', `${user?.uid}`);
    }
    if (startDate) {
      query = query.where('created_at', '>=', new Date(startDate));
    }
    if (endDate) {
      query = query.where('created_at', '<=', new Date(endDate));
    }
    const snapshot = await query.orderBy('created_at', 'desc').limit(50).get();
    const orders: IOrderDetails[] = [];
    snapshot.forEach((doc) => {
      orders.push(doc.data() as IOrderDetails);
    });
    return orders;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getBillingDetails = async (
  user?: IAuthUser,
): Promise<IBillingDetails | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const doc = await db
      .collection(FIRESTORE_COLLECTIONS.BILLING_DETAILS)
      .doc(user.uid)
      .get();
    return (doc.data() as IBillingDetails) ?? null;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const createOrUpdateBillingDetails = async (
  body: Partial<IBillingDetails>,
  user?: IAuthUser,
): Promise<DocumentData | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const documentRef = db
      .collection(FIRESTORE_COLLECTIONS.BILLING_DETAILS)
      .doc(user.uid);
    const billingDetailsSnapshot = await documentRef.get();
    const billingDetails = billingDetailsSnapshot.data();
    let dataToBeReturned: DocumentData | null = null;

    // merge data if billing info already exists
    // else create new record
    if (billingDetails) {
      // Some fields cannot be updated from FE
      delete body.uid;
      delete body.created_at;
      await documentRef.set(
        {
          ...body,
          updated_at: Timestamp.now(),
        },
        {
          merge: true, // caution: shallow merge only
        },
      );
      const updatedDoc = await documentRef.get();
      dataToBeReturned = updatedDoc.data() ?? null;
    } else {
      if (
        !body.country_code ||
        !body.billing_email ||
        !body.business_name ||
        !body.business_address ||
        !body.business_city ||
        !body.postal_code
      ) {
        throw new ValidationError(400, 'Missing required fields');
      }
      // create
      const data: Partial<IBillingDetails> = {
        ...body,
        uid: user.uid,
        created_at: Timestamp.now(),
        updated_at: Timestamp.now(),
      };

      await documentRef.set(data);
      dataToBeReturned = data;
    }
    // Update invoices created in less than 24 hours
    const currentDate = new Date();
    const last24HoursDate = new Date();
    last24HoursDate.setHours(currentDate.getHours() - 24);

    const last24HoursInvoices: IInvoiceDetails[] = [];
    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.INVOICES)
      .where('uid', '==', user.uid)
      .where('created_at', '>=', last24HoursDate)
      .get();
    snapshot.forEach((doc) => {
      const data = doc.data() as IInvoiceDetails;
      last24HoursInvoices.push(data);
    });
    const orderIds = last24HoursInvoices.map((item) => item.order_id);
    logger.info('last 24 hours invoices order IDs to be updated', orderIds);
    if (orderIds.length) {
      void generateInvoices({
        order_ids: orderIds,
      });
    }
    return dataToBeReturned;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const createOrderForExtendingCampaign = async (
  body: {
    campaign_id: string;
    campaign_extension_details: ICampaignExtensionDetails;
  },
  user?: IAuthUser,
): Promise<IOrderDetails | null> => {
  if (
    !body?.campaign_id ||
    !body.campaign_extension_details?.end_time ||
    !body.campaign_extension_details?.lifetime_budget ||
    !user?.uid
  ) {
    throw new ValidationError(400, 'Missing campaign_id/end_time/budget');
  }
  try {
    const doc = await getSingleCampaign(user?.uid, body.campaign_id);
    if (doc?.id) {
      const campaign = doc.data() as ICampaign;

      const currency =
        campaign?.details?.budget_and_scheduling?.currency ?? Currency.INR;

      // create order
      const existingLifetimeBudget = getCampaignLifetimeBudget(
        campaign?.details?.budget_and_scheduling,
      );
      const amount =
        body.campaign_extension_details.lifetime_budget -
        existingLifetimeBudget;
      const orderDetails = await createRazorpayOrder({
        amount,
        currency,
        campaign_id: campaign.id,
        uid: campaign.uid,
        type: GrowEasyOrderType.EXTEND,
        campaign_extension_details: body.campaign_extension_details,
        platform: AdPlatforms.META,
      });
      return orderDetails;
    } else {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const updateCampaignPostLaunch = async (
  body: Partial<ICampaign>,
  user?: IAuthUser,
): Promise<DocumentData | null> => {
  if (!body?.id || !user?.uid) {
    throw new ValidationError(400, 'Missing campaign Id');
  }
  try {
    const doc = await getSingleCampaign(user?.uid, body.id);
    if (doc?.id) {
      // only selected fields can be updated post launch
      const partialCampaign: Partial<ICampaign> = {
        updated_at: Timestamp.now(),
        friendly_name: body.friendly_name,
      };
      // merge
      await updateFirestoreCampaign(doc.id, partialCampaign);
      const updatedDoc = await db
        .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
        .doc(doc.id)
        .get();
      return updatedDoc.data() ?? null;
    } else {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

/**
 * Return account_details & assigned_pages only if accessToken is not expired
 * expiresIn = 0/null means non expiry token, It is the time of expiry in seconds
 */
export const getFbAssignedPages = async (
  queryParams: Record<string, any>,
  user?: IAuthUser,
): Promise<IProcessedFbLoginResponse | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const fbLoginRef = db
      .collection(FIRESTORE_COLLECTIONS.FB_LOGINS)
      .doc(user.uid);
    const fbLoginDoc = await fbLoginRef.get();
    const fbLoginDetails = fbLoginDoc.data() as IProcessedFbLoginDbData;

    if (!fbLoginDetails) {
      return null; // No stored data, return empty response
    }

    const tokenExpiryInSeconds =
      fbLoginDetails?.login_result?.authResponse?.expiresIn;
    const lastUpdatedAt = fbLoginDetails.updated_at?.toMillis?.(); // Convert Firestore timestamp to millis

    if (tokenExpiryInSeconds && lastUpdatedAt) {
      // check if token is not expired
      const expiryTime = lastUpdatedAt + tokenExpiryInSeconds * 1000; // Convert expiresIn to milliseconds
      if (Date.now() > expiryTime) {
        return null; // Token has expired
      }
    }
    return {
      account_details: fbLoginDetails?.account_details,
      assigned_pages: fbLoginDetails?.assigned_pages,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getAdCreditBalance = async (
  uid?: string,
): Promise<IAdCreditsBalance | null> => {
  if (!uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.AD_CREDITS)
      .doc(uid)
      .get();
    return snapshot.data() as IAdCreditsBalance;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getAdCreditTransactions = async (
  user?: IAuthUser,
): Promise<IAdCreditsTransaction[]> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.AD_CREDITS)
      .doc(user.uid)
      .collection(FIRESTORE_COLLECTIONS.TRANSACTIONS)
      .orderBy('created_at', 'desc')
      .limit(50)
      .get();
    const transactions: IAdCreditsTransaction[] = [];
    snapshot.forEach((doc) => {
      transactions.push(doc.data() as IAdCreditsTransaction);
    });
    return transactions;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getSelfAdAccountConfigs = async (params: {
  user?: IAuthUser;
  platform: AdPlatforms;
}): Promise<ISelfAdAccountConfigs[AdPlatforms] | undefined> => {
  const { user, platform } = params;

  if (!user?.uid) return undefined;

  const docRef = db
    .collection(FIRESTORE_COLLECTIONS.SELF_AD_ACCOUNT_CONFIGS)
    .doc(user.uid);

  const snapshot = await docRef.get();

  const data = snapshot.exists
    ? (snapshot.data() as ISelfAdAccountConfigs)
    : undefined;

  return data?.[platform];
};
