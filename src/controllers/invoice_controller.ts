import { Timestamp, type DocumentReference } from 'firebase-admin/firestore';
import {
  FIRESTORE_COLLECTIONS,
  GROW_EASY_PLATFORM_FEE_PERCENTAGE,
  INCREMENTAL_COUNTER_FIELDS,
} from '../constants';
import { auth, db } from '../modules/firebase_admin_sdk';
import logger from '../modules/logger';
import { GrowEasyPartners, type IAuthUser } from '../types';
import { Currency, type ICampaign } from '../types/campaign_details';
import {
  type IInvoiceDetails,
  type IBillingDetails,
  type IOrderDetails,
} from '../types/payments_invoices';
import ValidationError from '../utils/validation_error';
import {
  getIncrementalCounter,
  getSingleCampaign,
  isAdGlobalAiAdmin,
  isGrowEasyAdmin,
} from './util';
import { formatCurrencyAmount, getCurrencySymbol } from '../utils';

/**
 * 
 * Leadgen Services: ₹882.05
  
  Sub Total:	₹882.05
  GrowEasy Service Charge:	₹99.96
  GST on Service Charge:	₹17.99
  Total Taxable:	₹882.05
  SGST:	₹67.27
  CGST:	₹67.27
  IGST:	₹0.00
  Total Payable:	₹1000.00
 *
 */
// IGST for Inter-state movement i.e. different cities
// CGST & SGST: Intra state movement, i.e. same cities
// https://cleartax.in/s/what-is-sgst-cgst-igst
const updateInvoiceCalculationDetails = (payload: {
  orderDetails: IOrderDetails;
  campaign: ICampaign;
  billingDetails: Partial<IBillingDetails>;
  invoiceDetails: Partial<IInvoiceDetails>;
}): void => {
  const { orderDetails, campaign, billingDetails, invoiceDetails } = payload;

  const campaignBudgetInfo = campaign?.details?.budget_and_scheduling;

  // reverse billing
  const totalPayable =
    (orderDetails?.amount ?? 0) /
    ([Currency.IDR, Currency.VND].includes(orderDetails.currency) ? 1 : 100);
  const currencyIcon = getCurrencySymbol(orderDetails?.currency);
  invoiceDetails.total_payable = `${formatCurrencyAmount(
    totalPayable,
    orderDetails.currency,
  )}`;

  const totalDeduction =
    (totalPayable *
      (campaignBudgetInfo?.platform_fee_percentage ??
        GROW_EASY_PLATFORM_FEE_PERCENTAGE)) /
    100;
  const leadgenCostPassedToMeta = totalPayable - totalDeduction;

  if (invoiceDetails.partner === GrowEasyPartners.AD_GLOBAL_AI) {
    invoiceDetails.line_items?.push({
      description: 'Digital Marketing Services',
      amount: `${formatCurrencyAmount(
        totalPayable - totalDeduction,
        orderDetails.currency,
      )}`,
    });

    // Ad account is based out of India, so 18% tax to Meta
    const gst = leadgenCostPassedToMeta * 0.18;

    const partnerServiceCharge = totalDeduction - gst;

    const partnerServiceChargeRate = Math.round(
      (partnerServiceCharge / totalPayable) * 100,
    );

    // calculation_details
    invoiceDetails.calculation_details?.push(
      // subtotal = total amount - (tax + service charge)
      {
        key: 'Sub Total',
        value: `${formatCurrencyAmount(
          totalPayable - totalDeduction,
          orderDetails.currency,
        )}`,
      },
      {
        key: `AdGlobalAI Service Charge @ ${partnerServiceChargeRate}%`,
        value: `${formatCurrencyAmount(totalDeduction, orderDetails.currency)}`,
      },
      {
        key: `Tax paid to ${campaign?.platform} @ 18%`,
        value: `${formatCurrencyAmount(gst, orderDetails.currency)}`,
      },
    );
  } else {
    let sgst = 0; // State GST
    let cgst = 0; // Central Goods and Services Tax
    let igst = 0; // Integrated GST

    // GrowEasy address is UP, same city, CGST & SGST
    if (billingDetails?.state_code === 'UP') {
      cgst = leadgenCostPassedToMeta * 0.09;
      sgst = leadgenCostPassedToMeta * 0.09;
    } else {
      // inter city, IGST (Integrated GST)
      igst = leadgenCostPassedToMeta * 0.18;
    }
    // platform fee + 18% GST on platform fee
    const growEasyServiceChargeIncludingGst =
      totalDeduction - (sgst + cgst + igst);

    // platform fee only
    const growEasyServiceChargeExcludingGst =
      growEasyServiceChargeIncludingGst / 1.18;

    const growEasyServiceChargeRate = Math.round(
      (growEasyServiceChargeExcludingGst / totalPayable) * 100,
    );

    const gstOnServiceCharge =
      growEasyServiceChargeIncludingGst - growEasyServiceChargeExcludingGst;
    let serviceChargeSgst = 0;
    let serviceChargeCgst = 0;
    let serviceChargeIgst = 0;
    if (billingDetails?.state_code === 'UP') {
      // intra city
      serviceChargeCgst = gstOnServiceCharge / 2;
      serviceChargeSgst = gstOnServiceCharge / 2;
    } else {
      // inter city
      serviceChargeIgst = gstOnServiceCharge;
    }

    // line items
    invoiceDetails.line_items?.push({
      description: 'Digital Marketing Services',
      amount: `${currencyIcon}${(
        totalPayable - growEasyServiceChargeIncludingGst
      ).toFixed(2)}`,
    });

    // calculation_details
    invoiceDetails.calculation_details?.push(
      // subtotal = total amount - (tax + service charge)
      {
        key: 'Sub Total',
        value: `${currencyIcon}${(
          totalPayable -
          growEasyServiceChargeIncludingGst -
          (sgst + cgst + igst)
        ).toFixed(2)}`,
      },
      {
        key: `GrowEasy Service Charge @ ${growEasyServiceChargeRate}%`,
        value: `${currencyIcon}${growEasyServiceChargeExcludingGst.toFixed(2)}`,
      },
      {
        key: 'SGST on Service Charge',
        value: `${currencyIcon}${serviceChargeSgst.toFixed(2)}`,
      },
      {
        key: 'CGST on Service Charge',
        value: `${currencyIcon}${serviceChargeCgst.toFixed(2)}`,
      },
      {
        key: 'IGST on Service Charge',
        value: `${currencyIcon}${serviceChargeIgst.toFixed(2)}`,
      },
      {
        key: 'Total Taxable',
        value: `${currencyIcon}${(
          totalPayable -
          growEasyServiceChargeIncludingGst -
          (sgst + cgst + igst)
        ).toFixed(2)}`,
      },
      {
        key: 'SGST',
        value: `${currencyIcon}${sgst.toFixed(2)}`,
      },
      {
        key: 'CGST',
        value: `${currencyIcon}${cgst.toFixed(2)}`,
      },
      {
        key: 'IGST',
        value: `${currencyIcon}${igst.toFixed(2)}`,
      },
    );

    invoiceDetails.total_tax = {
      sgst: sgst + serviceChargeSgst,
      cgst: cgst + serviceChargeCgst,
      igst: igst + serviceChargeIgst,
    };
  }
};

export const createOrUpdateInvoice = async (payload: {
  orderDetails: IOrderDetails;
  campaign: ICampaign;
  billingDetails: Partial<IBillingDetails>;
}): Promise<IInvoiceDetails | null> => {
  const { campaign, billingDetails, orderDetails } = payload;

  const invoiceDocRef = orderDetails.invoice_id
    ? db.collection(FIRESTORE_COLLECTIONS.INVOICES).doc(orderDetails.invoice_id)
    : db.collection(FIRESTORE_COLLECTIONS.INVOICES).doc();
  const invoiceDetails: Partial<IInvoiceDetails> = {
    order_id: orderDetails.razorpay_order_id,
    campaign_id: campaign.id,
    id: invoiceDocRef.id,
    uid: campaign.uid,
    issue_date: Timestamp.now(),
    line_items: [],
    calculation_details: [],
    total_payable: '',
    billing_details: billingDetails,
    campaign_details: {
      business_category:
        campaign?.details?.business_details?.business_category ?? '',
      start_time: campaign?.details?.budget_and_scheduling?.start_time ?? '',
      end_time: campaign?.details?.budget_and_scheduling?.end_time ?? '',
      friendly_name: campaign?.friendly_name ?? '',
      product_or_service_description:
        campaign?.details?.business_details?.product_or_service_description ??
        '',
    },
    partner: campaign.details?.config?.partner ?? null,
  };
  if (orderDetails.invoice_id) {
    // use existing created_at & invoice_no
  } else {
    invoiceDetails.created_at = Timestamp.now();
    const invoiceNo = await getIncrementalCounter(
      INCREMENTAL_COUNTER_FIELDS.INVOICE,
    );
    invoiceDetails.invoice_no = invoiceNo;
  }

  updateInvoiceCalculationDetails({
    ...payload,
    invoiceDetails,
  });

  await invoiceDocRef.set(invoiceDetails, {
    merge: true,
  });
  const doc = await invoiceDocRef.get();
  return (doc.data() as IInvoiceDetails) ?? null;
};

export const generateInvoices = async (body: {
  order_ids: string[];
}): Promise<{
  invoice_ids: string[];
}> => {
  const { order_ids: orderIds } = body;
  if (!orderIds?.length) {
    throw new ValidationError(400, 'Please pass order_ids');
  }
  if (orderIds.length > 30) {
    // Firestore IN operator restriction
    throw new ValidationError(
      400,
      'List of order Ids cannot be greater than 30',
    );
  }
  const invoiceIds: string[] = [];
  try {
    // get orders
    const ordersSnapshot = await db
      .collection(FIRESTORE_COLLECTIONS.ORDERS)
      .where('razorpay_order_id', 'in', orderIds)
      .get();
    const orders: Array<{
      data: IOrderDetails;
      docRef: DocumentReference;
    }> = [];
    ordersSnapshot.forEach((doc) => {
      const data = doc.data() as IOrderDetails;
      orders.push({
        data,
        docRef: doc.ref,
      });
    });

    // for each order, get bill details, campaign details and generate invoice
    for (let i = 0; i < orders.length; i++) {
      const { data, docRef } = orders[i];
      const billingDetailsDoc = await db
        .collection(FIRESTORE_COLLECTIONS.BILLING_DETAILS)
        .doc(data.uid)
        .get();
      const userDetails = await auth.getUser(data.uid);
      const billingDetails = (billingDetailsDoc.data() as IBillingDetails) ?? {
        billing_email: userDetails?.email ?? '',
        billing_mobile: userDetails?.phoneNumber ?? '',
      };
      const campaignDoc = await getSingleCampaign(data.uid, data.campaign_id);
      const campaign = campaignDoc?.data() as ICampaign;
      if (
        (campaign?.details?.budget_and_scheduling?.platform_fee_percentage ??
          0) > 15.25
      ) {
        const invoiceDetails = await createOrUpdateInvoice({
          orderDetails: data,
          campaign,
          billingDetails,
        });
        logger.debug(invoiceDetails);
        const invoiceId = invoiceDetails?.id;
        if (invoiceId) {
          // write invoice id back to order
          const dataToBeUpdated: Partial<IOrderDetails> = {
            invoice_id: invoiceId,
            updated_at: Timestamp.now(),
          };
          await docRef.update(dataToBeUpdated);
          invoiceIds.push(invoiceId);
        }
      } else {
        logger.error(`Invalid or missing GST calculation for ${campaign?.id}`);
      }
    }
    return {
      invoice_ids: invoiceIds,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getInvoices = async (
  user?: IAuthUser,
): Promise<IInvoiceDetails[] | null> => {
  try {
    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.INVOICES)
      .where('uid', '==', `${user?.uid}`)
      .orderBy('created_at', 'desc')
      .limit(50)
      .get();
    const invoices: IInvoiceDetails[] = [];
    snapshot.forEach((doc) => {
      invoices.push(doc.data() as IInvoiceDetails);
    });
    return invoices;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getInvoiceDetails = async (
  invoiceId: string,
  user?: IAuthUser,
): Promise<IInvoiceDetails | null> => {
  const documentSnapshot = await db
    .collection(FIRESTORE_COLLECTIONS.INVOICES)
    .doc(invoiceId)
    .get();
  const invoiceDetails = (documentSnapshot?.data() as IInvoiceDetails) ?? null;
  // for non-admin, check for uid as well
  if (
    !(isGrowEasyAdmin(user) || isAdGlobalAiAdmin(user)) &&
    invoiceDetails?.uid !== user?.uid
  ) {
    throw new ValidationError(400, 'Bad invoice id');
  }
  return invoiceDetails;
};
