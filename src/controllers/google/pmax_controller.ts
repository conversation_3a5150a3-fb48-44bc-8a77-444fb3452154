import axios, { type AxiosError } from 'axios';
import { type Express } from 'express';
import fs from 'fs';
import {
  AD_GLOBAL_AI_PLATFORM_FEE_PERCENTAGE,
  GOOGLE_AD_ACCOUNT_ID,
  GROW_EASY_PLATFORM_FEE_PERCENTAGE,
  GROWEASY_S3_PUBLIC_BUCKET_NAME,
  S3_GROWEASY_GOOGLE_VIDEOS_UPLOAD_DIR,
} from '../../constants';
import {
  uploadVideoToYouTube,
  waitForVideoProcessing,
} from '../../modules/youtube';
import {
  AdPlatforms,
  GrowEasyPartners,
  type IAuthUser,
  type IGoogleAdsError,
} from '../../types';
import {
  Currency,
  type GoogleAdsMutateOperation,
  GrowEasyCampaignStatus,
  type IGoogleAdsData,
} from '../../types/campaign_details';
import { getDateDifferenceInDays } from '../../utils';
import ValidationError from '../../utils/validation_error';
import { getCampaignDetails } from '../db_controller';
import {
  addGrowEasyCampaignUtmParams,
  currencyToInrExchangeRate,
  getCampaignCurrencyBudgetNode,
  getCampaignLifetimeBudget,
  getFormattedDateAsPerGoogleAds,
  getGoogleAdsHeaders,
  getGoogleAdsUrl,
  getSingleCampaign,
  updateFirestoreCampaign,
} from '../util';
import logger from '../../modules/logger';
import { createRazorpayOrder } from '../payment_controller';
import { GrowEasyOrderType } from '../../types/payments_invoices';
import {
  executeMutateOperations,
  getConversionActionEventLabel,
  updateGoogleAdsDataToCampaign,
  useCustomGoalInCampaign,
} from './google_controller';
import { Timestamp } from 'firebase-admin/firestore';
import { uploadFileToS3 } from '../../modules/aws/s3';

const processPMaxCampaignKeywords = async (params: {
  keywords: string[];
  assetGroupResourceName: string;
  adAccountId?: string;
}): Promise<void> => {
  const { keywords, assetGroupResourceName, adAccountId } = params;

  logger.info(`Creating Signal for ${keywords.length} keywords`);

  for (const keyword of keywords) {
    const operation: GoogleAdsMutateOperation = {
      assetGroupSignalOperation: {
        create: {
          assetGroup: assetGroupResourceName,
          searchTheme: {
            text: keyword,
          },
        },
      },
    };
    try {
      // Try to create search theme signal
      await executeMutateOperations([operation], adAccountId);
      logger.info(`Signal creation successful for keyword: ${keyword}`);
    } catch (error) {
      logger.info(`Signal creation failed for keyword: ${keyword}`);
      const errorResponse = (
        (error as AxiosError)?.response?.data as { error: IGoogleAdsError }
      )?.error;

      // Check if the error is a policy violation
      logger.debug(errorResponse);
      if (errorResponse?.details) {
        const violations = errorResponse.details.filter(
          (item) =>
            item.errors?.[0]?.errorCode?.assetGroupSignalError ===
            'SEARCH_THEME_POLICY_VIOLATION',
        );
        if (violations[0]?.errors?.[0]?.details?.policyViolationDetails) {
          const { key, isExemptible } =
            violations[0].errors[0].details.policyViolationDetails;
          if (isExemptible) {
            // Add exemption keys to the request
            operation.assetGroupSignalOperation.exemptPolicyViolationKeys = [
              key,
            ];
            logger.info(
              `Exempt allowed for keyword: ${keyword}, attempting to add it again`,
            );
            try {
              await executeMutateOperations([operation], adAccountId);
              logger.info(
                `Signal creation successful after exempt for keyword: ${keyword}`,
              );
            } catch (error) {
              logger.error((error as AxiosError)?.response?.data);
              logger.error(
                `Signal creation failed even after exempt for keyword: ${keyword}`,
              );
            }
          } else {
            logger.info(`Non-exemptible violation for keyword: ${keyword}`);
          }
        }
      }
    }
  }
};

// create Performance Max campaign in Paused mode
export const createPMaxCampaign = async (
  data: {
    campaign_id: string;
  },
  user?: IAuthUser,
): Promise<{
  google_ads_data: IGoogleAdsData;
  order_id: string;
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const campaignId = data.campaign_id;

  const campaignDoc = await getSingleCampaign(user?.uid, campaignId);

  const campaignDetails = await getCampaignDetails(campaignId, user);

  const salesLandingPageUrl = addGrowEasyCampaignUtmParams({
    url: campaignDetails?.details?.business_details?.website ?? '',
    campaignId,
    platform: campaignDetails.platform,
  });
  const budgetAndScheduling = campaignDetails?.details?.budget_and_scheduling;

  if (!campaignDoc || !budgetAndScheduling?.end_time || !salesLandingPageUrl) {
    throw new ValidationError(
      400,
      'Campaign Not found or Missing permission or Missing Budget details or Missing Landing Page URL',
    );
  }

  try {
    // call generateAdAssets first before calling this controller
    if (
      !campaignDetails?.google_ads_data?.media_assets ||
      !campaignDetails?.google_ads_data?.text_assets
    ) {
      throw new ValidationError(
        400,
        'Campaign has mising Google Ad media or text assets',
      );
    }

    const currency =
      campaignDetails?.details?.budget_and_scheduling?.currency ?? Currency.INR;
    const campaignCurrencyBudgetNode = getCampaignCurrencyBudgetNode(
      campaignDetails?.details?.budget_and_scheduling,
    );
    if (currency !== Currency.INR) {
      if (campaignCurrencyBudgetNode) {
        // calculation will be made in createAdset and this rate will be saved in Firestore
        campaignCurrencyBudgetNode.exchange_rate =
          await currencyToInrExchangeRate(currency);
      }
    }

    if (campaignDetails.details?.budget_and_scheduling) {
      campaignDetails.details.budget_and_scheduling.platform_fee_percentage =
        campaignDetails.details?.config?.partner ===
        GrowEasyPartners.AD_GLOBAL_AI
          ? AD_GLOBAL_AI_PLATFORM_FEE_PERCENTAGE
          : GROW_EASY_PLATFORM_FEE_PERCENTAGE;
    }

    const googleAdsData = campaignDetails.google_ads_data;
    // in INR (0 decimal currencies) or paise (2 decimal currencies)
    let dailyBudget =
      (campaignCurrencyBudgetNode?.daily_budget ?? 0) *
      (campaignCurrencyBudgetNode?.exchange_rate ?? 1);

    // deduct platform fee
    dailyBudget = Math.round(
      dailyBudget - (dailyBudget * GROW_EASY_PLATFORM_FEE_PERCENTAGE) / 100,
    );

    const campaignStartDate = new Date(budgetAndScheduling?.start_time);
    const campaignEndDate = new Date(budgetAndScheduling?.end_time);
    // Google will process date in IST (account settings) and new Date() returns machine date
    const currentDate = new Date();
    const daysDifference = getDateDifferenceInDays(
      campaignStartDate,
      campaignEndDate,
    );
    const effectiveCampaignEndDate = new Date(currentDate);
    effectiveCampaignEndDate.setDate(currentDate.getDate() + daysDifference);

    const adAccountId =
      campaignDetails?.details?.config?.google_ad_account_id ??
      GOOGLE_AD_ACCOUNT_ID;

    const campaignResourceName = `customers/${adAccountId}/campaigns/-2`;
    const assetGroupResourceName = `customers/${adAccountId}/assetGroups/-3`;
    const conversionActionResourceName = `customers/${adAccountId}/conversionActions/-4`;

    const mutateOperations: GoogleAdsMutateOperation[] = [
      {
        campaignBudgetOperation: {
          create: {
            resourceName: `customers/${adAccountId}/campaignBudgets/-1`,
            name: campaignDetails.name,
            deliveryMethod: 'STANDARD',
            // division by 100 because amount is in paise, multiplication by 10^6 because amount has to be in micro
            amountMicros:
              dailyBudget *
              (1000000 /
                ([Currency.IDR, Currency.VND].includes(currency) ? 1 : 100)),
            explicitlyShared: false, // The budget won’t be available for other campaigns, i.e. no budget sharing
          },
        },
      },
      {
        campaignOperation: {
          create: {
            resourceName: campaignResourceName,
            status: 'PAUSED',
            advertisingChannelType: 'PERFORMANCE_MAX',
            maximizeConversions: {},
            urlExpansionOptOut: true, // target only final url
            name: campaignDetails.name,
            campaignBudget: `customers/${adAccountId}/campaignBudgets/-1`,
            startDate: getFormattedDateAsPerGoogleAds(currentDate),
            endDate: getFormattedDateAsPerGoogleAds(effectiveCampaignEndDate),
            containsEuPoliticalAdvertising:
              'DOES_NOT_CONTAIN_EU_POLITICAL_ADVERTISING',
          },
        },
      },
      ...(campaignDetails?.google_ads_data?.geo_locations ?? []).map((item) => {
        return {
          campaignCriterionOperation: {
            create: {
              campaign: campaignResourceName,
              location: {
                geoTargetConstant: item.geoTargetConstant?.resourceName,
              },
            },
          },
        };
      }),
      {
        assetGroupOperation: {
          create: {
            name: campaignDetails.name,
            resourceName: assetGroupResourceName,
            campaign: campaignResourceName,
            finalUrls: [salesLandingPageUrl],
          },
        },
      },
    ];

    // conversion action only if it is missing
    if (!googleAdsData?.conversion_action_resource) {
      mutateOperations.push(
        {
          conversionActionOperation: {
            create: {
              resourceName: conversionActionResourceName,
              name: campaignDetails.name,
              type: 'WEBPAGE',
              category: 'PURCHASE',
              status: 'ENABLED',
              valueSettings: {
                defaultValue: 1.0,
                alwaysUseDefaultValue: true,
              },
              countingType: 'ONE_PER_CLICK',
              attributionModelSettings: {
                attributionModel: 'GOOGLE_ADS_LAST_CLICK',
              },
            },
          },
        },
        {
          customConversionGoalOperation: {
            create: {
              name: campaignDetails.name,
              conversionActions: [conversionActionResourceName],
            },
          },
        },
      );
    }

    // push media assets
    if (googleAdsData.media_assets) {
      const fieldTypes = Object.keys(googleAdsData.media_assets) as Array<
        keyof typeof googleAdsData.media_assets
      >;
      for (const fieldType of fieldTypes) {
        const assets = googleAdsData.media_assets?.[fieldType];
        assets.forEach((asset) => {
          mutateOperations.push({
            assetGroupAssetOperation: {
              create: {
                assetGroup: assetGroupResourceName,
                asset: asset.resource_name,
                fieldType,
              },
            },
          });
        });
      }
    }

    // push text assets
    if (googleAdsData.text_assets) {
      const fieldTypes = Object.keys(googleAdsData.text_assets) as Array<
        keyof typeof googleAdsData.text_assets
      >;
      for (const fieldType of fieldTypes) {
        const assets = googleAdsData.text_assets?.[fieldType];
        assets.forEach((asset) => {
          mutateOperations.push({
            assetGroupAssetOperation: {
              create: {
                assetGroup: assetGroupResourceName,
                asset: asset.resource_name,
                fieldType,
              },
            },
          });
        });
      }
    }

    /* console.dir(mutateOperations, {
      depth: null
    }) */

    // Wait for Youtube video to get processed
    const youtubeVideoId = googleAdsData.ad_videos?.[0]?.youtube_video_id;
    if (youtubeVideoId) {
      await waitForVideoProcessing(youtubeVideoId);
    }

    const url = getGoogleAdsUrl('googleAds:mutate', adAccountId);
    const headers = await getGoogleAdsHeaders();
    const response = await axios.post(
      url.href,
      {
        mutateOperations,
      },
      {
        headers,
      },
    );
    const operationResponses = response.data?.mutateOperationResponses as Array<
      Record<
        string,
        {
          resourceName: string;
        }
      >
    >;
    logger.debug(operationResponses);

    for (const response of operationResponses) {
      const type = Object.keys(response)[0];
      if (type === 'campaignResult') {
        googleAdsData.campaign_resource = response[type]?.resourceName;
      } else if (type === 'campaignBudgetResult') {
        googleAdsData.campaign_budget_resource = response[type]?.resourceName;
      } else if (type === 'conversionActionResult') {
        googleAdsData.conversion_action_resource = response[type]?.resourceName;
      } else if (type === 'assetGroupResult') {
        googleAdsData.asset_group_resource = response[type]?.resourceName;
      } else if (type === 'customConversionGoalResult') {
        googleAdsData.custom_conversion_goal_resource =
          response[type]?.resourceName;
      }
    }

    // create order for making payment
    const amount = getCampaignLifetimeBudget(budgetAndScheduling);
    const orderCreationResponseData = await createRazorpayOrder({
      amount,
      currency: budgetAndScheduling?.currency,
      campaign_id: campaignDetails.id,
      uid: campaignDetails.uid,
      type: GrowEasyOrderType.LAUNCH,
      platform: AdPlatforms.GOOGLE,
    });
    logger.debug(
      `orderCreationResponseData for ${campaignDetails.id}`,
      orderCreationResponseData,
    );

    const orderId = orderCreationResponseData.razorpay_order_id;
    // googleAdsData.lead_form_url = leadFormUrl;

    // updating Firestore data to be in sync with Google ads manager
    if (campaignDetails.details?.budget_and_scheduling) {
      campaignDetails.details.budget_and_scheduling.start_time =
        currentDate.toISOString();
      campaignDetails.details.budget_and_scheduling.end_time =
        effectiveCampaignEndDate.toISOString();
    }

    // process search theme text/keywords signals asynchronously at last, since it can fail for policy violations and takes time
    if (
      googleAdsData.asset_group_resource &&
      campaignDetails?.google_ads_data?.search_keywords?.length
    ) {
      void processPMaxCampaignKeywords(
        // Google restriction for P-Max: unique and max 25 unique keywords
        {
          keywords: Array.from(
            new Set(campaignDetails?.google_ads_data?.search_keywords ?? []),
          ).slice(0, 25),
          assetGroupResourceName: googleAdsData.asset_group_resource,
          adAccountId,
        },
      );
    }

    // Use newly created custom goal in campaign
    if (
      googleAdsData.custom_conversion_goal_resource &&
      googleAdsData.campaign_resource
    ) {
      await useCustomGoalInCampaign({
        customConversionGoalResourceName:
          googleAdsData.custom_conversion_goal_resource,
        campaignResourceName: googleAdsData.campaign_resource,
        adAccountId,
      });
    }

    // populate event label to fire event in lead form
    try {
      googleAdsData.conversion_action_event_label =
        await getConversionActionEventLabel(
          googleAdsData.conversion_action_resource ?? '',
        );
    } catch (error) {
      logger.error('Failed to populate conversion action event label');
    }

    // update this data back to campaign
    await updateGoogleAdsDataToCampaign(campaignDoc.id, googleAdsData);
    await updateFirestoreCampaign(campaignDoc.id, {
      details: campaignDetails.details,
      updated_at: Timestamp.now(),
      status: GrowEasyCampaignStatus.PAUSED, // make it Active post payment
      order_id: orderId,
    });

    return {
      google_ads_data: googleAdsData,
      order_id: orderId,
    };
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const uploadAdVideo = async (
  file: Express.Multer.File,
  queryParams?: Record<string, any>,
  user?: IAuthUser,
): Promise<{
  video_url?: string;
  youtube_video_id?: string;
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const filePath = file.path;
  logger.info('uploadAdVideo.file.path', filePath);

  try {
    const promises = [
      uploadFileToS3(
        GROWEASY_S3_PUBLIC_BUCKET_NAME,
        filePath,
        `${S3_GROWEASY_GOOGLE_VIDEOS_UPLOAD_DIR}/${file.filename}`,
      ),
    ];

    const [s3Response] = await Promise.all(promises);
    let youtubeVideoId: string | undefined;

    try {
      // try-catch so that quota limit does not affect video processing
      const campaignId = queryParams?.campaign_id;
      const campaignDetails = await getCampaignDetails(campaignId, user);
      youtubeVideoId =
        (await uploadVideoToYouTube({
          videoPath: filePath,
          title: campaignDetails.name.substring(0, 100), // title has max chars limit 100
          description:
            campaignDetails.details?.business_details
              ?.product_or_service_description ?? '',
        })) ?? '';
    } catch (error) {
      logger.error('YouTube upload failed:', error);
    }

    await fs.promises.unlink(filePath);

    return {
      video_url: s3Response,
      youtube_video_id: youtubeVideoId,
    };
  } catch (error) {
    logger.error('Video upload failed:', error);
    await fs.promises.unlink(filePath);
    throw error;
  }
};
