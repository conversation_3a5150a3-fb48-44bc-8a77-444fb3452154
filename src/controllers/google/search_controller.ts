import axios, { type AxiosError } from 'axios';
import {
  AD_GLOBAL_AI_PLATFORM_FEE_PERCENTAGE,
  GOOGLE_AD_ACCOUNT_ID,
  GROW_EASY_PLATFORM_FEE_PERCENTAGE,
} from '../../constants';
import {
  AdPlatforms,
  GrowEasyPartners,
  type IAuthUser,
  type IGoogleAdsError,
} from '../../types';
import {
  Currency,
  type GoogleAdsMutateOperation,
  GROWEASY_CAMPAIGN_TYPE,
  GrowEasyCampaignStatus,
  type ICampaign,
  type IGoogleAdsData,
} from '../../types/campaign_details';
import { getDateDifferenceInDays } from '../../utils';
import ValidationError from '../../utils/validation_error';
import { getCampaignDetails } from '../db_controller';
import {
  addGrowEasyCampaignUtmParams,
  currencyToInrExchangeRate,
  getCampaignCurrencyBudgetNode,
  getCampaignLifetimeBudget,
  getFormattedDateAsPerGoogleAds,
  getGoogleAdsHeaders,
  getGoogleAdsUrl,
  getSingleCampaign,
  updateFirestoreCampaign,
} from '../util';
import logger from '../../modules/logger';
import { createRazorpayOrder } from '../payment_controller';
import { GrowEasyOrderType } from '../../types/payments_invoices';
import {
  executeMutateOperations,
  getConversionActionEventLabel,
  updateGoogleAdsDataToCampaign,
  useCustomGoalInCampaign,
} from './google_controller';
import { Timestamp } from 'firebase-admin/firestore';
import COUNTRIES from '../../constants/countries';

const processSearchCampaignKeywords = async (params: {
  keywords: string[];
  adGroupResourceName: string;
  adAccountId?: string;
}): Promise<void> => {
  const { keywords, adGroupResourceName, adAccountId } = params;

  logger.info(`Creating keyword for ${keywords.length} keywords`);

  for (const keyword of keywords) {
    const operation: GoogleAdsMutateOperation = {
      adGroupCriterionOperation: {
        create: {
          adGroup: adGroupResourceName,
          status: 'ENABLED',
          keyword: {
            text: keyword,
            matchType: 'PHRASE',
          },
        },
      },
    };
    try {
      // Try to create search theme signal
      await executeMutateOperations([operation], adAccountId);
      logger.info(`Keyword creation successful for keyword: ${keyword}`);
    } catch (error) {
      logger.info(`Keyword creation failed for keyword: ${keyword}`);
      const errorResponse = (
        (error as AxiosError)?.response?.data as { error: IGoogleAdsError }
      )?.error;

      // Check if the error is a policy violation
      logger.debug(errorResponse);
      if (errorResponse?.details) {
        const violations = errorResponse.details.filter(
          (item) =>
            item.errors?.[0]?.errorCode?.assetGroupSignalError ===
            'SEARCH_THEME_POLICY_VIOLATION',
        );
        if (violations[0]?.errors?.[0]?.details?.policyViolationDetails) {
          const { key, isExemptible } =
            violations[0].errors[0].details.policyViolationDetails;
          if (isExemptible) {
            // Add exemption keys to the request
            operation.adGroupCriterionOperation.exemptPolicyViolationKeys = [
              key,
            ];
            logger.info(
              `Exempt allowed for keyword: ${keyword}, attempting to add it again`,
            );
            try {
              await executeMutateOperations([operation], adAccountId);
              logger.info(
                `Signal creation successful after exempt for keyword: ${keyword}`,
              );
            } catch (error) {
              logger.error((error as AxiosError)?.response?.data);
              logger.error(
                `Signal creation failed even after exempt for keyword: ${keyword}`,
              );
            }
          } else {
            logger.info(`Non-exemptible violation for keyword: ${keyword}`);
          }
        }
      }
    }
  }
};

/* const createDemographicMutateOperations = (adGroupResourceName: string, params: {
  ageMin: number;
  ageMax: number;
  genders?: number[]
}) => {
  const { ageMin, ageMax, genders} = params;

  const mutateOperations: GoogleAdsMutateOperation[] = [];

  // Define Google Ads age ranges
  const ageRanges = [
    { id: "AGE_RANGE_18_24", min: 18, max: 24 },
    { id: "AGE_RANGE_25_34", min: 25, max: 34 },
    { id: "AGE_RANGE_35_44", min: 35, max: 44 },
    { id: "AGE_RANGE_45_54", min: 45, max: 54 },
    { id: "AGE_RANGE_55_64", min: 55, max: 64 },
    { id: "AGE_RANGE_65_UP", min: 65, max: 120 },
  ];

  // Filter age ranges based on min and max
  const validAgeRanges = ageRanges.filter(
    (range) => range.min >= ageMin && range.max <= ageMax
  );

  // Add age targeting criteria
  validAgeRanges.forEach((range) => {
    mutateOperations.push({
      adGroupCriterionOperation: {
        create: {
          adGroup: adGroupResourceName,
          status: 'ENABLED',
          ageRange: {
            type: range.id,
          },
        },
      },
    });
  });

  // Add gender targeting criteria if genders are specified
  const genderMap: {
    [key: number]: string
  } = {
    1: "MALE",
    2: "FEMALE"
  };

  if (genders?.length) {
    genders.forEach((gender) => {
      if (genderMap[gender]) {
        mutateOperations.push({
          adGroupCriterionOperation: {
            create: {
              adGroup: adGroupResourceName,
              status: 'ENABLED',
              gender: {
                type: genderMap[gender],
              },
            },
          },
        });
      }
    });
  }

  return mutateOperations;
} */

const createCallExtensionAsset = async (
  campaignDetails: ICampaign,
): Promise<
  Array<
    Record<
      string,
      {
        resourceName: string;
      }
    >
  >
> => {
  logger.info('createCallExtensionAsset called');
  const adAccountId =
    campaignDetails?.details?.config?.google_ad_account_id ??
    GOOGLE_AD_ACCOUNT_ID;
  const businessDetails = campaignDetails.details?.business_details;
  const countryCode =
    COUNTRIES.find((item) => item.dial_code === businessDetails?.country_code)
      ?.code ?? '';
  const phoneNumber = businessDetails?.mobile_without_country_code ?? '';
  const conversionActionResourceName =
    campaignDetails?.google_ads_data?.conversion_action_resource;
  const campaignResourceName =
    campaignDetails.google_ads_data?.campaign_resource;
  const callExtensionAssetResourceName = `customers/${adAccountId}/assets/-1`;

  if (
    conversionActionResourceName &&
    campaignResourceName &&
    phoneNumber &&
    countryCode
  ) {
    const mutateOperations: GoogleAdsMutateOperation[] = [];
    // 1. Create call asset
    mutateOperations.push({
      assetOperation: {
        create: {
          resourceName: callExtensionAssetResourceName,
          callAsset: {
            countryCode,
            phoneNumber,
            callConversionAction: conversionActionResourceName,
            callConversionReportingState:
              'USE_RESOURCE_LEVEL_CALL_CONVERSION_ACTION',
          },
        },
      },
    });

    // 2. Attach asset to campaign
    mutateOperations.push({
      campaignAssetOperation: {
        create: {
          campaign: campaignResourceName,
          asset: callExtensionAssetResourceName,
          fieldType: 'CALL',
        },
      },
    });

    const response = await executeMutateOperations(
      mutateOperations,
      adAccountId,
    );
    return response;
  } else {
    logger.error(
      'Missing conversionActionResourceName or phoneNumber or countryCode while creating call extension',
    );
    return [];
  }
};

// create Search campaign in Paused mode
export const createSearchOrCallCampaign = async (
  data: {
    campaign_id: string;
  },
  user?: IAuthUser,
): Promise<{
  google_ads_data: IGoogleAdsData;
  order_id: string;
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const campaignId = data.campaign_id;
  logger.info(`createSearchOrCallCampaign called for ${campaignId}`);

  const campaignDoc = await getSingleCampaign(user?.uid, campaignId);

  const campaignDetails = await getCampaignDetails(campaignId, user);
  const businessDetails = campaignDetails?.details?.business_details;
  const leadFormUrl = addGrowEasyCampaignUtmParams({
    url: campaignDetails?.google_ads_data?.lead_form_url ?? '',
    campaignId,
    platform: campaignDetails.platform,
  });
  const isSearchCampaign =
    campaignDetails.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH;
  const isCallCampaign =
    campaignDetails.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL;
  const budgetAndScheduling = campaignDetails?.details?.budget_and_scheduling;

  const selfAdAccountFlow =
    !!campaignDetails?.details?.config?.self_ad_account_configs?.ad_account_id;

  if (!campaignDoc || !budgetAndScheduling?.end_time || !leadFormUrl) {
    throw new ValidationError(
      400,
      'Campaign Not found or Missing permission or Missing Budget details or Missing Lead form URL',
    );
  }

  try {
    // call generateTextAssets first before calling this controller
    if (!campaignDetails?.google_ads_data?.text_assets) {
      throw new ValidationError(
        400,
        'Campaign has mising Google Ad text assets',
      );
    }

    const currency =
      campaignDetails?.details?.budget_and_scheduling?.currency ?? Currency.INR;
    const campaignCurrencyBudgetNode = getCampaignCurrencyBudgetNode(
      campaignDetails?.details?.budget_and_scheduling,
    );
    if (currency !== Currency.INR) {
      if (campaignCurrencyBudgetNode) {
        // calculation will be made in createAdset and this rate will be saved in Firestore
        campaignCurrencyBudgetNode.exchange_rate =
          await currencyToInrExchangeRate(currency);
      }
    }

    if (campaignDetails.details?.budget_and_scheduling) {
      campaignDetails.details.budget_and_scheduling.platform_fee_percentage =
        campaignDetails.details?.config?.partner ===
        GrowEasyPartners.AD_GLOBAL_AI
          ? AD_GLOBAL_AI_PLATFORM_FEE_PERCENTAGE
          : GROW_EASY_PLATFORM_FEE_PERCENTAGE;
    }

    const googleAdsData = campaignDetails.google_ads_data;
    // in INR (0 decimal currencies) or paise/cents etc (2 decimal currencies)
    let dailyBudget = campaignCurrencyBudgetNode?.daily_budget ?? 0;

    if (selfAdAccountFlow) {
      // for self ad account, do not convert currency to paise & do not deduct platform fee
    } else {
      dailyBudget *= campaignCurrencyBudgetNode?.exchange_rate ?? 1;

      // deduct platform fee
      dailyBudget = Math.round(
        dailyBudget - (dailyBudget * GROW_EASY_PLATFORM_FEE_PERCENTAGE) / 100,
      );
    }

    const campaignStartDate = new Date(budgetAndScheduling?.start_time);
    const campaignEndDate = new Date(budgetAndScheduling?.end_time);
    // Google will process date in IST (account settings) and new Date() returns machine date
    const currentDate = new Date();
    const daysDifference = getDateDifferenceInDays(
      campaignStartDate,
      campaignEndDate,
    );
    const effectiveCampaignEndDate = new Date(currentDate);
    effectiveCampaignEndDate.setDate(currentDate.getDate() + daysDifference);

    const adAccountId =
      campaignDetails?.details?.config?.google_ad_account_id ??
      GOOGLE_AD_ACCOUNT_ID;

    const campaignResourceName = `customers/${adAccountId}/campaigns/-2`;
    const adGroupResourceName = `customers/${adAccountId}/adGroups/-3`;
    const conversionActionResourceName = `customers/${adAccountId}/conversionActions/-4`;

    // for debugging
    logger.info(
      'Campaign start date & campaign end date:',
      currentDate.toISOString(),
      effectiveCampaignEndDate.toISOString(),
      getFormattedDateAsPerGoogleAds(currentDate),
      new Date().toISOString(),
    );

    const mutateOperations: GoogleAdsMutateOperation[] = [
      {
        campaignBudgetOperation: {
          create: {
            resourceName: `customers/${adAccountId}/campaignBudgets/-1`,
            name: campaignDetails.name,
            deliveryMethod: 'STANDARD',
            // division by 100 because amount is in paise, multiplication by 10^6 because amount has to be in micro
            amountMicros:
              dailyBudget *
              (1000000 /
                ([Currency.IDR, Currency.VND].includes(currency) ? 1 : 100)),
            explicitlyShared: false, // The budget won’t be available for other campaigns, i.e. no budget sharing
          },
        },
      },
      {
        campaignOperation: {
          create: {
            resourceName: campaignResourceName,
            status: selfAdAccountFlow ? 'ENABLED' : 'PAUSED',
            advertisingChannelType: 'SEARCH',
            name: campaignDetails.name,
            campaignBudget: `customers/${adAccountId}/campaignBudgets/-1`,
            startDate: getFormattedDateAsPerGoogleAds(currentDate),
            endDate: getFormattedDateAsPerGoogleAds(effectiveCampaignEndDate),
            networkSettings: {
              targetGoogleSearch: true,
              targetSearchNetwork: false,
              targetContentNetwork: false,
              targetPartnerSearchNetwork: false,
            },
            containsEuPoliticalAdvertising:
              'DOES_NOT_CONTAIN_EU_POLITICAL_ADVERTISING',
            ...(isSearchCampaign
              ? {
                  maximizeConversions: {
                    // values like cpcBidCeilingMicros can come here
                  },
                }
              : {
                  // comment maximizeConversions node & uncomment targetSpend to make bidding strategy maximize clicks
                  // targetSpend: {},
                  maximizeConversions: {},
                }),
          },
        },
      },
      ...(campaignDetails?.google_ads_data?.geo_locations ?? []).map((item) => {
        return {
          campaignCriterionOperation: {
            create: {
              campaign: campaignResourceName,
              location: {
                geoTargetConstant: item.geoTargetConstant?.resourceName,
              },
            },
          },
        };
      }),
      {
        adGroupOperation: {
          create: {
            name: campaignDetails.name,
            resourceName: adGroupResourceName,
            campaign: campaignResourceName,
            status: 'ENABLED',
          },
        },
      },
      // by default all age groups & genders are included, use negative to exclude age / gender
      /* ...createDemographicMutateOperations(adGroupResourceName, {
        ageMin: campaignDetails?.details?.targeting?.age_min ?? 18,
        ageMax: campaignDetails?.details?.targeting?.age_max ?? 65,
        genders: campaignDetails?.details?.targeting?.genders
      }), */
    ];

    // looks like API support is not yet available to add business name & logo in responsive search ad
    /* if (googleAdsData?.media_assets?.LOGO?.[0]?.resource_name) {
      mutateOperations.push({
        campaignAssetOperation: {
          create: {
            asset: googleAdsData?.media_assets?.LOGO?.[0]?.resource_name,
            campaign: campaignResourceName,
            fieldType: 'LOGO',
          }
        }
      })
    } */

    // conversion action only if it is missing
    // https://developers.google.com/google-ads/api/docs/conversions/categories#ad_call
    if (!googleAdsData?.conversion_action_resource) {
      mutateOperations.push(
        {
          conversionActionOperation: {
            create: {
              resourceName: conversionActionResourceName,
              name: campaignDetails.name,
              type: isCallCampaign ? 'AD_CALL' : 'WEBPAGE',
              category: isCallCampaign ? 'PHONE_CALL_LEAD' : 'SUBMIT_LEAD_FORM',
              status: 'ENABLED',
              valueSettings: {
                defaultValue: 1.0,
                alwaysUseDefaultValue: true,
              },
              countingType: 'ONE_PER_CLICK',
              attributionModelSettings: {
                attributionModel: 'GOOGLE_ADS_LAST_CLICK',
              },
            },
          },
        },
        {
          customConversionGoalOperation: {
            create: {
              name: campaignDetails.name,
              conversionActions: [conversionActionResourceName],
            },
          },
        },
      );
    }

    // push text assets
    if (googleAdsData.text_assets) {
      const headlines = googleAdsData.text_assets?.HEADLINE;
      const descriptions = googleAdsData.text_assets?.DESCRIPTION;

      if (isSearchCampaign) {
        // https://developers.google.com/google-ads/api/rest/reference/rest/v18/Ad#responsivesearchadinfo
        mutateOperations.push({
          adGroupAdOperation: {
            create: {
              adGroup: adGroupResourceName,
              status: 'ENABLED',
              ad: {
                responsiveSearchAd: {
                  // max 15 headlines
                  headlines: headlines
                    .slice(0, 15)
                    .map((item) => ({ text: item.text })),
                  // max 4 descriptions
                  descriptions: descriptions
                    .slice(0, 4)
                    .map((item) => ({ text: item.text })),
                },
                finalUrls: [leadFormUrl],
              },
            },
          },
        });
      } else if (isCallCampaign) {
        // https://developers.google.com/google-ads/api/rest/reference/rest/v18/Ad#calladinfo
        mutateOperations.push({
          adGroupAdOperation: {
            create: {
              adGroup: adGroupResourceName,
              status: 'ENABLED',
              ad: {
                callAd: {
                  businessName: businessDetails?.business_name ?? '',
                  headline1: headlines[0].text,
                  headline2: headlines[1].text,
                  description1: descriptions[0].text,
                  description2: descriptions[1].text,
                  countryCode:
                    COUNTRIES.find(
                      (item) =>
                        item.dial_code === businessDetails?.country_code,
                    )?.code ?? '',
                  phoneNumber:
                    businessDetails?.mobile_without_country_code ?? '',
                  phoneNumberVerificationUrl: leadFormUrl,
                  callTracked: true,
                  disableCallConversion: false,
                },
                finalUrls: [],
              },
            },
          },
        });
      }
    }

    /* console.dir(mutateOperations, {
      depth: null,
    }); */

    const url = getGoogleAdsUrl('googleAds:mutate', adAccountId);
    const headers = await getGoogleAdsHeaders();
    const response = await axios.post(
      url.href,
      {
        mutateOperations,
      },
      {
        headers,
      },
    );
    const operationResponses = response.data?.mutateOperationResponses as Array<
      Record<
        string,
        {
          resourceName: string;
        }
      >
    >;
    logger.debug(operationResponses);

    for (const response of operationResponses) {
      const type = Object.keys(response)[0];
      if (type === 'campaignResult') {
        googleAdsData.campaign_resource = response[type]?.resourceName;
      } else if (type === 'campaignBudgetResult') {
        googleAdsData.campaign_budget_resource = response[type]?.resourceName;
      } else if (type === 'conversionActionResult') {
        googleAdsData.conversion_action_resource = response[type]?.resourceName;
      } else if (type === 'adGroupResult') {
        googleAdsData.ad_group_resource = response[type]?.resourceName;
      } else if (type === 'customConversionGoalResult') {
        googleAdsData.custom_conversion_goal_resource =
          response[type]?.resourceName;
      }
    }

    // create order for making payment
    const amount = getCampaignLifetimeBudget(budgetAndScheduling);
    const orderCreationResponseData = await createRazorpayOrder({
      amount,
      currency: budgetAndScheduling?.currency,
      campaign_id: campaignDetails.id,
      uid: campaignDetails.uid,
      type: GrowEasyOrderType.LAUNCH,
      platform: AdPlatforms.GOOGLE,
    });
    logger.debug(
      `orderCreationResponseData for ${campaignDetails.id}`,
      orderCreationResponseData,
    );

    const orderId = orderCreationResponseData.razorpay_order_id;
    googleAdsData.lead_form_url = leadFormUrl;

    // updating Firestore data to be in sync with Google ads manager
    if (campaignDetails.details?.budget_and_scheduling) {
      campaignDetails.details.budget_and_scheduling.start_time =
        currentDate.toISOString();
      campaignDetails.details.budget_and_scheduling.end_time =
        effectiveCampaignEndDate.toISOString();
    }

    // process search theme text/keywords signals at last, since it can fail for policy violations
    if (
      googleAdsData.ad_group_resource &&
      campaignDetails?.google_ads_data?.search_keywords?.length
    ) {
      // async because it'll take time significant amount of time
      void processSearchCampaignKeywords(
        // Unique keywords
        {
          keywords: Array.from(
            new Set(campaignDetails?.google_ads_data?.search_keywords ?? []),
          ),
          adGroupResourceName: googleAdsData.ad_group_resource,
          adAccountId,
        },
      );
    }

    // Use newly created custom goal in campaign
    if (
      googleAdsData.custom_conversion_goal_resource &&
      googleAdsData.campaign_resource
    ) {
      await useCustomGoalInCampaign({
        customConversionGoalResourceName:
          googleAdsData.custom_conversion_goal_resource,
        campaignResourceName: googleAdsData.campaign_resource,
        adAccountId,
      });
    }

    // populate event label to fire event in lead form
    try {
      if (googleAdsData.conversion_action_resource) {
        googleAdsData.conversion_action_event_label =
          await getConversionActionEventLabel(
            googleAdsData.conversion_action_resource,
          );
      }
    } catch (error) {
      logger.error('Failed to populate conversion action event label');
    }

    // update this data back to campaign
    await updateGoogleAdsDataToCampaign(campaignDoc.id, googleAdsData);
    await updateFirestoreCampaign(campaignDoc.id, {
      details: campaignDetails.details,
      updated_at: Timestamp.now(),
      // for self ad account flow, make it active
      // else make it Active post payment
      status: selfAdAccountFlow
        ? GrowEasyCampaignStatus.ACTIVE
        : GrowEasyCampaignStatus.PAUSED,
      order_id: orderId,
    });

    // asynchronously add call extension asset for call only campaign
    if (isCallCampaign) {
      void createCallExtensionAsset(campaignDetails);
    }

    return {
      google_ads_data: googleAdsData,
      order_id: orderId,
    };
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};
