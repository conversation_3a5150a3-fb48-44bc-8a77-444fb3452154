import axios, { type AxiosError } from 'axios';
import fs, { existsSync, mkdirSync } from 'fs';

import logger from '../../modules/logger';
import {
  AdPlatforms,
  type IGoogleAdsError,
  type IAuthUser,
  type IParamsForAdCreative,
} from '../../types';
import {
  type IGoogleAdsData,
  type ICampaign,
  type IGoogleAdCopy,
  GoogleAssetFieldType,
  type GoogleAdsMutateOperation,
  type IGoogleCampaignInsights,
  type IGoogleLocationDetails,
  type IGoogleMediaAssetDetails,
  GROWEASY_CAMPAIGN_TYPE,
  type IGoogleSearchKeywordsInsights,
  type IGoogleKeywordIdeas,
  type IGoogleCustomConversionAction,
  type IGoogleSearchLocationsInsights,
  type IGoogleCallsInsights,
} from '../../types/campaign_details';
import ValidationError from '../../utils/validation_error';
import { getAdImages } from '../meta_controller';
import {
  getGoogleAdsHeaders,
  getGoogleAdsUrl,
  getGrowEasyHostedLeadFormUrl,
  getSingleCampaign,
  isGrowEasyAdmin,
  resizeImageAsPerGoogleAds,
  updateFirestoreCampaign,
} from '../util';
import { Timestamp } from 'firebase-admin/firestore';
import {
  getGoogleAdCopiesPrompt,
  getGoogleLeadFormContentPrompt,
  getGoogleSearchKeywordsPromptV2,
} from '../../prompts/groweasy_prompts';
import { getResponseFromAzureOpenAi } from '../../modules/openai';
import {
  FIRESTORE_COLLECTIONS,
  GOOGLE_AD_ACCOUNT_ID,
  TMP_ADIMAGES_UPLOAD_DIR,
} from '../../constants';
import { db } from '../../modules/firebase_admin_sdk';
import {
  getCampaignDetails,
  getGoogleAdsDataFromSubCollection,
} from '../db_controller';
import { type IGoogleLead } from '../../types/leads';
import { sendNewLeadsEmail } from '../email_controller';
import { createPMaxCampaign } from './pmax_controller';
import { createSearchOrCallCampaign } from './search_controller';
import { uploadVideoToYouTube } from '../../modules/youtube';

export const updateGoogleAdsDataToCampaign = async (
  campaignDocId: string,
  googleAdsData: Partial<IGoogleAdsData>,
): Promise<void> => {
  await db
    .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
    .doc(campaignDocId)
    .collection(FIRESTORE_COLLECTIONS.PLATFORM_DATA)
    .doc('google')
    .set(googleAdsData, {
      // caution: shallow merge only
      merge: true,
    });
};

export const executeMutateOperations = async (
  mutateOperations: GoogleAdsMutateOperation[],
  adAccountId = GOOGLE_AD_ACCOUNT_ID,
): Promise<
  Array<
    Record<
      string,
      {
        resourceName: string;
      }
    >
  >
> => {
  const url = getGoogleAdsUrl('googleAds:mutate', adAccountId);
  const headers = await getGoogleAdsHeaders();
  const response = await axios.post(
    url.href,
    {
      mutateOperations,
    },
    {
      headers,
    },
  );
  const operationResponses = response.data?.mutateOperationResponses as Array<
    Record<
      string,
      {
        resourceName: string;
      }
    >
  >;
  return operationResponses;
};

export const useCustomGoalInCampaign = async (params: {
  customConversionGoalResourceName: string;
  campaignResourceName: string;
  adAccountId?: string;
}): Promise<void> => {
  const {
    customConversionGoalResourceName,
    campaignResourceName,
    adAccountId,
  } = params;

  logger.info(`Using custom goal in campaign`);

  const operation: GoogleAdsMutateOperation = {
    conversionGoalCampaignConfigOperation: {
      update: {
        goalConfigLevel: 'CAMPAIGN',
        customConversionGoal: customConversionGoalResourceName,
        resourceName: campaignResourceName.replace(
          'campaigns',
          'conversionGoalCampaignConfigs',
        ),
      },
      updateMask: 'customConversionGoal',
    },
  };
  try {
    await executeMutateOperations([operation], adAccountId);
  } catch (error) {
    logger.error(`Custom goal consumption failed`);
    const errorResponse = (
      (error as AxiosError)?.response?.data as { error: IGoogleAdsError }
    )?.error;
    logger.error(errorResponse);
  }
};

export const getConversionActionEventLabel = async (
  conversionActionResourceName: string,
): Promise<string> => {
  try {
    const adAccountId = conversionActionResourceName.split('/')[1];
    const url = getGoogleAdsUrl('googleAds:searchStream', adAccountId);
    const headers = await getGoogleAdsHeaders();
    const response = await axios.post(
      url.href,
      {
        query: `SELECT
          conversion_action.tag_snippets
          FROM conversion_action
          WHERE conversion_action.resource_name = '${conversionActionResourceName}'`,
      },
      {
        headers,
      },
    );
    const conversionAction = response.data?.[0]?.results?.[0]
      ?.conversionAction as {
      tagSnippets: Array<{
        type: 'WEBPAGE_ONCLICK';
        pageFormat: 'HTML';
        eventSnippet: string;
      }>;
    };
    let eventLabel = '';
    conversionAction?.tagSnippets?.forEach((item) => {
      if (item.type === 'WEBPAGE_ONCLICK' && item.pageFormat === 'HTML') {
        const sendToMatch = item.eventSnippet.match(/'send_to':\s*'([^']+)'/);
        if (sendToMatch) {
          eventLabel = sendToMatch[1];
        }
      }
    });
    return eventLabel;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const uploadMediaAssets = async (
  data: {
    campaign_id: string;
  },
  user?: IAuthUser,
): Promise<IGoogleAdsData> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const campaignId = data.campaign_id;
  const campaignDoc = await getSingleCampaign(user?.uid, campaignId);
  const campaignDetails = await getCampaignDetails(campaignId, user);
  if (!campaignDoc || !campaignDetails) {
    throw new ValidationError(400, 'Campaign Not found or Missing permission');
  }
  if (!campaignDetails?.details?.business_details?.business_logo?.square?.url) {
    throw new ValidationError(400, 'Campaign does not have square logo url');
  }
  const googleAdsData: IGoogleAdsData = {
    ...campaignDetails.google_ads_data,
  };

  try {
    let adImages = [] as Array<{
      label?: string;
      width: number;
      height: number;
      url: string;
      hash: string;
    }>;
    const mutateOperations = [];
    let adImageIndex = 0;

    // process banners only for P Max
    if (campaignDetails.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX) {
      const queryParams = {
        hashes: JSON.stringify(
          campaignDetails?.details?.ad_banners?.map((item) => item.image?.hash),
        ),
        ad_account_id: campaignDetails?.details?.config?.ad_account_id ?? '',
        fields: 'id,hash,width,height,label,url',
      };
      adImages = (await getAdImages(queryParams))?.data?.data;
    }

    if (!existsSync(TMP_ADIMAGES_UPLOAD_DIR)) {
      mkdirSync(TMP_ADIMAGES_UPLOAD_DIR, { recursive: true });
    }

    // Ad Video has already been uploaded in onboarding journey
    // this is to avoid "youtubeVideoRegistrationError":"VIDEO_NOT_FOUND", Youtube takes some time processing it
    const videoUrl = campaignDetails.details?.ad_videos?.[0]?.video_url;
    const youtubeVideoId =
      campaignDetails.details?.ad_videos?.[0]?.youtube_video_id ?? '';
    if (videoUrl && youtubeVideoId) {
      googleAdsData.ad_videos = [
        {
          video_url: videoUrl,
          youtube_video_id: youtubeVideoId,
        },
      ];
    }

    // process Ad images
    for (const adImage of adImages) {
      // Fetch the image data using axios
      const imageResponse = await axios.get(adImage.url, {
        responseType: 'arraybuffer',
      });

      const inputImagePath = `${TMP_ADIMAGES_UPLOAD_DIR}/${adImage.hash}_input`;
      const outputImagePath = `${TMP_ADIMAGES_UPLOAD_DIR}/${adImage.hash}_output`;

      await fs.promises.writeFile(inputImagePath, imageResponse.data);
      await resizeImageAsPerGoogleAds({
        inputImagePath,
        outputImagePath,
        inputImageWidth: adImage.width,
        inputImageHeight: adImage.height,
      });
      const outputImageBuffer = await fs.promises.readFile(outputImagePath);
      const base64Image = outputImageBuffer.toString('base64');

      // Create a new asset for each image
      mutateOperations.push({
        create: {
          name: `${campaignDetails.name}_${adImageIndex++}_${adImage.width}_${
            adImage.height
          }`,
          type: 'IMAGE',
          imageAsset: {
            data: base64Image,
            mimeType: 'IMAGE_JPEG',
            fullSize: {
              heightPixels: adImage.height,
              widthPixels: adImage.width,
            },
          },
        },
      });

      await fs.promises.unlink(inputImagePath);
      await fs.promises.unlink(outputImagePath);
    }

    // process business logo
    const businessLogo =
      campaignDetails?.details?.business_details?.business_logo?.square;
    const logoImageResponse = await axios.get(businessLogo.url, {
      responseType: 'arraybuffer',
    });
    mutateOperations.push({
      create: {
        name: `${campaignDetails.name}_logo`,
        type: 'IMAGE',
        imageAsset: {
          data: Buffer.from(logoImageResponse.data).toString('base64'),
          mimeType: 'IMAGE_JPEG',
          fullSize: {
            heightPixels: businessLogo.height,
            widthPixels: businessLogo.width,
          },
        },
      },
    });

    if (youtubeVideoId) {
      mutateOperations.push({
        create: {
          name: campaignDetails.name,
          type: 'YOUTUBE_VIDEO',
          youtube_video_asset: {
            youtube_video_id: youtubeVideoId,
          },
        },
      });
    }
    logger.debug(mutateOperations);
    const url = getGoogleAdsUrl(
      'assets:mutate',
      campaignDetails?.details?.config?.google_ad_account_id,
    );
    const headers = await getGoogleAdsHeaders();
    const response = await axios.post(
      url.href,
      {
        operations: mutateOperations,
      },
      {
        headers,
      },
    );
    const uploadedMediaAssets = response.data?.results as Array<{
      resourceName: string;
    }>;
    let imagesAssets = uploadedMediaAssets;
    let adVideoAssets: Array<{
      resourceName: string;
    }> = [];
    if (youtubeVideoId) {
      imagesAssets = uploadedMediaAssets.slice(0, -1); // all except last
      adVideoAssets = uploadedMediaAssets.slice(-1); // last item will be video
    }
    const adBannersAssets = imagesAssets.slice(0, -1); // last image is logo
    const logoAssets = imagesAssets.slice(-1);

    googleAdsData.media_assets = {
      [GoogleAssetFieldType.MARKETING_IMAGE]: [],
      [GoogleAssetFieldType.SQUARE_MARKETING_IMAGE]: [],
      [GoogleAssetFieldType.PORTRAIT_MARKETING_IMAGE]: [],
      [GoogleAssetFieldType.LOGO]: [
        {
          resource_name: logoAssets[0].resourceName,
        },
      ],
      [GoogleAssetFieldType.YOUTUBE_VIDEO]: adVideoAssets.length
        ? [
            {
              resource_name: adVideoAssets[0].resourceName,
            },
          ]
        : [],
    };

    adBannersAssets.forEach((asset, index) => {
      const correspondingAdImage = adImages[index];
      if (googleAdsData.media_assets) {
        if (correspondingAdImage.width === correspondingAdImage.height) {
          googleAdsData.media_assets[
            GoogleAssetFieldType.SQUARE_MARKETING_IMAGE
          ].push({
            resource_name: asset.resourceName,
          });
        } else if (correspondingAdImage.width > correspondingAdImage.height) {
          googleAdsData.media_assets[GoogleAssetFieldType.MARKETING_IMAGE].push(
            {
              resource_name: asset.resourceName,
            },
          );
        } else {
          googleAdsData.media_assets[
            GoogleAssetFieldType.PORTRAIT_MARKETING_IMAGE
          ].push({
            resource_name: asset.resourceName,
          });
        }
      }
    });

    // update this data back to campaign
    await updateGoogleAdsDataToCampaign(campaignDoc.id, googleAdsData);

    return googleAdsData;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const generateAdCopies = async (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
    type: GROWEASY_CAMPAIGN_TYPE;
  },
): Promise<IGoogleAdCopy[]> => {
  const prompt = getGoogleAdCopiesPrompt({
    business_details: params.business_details,
    targeting: params.targeting,
    ai_assisted_product_usps: params.ai_assisted_product_usps,
    type: params.type,
  });
  const openAiResponse = await getResponseFromAzureOpenAi(prompt);

  const adCopies: IGoogleAdCopy[] = openAiResponse.copies.filter(
    (item: IGoogleAdCopy) => {
      // Enforce text length restrictions
      if (
        item.short_headline?.length <= 15 &&
        item.headline?.length <= 30 &&
        item.long_headline?.length <= 90 &&
        item.short_description?.length <= 60 &&
        item.description?.length <= 90
      ) {
        return true;
      }
      return false;
    },
  );
  // .slice(0, 5); // store max 5 valid copies
  return adCopies;
};

export const generateTextAssets = async (
  data: {
    campaign_id: string;
  },
  user?: IAuthUser,
): Promise<IGoogleAdsData> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const campaignId = data.campaign_id;
  const campaignDoc = await getSingleCampaign(user?.uid, campaignId);
  const campaignDetails = await getCampaignDetails(campaignId, user);
  if (!campaignDoc || !campaignDetails) {
    throw new ValidationError(400, 'Campaign Not found or Missing permission');
  }
  if (!campaignDetails?.details?.business_details?.business_name) {
    throw new ValidationError(400, 'Campaign does not have business name');
  }
  if (campaignDetails?.details?.business_details?.business_name.length > 25) {
    throw new ValidationError(
      400,
      'Business name must be less than 25 characters',
    );
  }
  try {
    if (
      !campaignDetails.details?.business_details ||
      !campaignDetails.details?.targeting
    ) {
      throw new ValidationError(400, 'Missing Campaign details');
    }

    const adCopies = await generateAdCopies({
      business_details: campaignDetails?.details?.business_details,
      targeting: campaignDetails?.details?.targeting,
      ai_assisted_product_usps:
        campaignDetails?.details?.ai_assisted_product_usps,
      type: campaignDetails.type,
    });

    // text assets creation (resource names) is only needed for P max, for Search & Call, we directly pass text values
    // todo: skip for search & call
    const headlineMutateOperations = [];
    const longHeadlineMutateOperations = [];
    const descriptionMutateOperations = [];
    const businessNameMutateOperations = [
      {
        assetOperation: {
          create: {
            textAsset: {
              text: campaignDetails.details.business_details.business_name,
            },
          },
        },
      },
    ];
    for (const copy of adCopies) {
      // for firt set in P-Max campaign, use headline of 15 chars or less and description of 60 chars or less
      const shortCopySet: boolean =
        headlineMutateOperations.length === 0 &&
        campaignDetails.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX;
      headlineMutateOperations.push({
        assetOperation: {
          create: {
            textAsset: {
              text: shortCopySet ? copy.short_headline : copy.headline,
            },
          },
        },
      });
      longHeadlineMutateOperations.push({
        assetOperation: {
          create: {
            textAsset: {
              text: copy.long_headline,
            },
          },
        },
      });
      descriptionMutateOperations.push({
        assetOperation: {
          create: {
            textAsset: {
              text: shortCopySet ? copy.short_description : copy.description,
            },
          },
        },
      });
    }
    const url = getGoogleAdsUrl(
      'googleAds:mutate',
      campaignDetails?.details?.config?.google_ad_account_id,
    );
    const headers = await getGoogleAdsHeaders();
    const promises = [];
    /* console.dir(descriptionMutateOperations, {
      depth: null
    }) */
    for (const operations of [
      headlineMutateOperations,
      longHeadlineMutateOperations,
      descriptionMutateOperations,
      businessNameMutateOperations,
    ]) {
      promises.push(
        axios.post(
          url.href,
          {
            mutateOperations: operations,
          },
          {
            headers,
          },
        ),
      );
    }
    const [
      headlineResponse,
      longHeadlineResponse,
      descriptionResponse,
      businessNameResponse,
    ] = await Promise.all(promises);
    const googleAdsData: IGoogleAdsData = {
      ...campaignDetails.google_ads_data,
      ad_copies: adCopies,
      text_assets: {
        [GoogleAssetFieldType.BUSINESS_NAME]: [],
        [GoogleAssetFieldType.HEADLINE]: [],
        [GoogleAssetFieldType.LONG_HEADLINE]: [],
        [GoogleAssetFieldType.DESCRIPTION]: [],
      },
    };
    adCopies.forEach((adCopy, index) => {
      const headlineAssetResult =
        headlineResponse.data?.mutateOperationResponses?.[index]?.assetResult;
      const longHeadlineAssetResult =
        longHeadlineResponse.data?.mutateOperationResponses?.[index]
          ?.assetResult;
      const descriptionAssetResult =
        descriptionResponse.data?.mutateOperationResponses?.[index]
          ?.assetResult;
      const shortCopySet =
        index === 0 &&
        campaignDetails.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX;
      if (googleAdsData.text_assets) {
        googleAdsData.text_assets[GoogleAssetFieldType.HEADLINE].push({
          text: shortCopySet ? adCopy.short_headline : adCopy.headline,
          resource_name: headlineAssetResult.resourceName,
        });
        googleAdsData.text_assets[GoogleAssetFieldType.LONG_HEADLINE].push({
          text: adCopy.long_headline,
          resource_name: longHeadlineAssetResult.resourceName,
        });
        googleAdsData.text_assets[GoogleAssetFieldType.DESCRIPTION].push({
          text: shortCopySet ? adCopy.short_description : adCopy.description,
          resource_name: descriptionAssetResult.resourceName,
        });
      }
    });
    if (googleAdsData.text_assets) {
      googleAdsData.text_assets[GoogleAssetFieldType.BUSINESS_NAME].push({
        text: campaignDetails.details.business_details.business_name,
        resource_name:
          businessNameResponse.data?.mutateOperationResponses?.[0]?.assetResult
            ?.resourceName,
      });
    }

    // update this data back to campaign
    await updateGoogleAdsDataToCampaign(campaignDoc.id, googleAdsData);
    return googleAdsData;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const getCampaignReportingFromGoogle = async (
  googleCampaignResource: string,
): Promise<IGoogleCampaignInsights[]> => {
  try {
    const arr = googleCampaignResource.split('/');
    const googleCampaignId = arr[arr.length - 1];
    const adAccountId = arr[1];
    const url = getGoogleAdsUrl('googleAds:searchStream', adAccountId);
    const headers = await getGoogleAdsHeaders();
    const response = await axios.post(
      url.href,
      {
        query: `SELECT
          campaign.name,  
          segments.device,
          metrics.impressions,
          metrics.clicks,
          metrics.ctr,
          metrics.average_cpc,
          metrics.cost_micros,
          metrics.conversions,
          metrics.all_conversions
          FROM campaign
          WHERE campaign.id = '${googleCampaignId}'`,
      },
      {
        headers,
      },
    );
    return response.data?.length ? response.data?.[0]?.results ?? [] : [];
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const getKeywordsReportingFromGoogle = async (
  googleCampaignResource: string,
): Promise<IGoogleSearchKeywordsInsights[]> => {
  try {
    const arr = googleCampaignResource.split('/');
    const googleCampaignId = arr[arr.length - 1];
    const adAccountId = arr[1];
    const url = getGoogleAdsUrl('googleAds:searchStream', adAccountId);
    const headers = await getGoogleAdsHeaders();
    const response = await axios.post(
      url.href,
      {
        query: `SELECT
          metrics.impressions,
          metrics.clicks,
          metrics.ctr,
          metrics.average_cpc,
          metrics.cost_micros,
          metrics.conversions,
          metrics.all_conversions,
          ad_group_criterion.keyword.text,
          ad_group_criterion.keyword.match_type
          FROM keyword_view
          WHERE campaign.id = '${googleCampaignId}'`,
      },
      {
        headers,
      },
    );
    return response.data?.length ? response.data?.[0]?.results : [];
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const getCampaignLocationPerformance = async (
  googleCampaignResource: string,
): Promise<IGoogleSearchLocationsInsights[]> => {
  try {
    const arr = googleCampaignResource.split('/');
    const googleCampaignId = arr[arr.length - 1];
    const adAccountId = arr[1];
    const url = getGoogleAdsUrl('googleAds:searchStream', adAccountId);
    const headers = await getGoogleAdsHeaders();

    // Step 1: Get performance data with location IDs
    const performanceResponse = await axios.post(
      url.href,
      {
        query: `SELECT
          campaign.id,
          campaign.name,
          segments.geo_target_city,
          metrics.impressions,
          metrics.clicks,
          metrics.ctr,
          metrics.average_cpc,
          metrics.cost_micros,
          metrics.conversions,
          metrics.all_conversions,
          geographic_view.resource_name
        FROM geographic_view
        WHERE campaign.id = '${googleCampaignId}'`,
      },
      { headers },
    );

    const performanceData = performanceResponse.data?.length
      ? (performanceResponse.data[0]
          ?.results as IGoogleSearchLocationsInsights[])
      : [];
    if (!performanceData || performanceData.length === 0) return [];

    // Step 2: Get canonical names for cities
    const cityIds = [
      ...new Set(
        performanceData
          .map((item) => item.segments?.geoTargetCity)
          .filter(Boolean),
      ),
    ];

    if (cityIds.length === 0) return performanceData;

    const cityResponse = await axios.post(
      url.href,
      {
        query: `SELECT
          geo_target_constant.resource_name,
          geo_target_constant.canonical_name
        FROM geo_target_constant
        WHERE geo_target_constant.resource_name IN (${cityIds
          .map((id) => `'${id}'`)
          .join(',')})`,
      },
      { headers },
    );

    const cityData = cityResponse.data?.length
      ? (cityResponse.data[0]?.results as Array<{
          geoTargetConstant: {
            resourceName: string;
            canonicalName: string;
          };
        }>)
      : [];
    const cityMap = cityData.reduce<Record<string, string>>((map, city) => {
      map[city.geoTargetConstant.resourceName] =
        city.geoTargetConstant.canonicalName;
      return map;
    }, {});

    performanceData.forEach((item) => {
      item.segments.geoTargetCityCanonicalName =
        cityMap[item.segments.geoTargetCity];
    });

    return performanceData;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const getCampaignCallsPerformance = async (
  googleCampaignResource: string,
): Promise<IGoogleCallsInsights[]> => {
  try {
    const arr = googleCampaignResource.split('/');
    const googleCampaignId = arr[arr.length - 1];
    const adAccountId = arr[1];
    const url = getGoogleAdsUrl('googleAds:searchStream', adAccountId);
    const headers = await getGoogleAdsHeaders();
    const response = await axios.post(
      url.href,
      {
        query: `SELECT
          call_view.resource_name,
          call_view.call_status,
          call_view.start_call_date_time,
          call_view.end_call_date_time,
          call_view.call_duration_seconds,
          call_view.caller_area_code,
          call_view.caller_country_code,
          campaign.id,
          campaign.name
          FROM call_view
          WHERE campaign.id = '${googleCampaignId}'`,
      },
      {
        headers,
      },
    );
    return response.data?.length ? response.data?.[0]?.results : [];
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const getCampaignInsights = async (
  data: {
    campaignId: string;
  },
  user?: IAuthUser,
): Promise<IGoogleCampaignInsights[]> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  const { campaignId } = data;

  const campaignDetails = await getCampaignDetails(campaignId, user);
  const googleCampaignResource =
    campaignDetails?.google_ads_data?.campaign_resource;
  if (!googleCampaignResource) {
    throw new ValidationError(
      400,
      'Campaign Not found or Missing permission or missing Google campaign resource',
    );
  }
  try {
    return await getCampaignReportingFromGoogle(googleCampaignResource);
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const getCampaignInsightsV2 = async (
  data: {
    campaignId: string;
  },
  user?: IAuthUser,
): Promise<{
  campaign: IGoogleCampaignInsights[];
  keywords: IGoogleSearchKeywordsInsights[];
  locations: IGoogleSearchLocationsInsights[];
  calls: IGoogleCallsInsights[];
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  const { campaignId } = data;

  const campaignDetails = await getCampaignDetails(campaignId, user);
  const googleCampaignResource =
    campaignDetails?.google_ads_data?.campaign_resource;
  if (!googleCampaignResource) {
    throw new ValidationError(
      400,
      'Campaign Not found or Missing permission or missing Google campaign resource',
    );
  }
  try {
    const response = {
      campaign: await getCampaignReportingFromGoogle(googleCampaignResource),
      keywords: [] as IGoogleSearchKeywordsInsights[],
      locations: [] as IGoogleSearchLocationsInsights[],
      calls: [] as IGoogleCallsInsights[],
    };
    if (
      [
        GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
        GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
      ].includes(campaignDetails.type)
    ) {
      const [keywordsInsights, locationsInsights] = await Promise.all([
        getKeywordsReportingFromGoogle(googleCampaignResource),
        getCampaignLocationPerformance(googleCampaignResource),
      ]);
      response.keywords = keywordsInsights;
      response.locations = locationsInsights;
    }
    if (campaignDetails.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL) {
      response.calls = await getCampaignCallsPerformance(
        googleCampaignResource,
      );
    }
    return response;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const getLocationSearch = async (
  queryParams: Record<string, string>,
): Promise<IGoogleLocationDetails[]> => {
  const { query } = queryParams;
  if (!query) {
    throw new ValidationError(400, 'Missing query');
  }
  try {
    const url = getGoogleAdsUrl('googleAds:searchStream');
    const headers = await getGoogleAdsHeaders();
    const response = await axios.post(
      url.href,
      {
        query: `SELECT 
          geo_target_constant.country_code, 
          geo_target_constant.id, 
          geo_target_constant.name, 
          geo_target_constant.canonical_name 
          FROM geo_target_constant 
          WHERE geo_target_constant.name LIKE '${query}%'`,
      },
      {
        headers,
      },
    );
    return response.data?.length ? response.data?.[0]?.results : [];
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const generateAdAssets = async (
  campaignDetails: Partial<ICampaign>,
  user?: IAuthUser,
): Promise<IGoogleAdsData> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  if (!campaignDetails?.id) {
    throw new ValidationError(400, 'Missing campaign id');
  }
  const campaignDoc = await getSingleCampaign(user.uid, campaignDetails.id);
  if (campaignDoc?.id) {
    // update details
    await updateFirestoreCampaign(campaignDoc.id, {
      details: campaignDetails.details,
      updated_at: Timestamp.now(),
    });

    // generate text and/or media assets
    // create media assets first so that Youtube video gets enough processing time
    await uploadMediaAssets(
      {
        campaign_id: campaignDetails.id,
      },
      user,
    );
    // for search campaigns, IMAGE assets can be created (just like PMax) and then can be associated to campaign
    // via CampaignAssetService/AdGroupAssetService ?

    // generate text assets
    const googleAdsData = await generateTextAssets(
      {
        campaign_id: campaignDetails.id,
      },
      user,
    );
    logger.debug(googleAdsData);
    return googleAdsData;
  } else {
    throw new ValidationError(
      400,
      'This campaign does not exist or you do not have permission to access it.',
    );
  }
};

export const getMediaAssetsDetails = async (
  resourceNames: string[],
  user?: IAuthUser,
): Promise<IGoogleMediaAssetDetails[]> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  try {
    const adAccountId = resourceNames[0]?.split('/')[1];
    const url = getGoogleAdsUrl('googleAds:search', adAccountId);
    const headers = await getGoogleAdsHeaders();

    const response = await axios.post(
      url.href,
      {
        query: `
          SELECT
            asset.id,
            asset.name,
            asset.type,
            asset.image_asset.file_size,
            asset.image_asset.full_size.url,
            asset.image_asset.mime_type,
            asset.youtube_video_asset.youtube_video_id,
            asset.youtube_video_asset.youtube_video_title
          FROM asset
          WHERE asset.resource_name IN (${resourceNames
            .map((name) => `'${name}'`)
            .join(', ')})
        `,
      },
      {
        headers,
      },
    );
    return response.data?.results ?? [];
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const getCampaignDetailsForGoogleLeadForm = async (
  campaignDocId: string,
): Promise<Partial<ICampaign> | null> => {
  try {
    const campaignDocRef = await db
      .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
      .doc(campaignDocId)
      .get();

    const campaignDetails = campaignDocRef.data() as ICampaign;

    if (campaignDetails) {
      campaignDetails.google_ads_data =
        await getGoogleAdsDataFromSubCollection(campaignDocId);

      return {
        id: campaignDetails.id,
        details: campaignDetails.details,
        google_ads_data: campaignDetails.google_ads_data,
        type: campaignDetails.type,
      };
    } else {
      return null;
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const saveGoogleLeadFormData = async (data: {
  campaign_id: string;
  form_values: Record<string, string>;
}): Promise<void> => {
  const { campaign_id: campaignId, form_values: formValues } = data;

  try {
    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
      .where('id', '==', campaignId)
      .get();
    const campaignDetails = snapshot.docs?.[0]?.data() as ICampaign;
    if (!campaignDetails) {
      throw new Error('Campaign not found');
    }
    const leadDocRef = db
      .collection(FIRESTORE_COLLECTIONS.GOOGLE_FORM_LEADS)
      .doc();
    const lead: IGoogleLead = {
      campaign_id: campaignId,
      field_data: [],
      created_at: Timestamp.now(),
      uid: campaignDetails.uid,
      id: leadDocRef.id,
      created_time: Math.round(Date.now() / 1000), // in seconds
    };
    campaignDetails?.details?.leadgen_form?.questions?.forEach((question) => {
      lead.field_data?.push({
        ...question,
        values: [formValues[question.key] ?? 'NA'],
        name: question.key,
      });
    });
    await leadDocRef.set(lead);
    void sendNewLeadsEmail({
      uid: lead.uid ?? '',
      platform: AdPlatforms.GOOGLE,
      parsed_leads: [
        {
          ...lead,
          created_time: new Date().toISOString(),
          campaign_name:
            campaignDetails.details?.business_details?.business_category ?? '',
          business_mobile:
            campaignDetails.details?.business_details?.mobile ?? '',
          business_product_or_service_description:
            campaignDetails.details?.business_details
              ?.product_or_service_description ?? '',
          leadgen_id: '',
        },
      ],
    });
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getCampaignLeads = async (
  campaignId: string,
  user?: IAuthUser,
): Promise<IGoogleLead[]> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  let query = db
    .collection(FIRESTORE_COLLECTIONS.GOOGLE_FORM_LEADS)
    .where('campaign_id', '==', campaignId);
  if (!isGrowEasyAdmin(user)) {
    query = query.where('uid', '==', `${user?.uid}`);
  }
  const snapshot = await query.orderBy('created_at', 'desc').limit(500).get();
  const leads: IGoogleLead[] = [];
  snapshot.forEach((doc) => {
    const data = doc.data() as IGoogleLead;
    leads.push(data);
  });
  return leads;
};

export const startGoogleCampaign = async (
  campaign: ICampaign,
  user?: IAuthUser,
): Promise<{
  id: string;
  order_id: string;
}> => {
  try {
    logger.info(`startGoogleCampaign called for ${campaign?.id}`);
    await generateAdAssets(campaign, user);
    const createCampaign =
      campaign.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX
        ? createPMaxCampaign
        : createSearchOrCallCampaign;
    const response = await createCampaign(
      {
        campaign_id: campaign.id,
      },
      user,
    );
    return {
      id: campaign.id,
      order_id: response.order_id,
    };
  } catch (error) {
    const errorMessage =
      ((error as AxiosError)?.response?.data as { error: IGoogleAdsError })
        ?.error?.details?.[0]?.errors?.[0]?.message ?? (error as Error).message;
    const statusCode =
      (error as AxiosError).response?.status ??
      (error as ValidationError).statusCode ??
      500;
    const parsedError = new ValidationError(statusCode, errorMessage);
    logger.error(parsedError);
    throw parsedError;
  }
};

export const updateGoogleCampaign = async (
  campaignResourceName: string,
  payload: {
    status?: 'PAUSED' | 'ENABLED' | 'REMOVED';
    endDate?: string;
    name?: string;
  },
  adAccountId?: string,
): Promise<void> => {
  // Determine the fields to include in the updateMask
  const updateMaskFields = [];
  if (payload.status) {
    updateMaskFields.push('status');
  }
  if (payload.endDate) {
    updateMaskFields.push('endDate');
  }
  if (payload.name) {
    updateMaskFields.push('name');
  }
  try {
    await executeMutateOperations(
      [
        {
          campaignOperation: {
            update: {
              resourceName: campaignResourceName,
              ...payload,
            },
            updateMask: updateMaskFields.join(','),
          },
        },
      ],
      adAccountId,
    );
  } catch (error) {
    const errorMessage =
      ((error as AxiosError)?.response?.data as { error: IGoogleAdsError })
        ?.error?.details?.[0]?.errors?.[0]?.message ?? (error as Error).message;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }
};

export const updateGoogleCampaignBudget = async (
  campaignBudgetResourceName: string,
  payload: {
    amountMicros: number;
  },
  adAccountId?: string,
): Promise<void> => {
  try {
    await executeMutateOperations(
      [
        {
          campaignBudgetOperation: {
            update: {
              resourceName: campaignBudgetResourceName,
              ...payload,
            },
            updateMask: 'amountMicros',
          },
        },
      ],
      adAccountId,
    );
  } catch (error) {
    const errorMessage =
      ((error as AxiosError)?.response?.data as { error: IGoogleAdsError })
        ?.error?.details?.[0]?.errors?.[0]?.message ?? (error as Error).message;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }
};

export const populateLeadFormContent = async (
  campaignId: string,
  user?: IAuthUser,
): Promise<IGoogleAdsData['lead_form_content']> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const campaignDoc = await getSingleCampaign(user?.uid, campaignId);
  const campaignDetails = campaignDoc?.data() as ICampaign;
  if (!campaignDoc || !campaignDetails) {
    throw new ValidationError(400, 'Campaign Not found or Missing permission');
  }

  try {
    if (
      !campaignDetails.details?.business_details?.business_name ||
      !campaignDetails.details?.targeting
    ) {
      throw new ValidationError(400, 'Missing Campaign details');
    }
    const prompt = getGoogleLeadFormContentPrompt({
      business_details: campaignDetails?.details?.business_details,
      targeting: campaignDetails?.details?.targeting,
    });
    const leadFormContent = (await getResponseFromAzureOpenAi(
      prompt,
    )) as IGoogleAdsData['lead_form_content'];

    // update this data back to campaign
    await updateGoogleAdsDataToCampaign(campaignDoc.id, {
      lead_form_content: leadFormContent,
      lead_form_url: getGrowEasyHostedLeadFormUrl(
        campaignDetails?.details?.business_details?.business_name,
        campaignDoc?.id,
      ),
    });
    return leadFormContent;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const populateSearchKeywordsSuggestions = async (
  campaignId: string,
  user?: IAuthUser,
): Promise<IGoogleAdsData['search_keywords_suggestions']> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const campaignDoc = await getSingleCampaign(user?.uid, campaignId);
  const campaignDetails = campaignDoc?.data() as ICampaign;
  if (!campaignDoc || !campaignDetails) {
    throw new ValidationError(400, 'Campaign Not found or Missing permission');
  }

  try {
    if (!campaignDetails.details?.business_details) {
      throw new ValidationError(400, 'Missing Campaign details');
    }
    const prompt = getGoogleSearchKeywordsPromptV2({
      business_details: campaignDetails?.details?.business_details,
      // hardcoded targeting for google
      targeting: {
        age_min: 21,
        age_max: 65,
        genders: [1, 2],
        geo_locations: {},
        flexible_spec: [],
        age_range: [],
      },
      ai_assisted_product_usps:
        campaignDetails?.details?.ai_assisted_product_usps,
      type: campaignDetails.type,
    });
    const searchKeywordsSuggestions = (await getResponseFromAzureOpenAi(
      prompt,
    )) as IGoogleAdsData['search_keywords_suggestions'];

    // update this data back to campaign
    await updateGoogleAdsDataToCampaign(campaignDoc.id, {
      search_keywords_suggestions: searchKeywordsSuggestions,
    });
    return searchKeywordsSuggestions;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const generateKeywordIdeas = async (params: {
  url?: string;
  seed_keywords: string[];
  geo_target_constants?: string[];
}): Promise<{
  keyword_ideas: IGoogleKeywordIdeas[];
}> => {
  const { url, geo_target_constants: geoTargetConstants } = params;

  // Limit seed keywords to a maximum of 10
  const seedKeywords = params.seed_keywords.slice(0, 10);

  try {
    const apiUrl = getGoogleAdsUrl(':generateKeywordIdeas');
    const headers = await getGoogleAdsHeaders();

    // Construct request payload dynamically
    const requestBody: {
      keywordPlanNetwork: 'GOOGLE_SEARCH' | 'GOOGLE_SEARCH_AND_PARTNERS';
      language: string;
      geoTargetConstants: string[];
      keywordAndUrlSeed?: {
        keywords: string[];
        url: string;
      };
      keywordSeed?: {
        keywords: string[];
      };
    } = {
      keywordPlanNetwork: 'GOOGLE_SEARCH', // GOOGLE_SEARCH_AND_PARTNERS
      language: 'languageConstants/1000', // Assuming English
      // max 10 locations, If no locations are provided, use an empty array
      geoTargetConstants: (geoTargetConstants ?? []).slice(0, 10),
    };

    // Add either keywordSeed or keywordAndUrlSeed based on the presence of `url`
    if (url) {
      requestBody.keywordAndUrlSeed = {
        keywords: seedKeywords,
        url,
      };
    } else {
      requestBody.keywordSeed = {
        keywords: seedKeywords,
      };
    }

    const response = await axios.post(apiUrl.href, requestBody, { headers });
    const keywordIdeas = response.data?.results as IGoogleKeywordIdeas[];
    const sortedKeywordIdeas = keywordIdeas
      ?.map((item: IGoogleKeywordIdeas) => ({
        ...item,
        keywordIdeaMetrics: {
          ...item.keywordIdeaMetrics,
          avgMonthlySearches:
            Number(item.keywordIdeaMetrics?.avgMonthlySearches) || 0, // Ensure it's a number
          monthlySearchVolumes: [], // no need of this data
          competition: item.keywordIdeaMetrics?.competition ?? '-',
        },
      }))
      .sort(
        (a, b) =>
          b.keywordIdeaMetrics.avgMonthlySearches -
          a.keywordIdeaMetrics.avgMonthlySearches,
      )
      .slice(0, 100); // top 100 keywords

    return {
      keyword_ideas: sortedKeywordIdeas,
    };
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const createCustomConversionAction = async (
  campaignId: string,
  user?: IAuthUser,
): Promise<IGoogleCustomConversionAction> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  const campaignDoc = await getSingleCampaign(user?.uid, campaignId);
  const campaignDetails = await getCampaignDetails(campaignId, user);

  const adAccountId =
    campaignDetails?.details?.config?.google_ad_account_id ??
    GOOGLE_AD_ACCOUNT_ID;
  const customConversionActionDocId =
    campaignDetails?.details?.config?.google_custom_conversion_action_doc_id;

  if (!customConversionActionDocId) {
    throw new ValidationError(
      400,
      'This campaign has missing google_custom_conversion_action_doc_id',
    );
  }
  const docId = `${adAccountId}_${customConversionActionDocId}`;

  try {
    const docRef = db
      .collection(FIRESTORE_COLLECTIONS.GOOGLE_CUSTOM_CONVERSION_ACTONS)
      .doc(docId);
    let customConversionAction = (
      await docRef.get()
    ).data() as IGoogleCustomConversionAction;

    // check if it has already been created
    if (!customConversionAction) {
      // create new
      customConversionAction = {
        id: docId,
        conversion_action_resource: '',
        conversion_action_event_label: '',
        custom_conversion_goal_resource: '',
        created_at: Timestamp.now(),
        category:
          campaignDetails.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH
            ? 'SUBMIT_LEAD_FORM'
            : 'PURCHASE',
        ad_account_id: adAccountId,
      };
      const conversionActionResourceName = `customers/${adAccountId}/conversionActions/-1`;

      const operations: GoogleAdsMutateOperation[] = [
        {
          conversionActionOperation: {
            create: {
              resourceName: conversionActionResourceName,
              name: customConversionActionDocId,
              type: 'WEBPAGE',
              category: customConversionAction.category,
              status: 'ENABLED',
              valueSettings: {
                defaultValue: 1.0,
                alwaysUseDefaultValue: true,
              },
              countingType: 'ONE_PER_CLICK',
              attributionModelSettings: {
                attributionModel: 'GOOGLE_ADS_LAST_CLICK',
              },
            },
          },
        },
        {
          customConversionGoalOperation: {
            create: {
              name: `goal_${customConversionActionDocId}`,
              conversionActions: [conversionActionResourceName],
            },
          },
        },
      ];
      const operationResponses = await executeMutateOperations(
        operations,
        campaignDetails?.details?.config?.google_ad_account_id,
      );
      for (const response of operationResponses) {
        const type = Object.keys(response)[0];
        if (type === 'conversionActionResult') {
          customConversionAction.conversion_action_resource =
            response[type]?.resourceName;
        } else if (type === 'customConversionGoalResult') {
          customConversionAction.custom_conversion_goal_resource =
            response[type]?.resourceName;
        }
      }
      customConversionAction.conversion_action_event_label =
        await getConversionActionEventLabel(
          customConversionAction.conversion_action_resource ?? '',
        );
      // write to Firestore
      await docRef.set(customConversionAction, {
        merge: true,
      });
    }
    // update campaign
    const googleAdsData = campaignDetails.google_ads_data ?? {};
    googleAdsData.conversion_action_resource =
      customConversionAction.conversion_action_resource;
    googleAdsData.conversion_action_event_label =
      customConversionAction.conversion_action_event_label;
    googleAdsData.custom_conversion_goal_resource =
      customConversionAction.custom_conversion_goal_resource;
    await updateGoogleAdsDataToCampaign(campaignDoc?.id, googleAdsData);
    return customConversionAction;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

export const uploadVideoToYoutubeFromUrl = async (
  data: {
    file_url: string;
    campaign_id: string;
  },
  user?: IAuthUser,
): Promise<{
  youtube_video_id: string;
  video_url?: string;
}> => {
  try {
    // try-catch so that quota limit does not affect video processing
    const campaignId = data.campaign_id;
    const campaignDetails = await getCampaignDetails(campaignId, user);
    const youtubeVideoId =
      (await uploadVideoToYouTube({
        videoUrl: data.file_url,
        title: campaignDetails.name.substring(0, 100), // title has max chars limit 100
        description:
          campaignDetails.details?.business_details
            ?.product_or_service_description ?? '',
      })) ?? '';
    return {
      youtube_video_id: youtubeVideoId,
      video_url: data.file_url,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};
