import express from 'express';
import cors from 'cors';
import axiosRetry from 'axios-retry';
import axios from 'axios';
import compression from 'compression';
import 'dotenv/config';

import routes from './routes';
import miscRoutes from './routes/misc_routes';
import metaRoutes from './routes/meta_routes';
import openAiRoutes from './routes/openai_routes';
import dbRoutes from './routes/db_routes';
import crmRoutes from './routes/crm_routes';
import adminRoutes from './routes/admin_routes';
import userRoutes from './routes/user_routes';
import testRoutes from './routes/test_routes';
import cronRoutes from './routes/cron_routes';
import ctwaRoutes from './routes/ctwa_routes';
import paymentRoutes from './routes/payment_routes';
import bannerbotRoutes from './routes/bannerbot';
import launcheasyRoutes from './routes/launcheasy_routes';
import seoRoutes from './routes/seo_routes';
import agentsRoutes from './routes/agents';
import googleRoutes from './routes/google_routes';
import videoRoutes from './routes/video_routes';
import reportsRoutes from './routes/reports_routes';
import agenteasyRoutes from './routes/agenteasy_routes';
import tiktokRoutes from './routes/tiktok_routes';
import logger from './modules/logger';
import authMiddleware from './middlewares/auth_middleware';

require('newrelic');

const app = express();

// setup retry on global axios
axiosRetry(axios, {
  retries: 3, // number of retries
  retryDelay: (retryCount) => {
    logger.info(`axios retry attempt: ${retryCount}`);
    return retryCount * 1000; // time interval between retries
  },
  retryCondition: (error) => {
    logger.info(`Inside retryCondition:`, error.response?.status);
    const config = error.config;

    // Skip retry if custom header is set
    if (config?.headers?.['x-no-retry'] === 'true') {
      return false;
    }

    // if retry condition is not specified, by default idempotent requests are retried
    return error?.response?.status === 500;
  },
});

const corsOptions = {
  origin: [
    /^https?:\/\/([a-zA-Z0-9-]+\.)*groweasy\.ai$/,
    /^https?:\/\/([a-zA-Z0-9-]+\.)*bannerbot\.xyz$/,
    /^https?:\/\/([a-zA-Z0-9-]+\.)*designeasy\.ai$/,
    'http://localhost:3000',
    'https://ai.nividasoftware.com',
    'https://ai.zendot.in',
    'https://connectform.co',
    'https://ads.teamgeniusmarketing.com',
    'https://adglobalai.com',
  ],
  optionsSuccessStatus: 200, // some legacy browsers (IE11, various SmartTVs) choke on 204
};
app.use(cors(corsOptions));

// Enable Gzip compression
app.use(compression() as any);

app.use(
  express.json({
    limit: '5mb',
    verify: (req, res, buf) => {
      req.rawBody = buf.toString();
    },
  }),
);
// app.use(express.urlencoded({ extended: true, limit: '5mb' }));

app.use((req, res, next) => {
  if (['/api/ping', '/api/seo/blogs-categories'].includes(req.originalUrl)) {
    // do not log these requests
  } else {
    logger.info(
      'Worker: %d | Request: %s %s | Trace-Id: %s',
      process.pid,
      req.method,
      req.originalUrl,
      req.headers['groweasy-trace-id'],
    );
  }
  next();
});

// public routes
app.use('/api', routes);
app.use('/api', miscRoutes);
app.use('/api/test', testRoutes);
app.use('/api/cron', cronRoutes);
app.use('/api/bannerbot', bannerbotRoutes);
app.use('/api/launcheasy', launcheasyRoutes);
app.use('/api/seo', seoRoutes);
app.use('/api/agents', agentsRoutes);

// auth enabled routes
app.use(authMiddleware);
app.use('/api/meta', metaRoutes);
app.use('/api/openai', openAiRoutes);
app.use('/api/db', dbRoutes);
app.use('/api/crm', crmRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/users', userRoutes);
app.use('/api/ctwa', ctwaRoutes);
app.use('/api/payment', paymentRoutes);
app.use('/api/google', googleRoutes);
app.use('/api/video', videoRoutes);
app.use('/api/reports', reportsRoutes);
app.use('/api/agenteasy', agenteasyRoutes);
app.use('/api/tiktok', tiktokRoutes);

export default app;
