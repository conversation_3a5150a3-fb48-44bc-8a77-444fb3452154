import { type Timestamp } from 'firebase-admin/firestore';

export interface ISubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_in_usd: number;
  period: 'month' | 'year';
  created_at: Timestamp;
  line_items: string[];
  features: Record<string, string>;
  stripe_product_id: string;
  stripe_price_id: string;
  active: boolean;
}

export enum UserSubscriptionSource {
  STRIPE = 'STRIPE',
  APP_SUMO = 'APP_SUMO',
  ADMIN = 'ADMIN',
}

export interface IUserSubscription {
  id: string;
  uid: string;
  source: UserSubscriptionSource;

  // stripe specific fields
  stripe_customer_id?: string;
  stripe_subscription_id?: string;

  // appsumo specific fields
  appsumo_coupon_code?: string;

  subscription_plan_id: string;
  created_at: Timestamp;
  updated_at: Timestamp;
}
