import { type Timestamp } from 'firebase-admin/firestore';
import { type IPexelsVideoData, type IImageData } from '../stock_image';
import { type UserSubscriptionSource } from './subscription';

interface IVideoFrameData {
  image_keywords?: string[];
  images?: IImageData[];
  script_text: string;
  duration: number;
  videos?: IPexelsVideoData[];
  video_content_description_involving_people?: string;
}

export interface IVideoData {
  frames: IVideoFrameData[];
  total_duration: number;
  video_caption: string;
  audio_url?: string;
}

export interface IBannerbotBusinessDetails {
  product_or_service_description: string;
  product_images?: Array<{
    url: string;
    width: number;
    height: number;
  }>;
  website?: string;
  mobile?: string;
  business_name?: string;
  business_logo?: {
    square: {
      url: string;
      width: number;
      height: number;
    };
  } | null;
  key_benefits?: string[];
}

export enum BannerbotAdType {
  SINGLE_IMAGE = 'SINGLE_IMAGE',
  CAROUSEL = 'CAROUSEL',
  VIDEO = 'VIDEO',
  AI_BANNER = 'AI_BANNER',
}

export enum BannerbotProjectStatus {
  DRAFT = 'DRAFT',
  ARCHIVED = 'ARCHIVED',
  COMPLETE = 'COMPLETE',
}

export interface IBannerbotAdDetails {
  types: BannerbotAdType[];
}

export interface IBannerbotVideoAdDetails {
  id: string;
  url: string;
  thumbnail_url: string;
  caption: string;
  template_id: string;
  width: number;
  height: number;
  created_at?: number;
  updated_at?: number; // will be updated once s3 URL is ready
}

export interface IBannerbotProject {
  id: string;
  title: string;
  status: BannerbotProjectStatus;
  type: 'AI_ONLY' | 'AI_HUMAN';
  created_at: Timestamp;
  updated_at: Timestamp;
  uid: string;
  details: {
    business_details: IBannerbotBusinessDetails;
    ad_details: IBannerbotAdDetails;
    ai_key_benefits?: string[];
    saved_assets?: {
      [BannerbotAdType.SINGLE_IMAGE]?: Array<{
        url: string;
        width: number;
        height: number;
        created_at?: number;
      }>;
      [BannerbotAdType.VIDEO]?: IBannerbotVideoAdDetails[];
      [BannerbotAdType.CAROUSEL]?: Array<{
        width: number;
        height: number;
        image_urls: string[];
        created_at?: number;
      }>;
      [BannerbotAdType.AI_BANNER]?: Array<{
        url: string;
        width: number;
        height: number;
        created_at?: number;
      }>;
    };
  };
}

export interface IBannerbotUserProfile {
  name: string;
  uid: string;
  email: string;
  mobile?: string;
  created_at: Timestamp;
  updated_at: Timestamp;
  mobile_dial_code?: string;
  whatsapp_opt_in?: boolean;
  acquisition_source?: 'web' | 'android' | 'ios';
}

export interface ITemplatizedVideo {
  id: string;
  uid: string;
  video_id: string;
  video_url: string;
  project_id: string;
  thumbnail_url: string;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export interface IAiAdBanner {
  id: string;
  uid: string;
  project_id: string;
  banner_url: string;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export interface ICouponCode {
  id: string;
  created_at: Timestamp;
  redeemed_at: Timestamp | null;
  redeemed_by: string | null; // User's uid
  code: string; // e.g., "AS-H7M2NZ18X4CK"
  is_active: boolean;
  valid_until: Timestamp;
  discount_type: 'flat' | 'percent';
  discount_value: number; // 99 for $99 off or 50 for 50% off
  metadata: ICouponMetadata;
  description: string;
}

interface ICouponMetadata {
  source: UserSubscriptionSource;
}
