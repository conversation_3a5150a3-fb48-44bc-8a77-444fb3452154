import { type Timestamp } from 'firebase-admin/firestore';
import {
  type IBusinessDetails,
  type ILeadgenFormQuestion,
} from './campaign_details';
import { type AdPlatforms } from '.';
import { type IAgentEasyLeadClassificationData } from './agenteasy';

export interface IMetaLead {
  created_time: string;
  id: string;
  ad_id: string;
  form_id: string;
  field_data: Array<{
    name: string;
    values: string[];
  }>;
  uid?: string;
  campaign_id?: string;
  agenteasy?: IAgentEasyLeadClassificationData;
}

export interface IParsedLead {
  created_time: string | number;
  field_data: Array<
    {
      name: string;
      values: string[];
    } & Partial<ILeadgenFormQuestion>
  >;
}

export interface ILeadsNotificationPayload {
  // todo separate it for CTWA
  uid: string;
  parsed_leads: Array<
    IParsedLead & {
      campaign_name: string;
      campaign_id: string;
      leadgen_id: string;
      business_mobile?: string; // for ctwa
      business_product_or_service_description?: string; // for form lead email
    }
  >;
  platform?: AdPlatforms;
}

export interface ICtwaLead {
  created_time: number;
  // this will be used in chat link, to keep it smaller using meta_id instead of campaign_id
  id: string; // <wa_id>_<meta_id>
  meta_id: string;
  campaign_id: string;
  wa_id: string;
  field_data: Array<{
    name: string;
    label: string;
    values: string[];
  }>;
  client_uid: string;
  business_details?: IBusinessDetails;
}

export interface IGoogleLead {
  campaign_id: string;
  field_data: IParsedLead['field_data'];
  created_at: Timestamp;
  created_time: number; // in seconds (in sync with IMetaLead)
  uid: string;
  id: string;
}
