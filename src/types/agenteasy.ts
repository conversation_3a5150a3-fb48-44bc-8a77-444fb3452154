import { type Timestamp } from 'firebase-admin/firestore';
import { type IWaMessageToBeSentPayload } from '../modules/interakt/types';
import { type IWaMessage } from './whatsapp';

export interface IAgentEasyDataForCampaign {
  business_name: string;
  ad_name: string;
  questions: string[];
  lead_classification_criteria: object;
  campaign_id: string;
  uid: string;
  created_at: Timestamp;
  // business_waba_id?: string;
  // having campaign level check too apart from user level
  is_active: boolean;
}

export interface IAgentEasyLeadClassificationData {
  lead_summary?: string;
  lead_category?: 'HOT' | 'COLD' | 'WARM';
  source?: 'GROWEASY'; // only GrowEasy for now
  chat_doc_id: string;
  asked_questions?: Array<{
    question: string;
    answer: string;
  }>;
}

export interface IAgentEasyCampaignContext {
  full_name: string;
  campaign_id: string;
  uid: string;
  leadgen_id: string;
  classification_data?: IAgentEasyLeadClassificationData;
  chat_status?: 'OPEN' | 'CLOSED' | 'EXPIRED';
}

export interface IAgentEasyConversationDetails {
  created_at: Timestamp;
  campaign_context: IAgentEasyCampaignContext;
  user_last_reply_time?: Timestamp; // close chat post 24 hours of this
  previous_campaigns_contexts?: IAgentEasyCampaignContext[];
  updated_at: Timestamp;
  last_message?: IAgentEasyWaMessageDetails;
}

export enum AgentEasyMessageRole {
  ASSISTANT = 'ASSISTANT',
  USER = 'USER',
}

export interface IAgentEasyWaMessageDetails {
  role: AgentEasyMessageRole;
  time: Timestamp;
  payload: IWaMessageToBeSentPayload | IWaMessage;
}
