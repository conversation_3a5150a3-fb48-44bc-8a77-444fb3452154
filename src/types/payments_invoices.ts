import { type Timestamp } from 'firebase-admin/firestore';
import { type GrowEasyPartners, type AdPlatforms } from '.';
import { type Currency } from './campaign_details';

export enum GrowEasyOrderType {
  // launch = new campaign, extend = existing campaign
  LAUNCH = 'launch',
  EXTEND = 'extend',
}

export enum GrowEasyPaymentSource {
  GROWEASY_AI = 'GROWEASY_AI',
  GROWEASY_VIDEOS = 'GROWEASY_VIDEOS',
  GROWEASY_MASTER_CLASS = 'GROWEASY_MASTER_CLASS',
}

export interface ICampaignExtensionDetails {
  lifetime_budget?: number; // in paise or cents
  end_time: null | string;
  daily_budget?: number; // in paise or cents
}

export interface IOrderDetails {
  razorpay_payment_id?: string;
  razorpay_order_id: string;
  razorpay_signature?: string;
  status: 'created' | 'attempted' | 'paid';
  amount: number;
  currency: Currency;
  created_at: Timestamp;
  updated_at: Timestamp;
  campaign_id: string;
  uid: string;
  type: GrowEasyOrderType;
  campaign_extension_details?: ICampaignExtensionDetails;
  invoice_id?: string; // GrowEasy Invoice
  stripe_payment_intent_id?: string;
  platform?: AdPlatforms;
  xendit_invoice_id?: string;

  // Ad budget = order amount + ad_credit_to_be_consumed
  // payment by PG = order amount
  // if amount_to_be_consumed > 0, before initiating payment, check if enought credit is available
  ad_credit?: {
    amount_to_be_consumed: number; // in paise/cents/IDR
    transaction_id?: string | null;
  };
}

export interface IBillingDetails {
  uid: string;
  country_code: string;
  billing_email: string;
  billing_mobile: string;
  business_name: string;
  business_address: string;
  business_city: string;
  state_code?: string;
  postal_code: string;
  business_tax_id?: string;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export interface IInvoiceDetails {
  order_id: string;
  campaign_id: string;
  uid: string;
  id: string;
  created_at: Timestamp;
  issue_date: Timestamp;
  invoice_no: number;
  line_items: Array<{
    description: string;
    amount: string;
    quantity?: string;
    unit_price?: string;
  }>;
  calculation_details: Array<{
    key: string;
    value: string;
  }>;
  total_payable: string;
  billing_details: Partial<IBillingDetails>;
  campaign_details: {
    business_category: string;
    start_time: string;
    end_time: string;
    friendly_name?: string;
    product_or_service_description: string;
  };
  total_tax?: {
    sgst: number;
    cgst: number;
    igst: number;
  };
  partner?: GrowEasyPartners | null;
}

export interface IRazorpayWebhookEventBody {
  entity: string;
  account_id: string;
  event: string;
  contains: string[];
  payload: {
    payment: {
      entity: {
        id: string;
        entity: string;
        amount: number;
        currency: string;
        status: string;
        order_id: string;
        invoice_id: any;
        international: boolean;
        method: string;
        amount_refunded: number;
        refund_status: any;
        captured: boolean;
        description: string;
        card_id: any;
        bank: any;
        wallet: any;
        vpa: string;
        email: string;
        contact: string;
        notes: {
          campaign_id?: string;
          uid?: string;
          source: GrowEasyPaymentSource;
        };
        fee: number;
        tax: number;
        error_code: any;
        error_description: any;
        error_source: any;
        error_step: any;
        error_reason: any;
        acquirer_data: object;
        created_at: number;
        reward: any;
        upi: object;
        base_amount: number;
      };
    };
  };
  created_at: number;
}

export interface IRazorpayError {
  statusCode: string | number;
  error: {
    code: string;
    description: string;
    field?: any;
    source?: string;
    step?: string;
    reason?: string;
    metadata?: Record<string, string>;
  };
}

export interface IXenditWebhookEventBody {
  id: string;
  external_id: string;
  user_id: string;
  is_high: boolean;
  payment_method: string;
  status: string;
  merchant_name: string;
  amount: number;
  paid_amount: number;
  bank_code: string;
  paid_at: string;
  payer_email: string;
  description: string;
  adjusted_received_amount: number;
  fees_paid_amount: number;
  updated: string;
  created: string;
  currency: string;
  payment_channel: string;
  payment_destination: string;
}
