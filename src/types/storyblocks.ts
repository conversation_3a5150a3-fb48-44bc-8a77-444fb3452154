interface IPreviewUrls {
  _180p: string;
  _360p: string;
  _480p: string;
  _720p: string;
}

export interface IStoryblocksItem {
  title: string;
  thumbnail_url: string;
  id: number;
  contentClass: string;
  type: string;
  is_new: boolean;
  preview_urls: IPreviewUrls;
  duration: number;
  durationMs: number;
}

export interface IStoryblocksSearchResults {
  total_results: number;
  results: IStoryblocksItem[];
}
