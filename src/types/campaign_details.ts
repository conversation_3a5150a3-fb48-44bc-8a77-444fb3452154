import { type Timestamp } from 'firebase-admin/firestore';
import {
  type GrowEasyPartners,
  type AdPlatforms,
  type ISelfAdAccountConfigDetails,
} from '.';

export interface IBusinessDetails {
  business_category: string;
  product_or_service_description: string;
  product_or_service_offers_or_usp?: string;
  consumer_type?: 'B2B' | 'B2C' | 'All';
  ideal_customers?: string;
  website?: string;
  mobile?: string;
  country_code?: string;
  mobile_without_country_code?: string;
  business_name?: string;
  business_logo?: {
    square: {
      url: string;
      width: number;
      height: number;
    };
  };
}

export interface ILocationDetails {
  distance_unit: 'mile' | 'kilometer';
  key?: string;
  name?: string;
  region?: string;
  region_id?: string;
  radius?: number;
  country?: string;
  latitude?: number;
  longitude?: number;
  primary_city_id?: number;
  address_string?: string;
  type?: string;
}

export interface IGeoLocations {
  countries?: string[];
  cities?: ILocationDetails[];
  regions?: ILocationDetails[];
  neighborhoods?: ILocationDetails[];
  places?: ILocationDetails[];
  custom_locations?: ILocationDetails[];
}
export enum FlexibleTargetingItemType {
  behaviors = 'behaviors',
  interests = 'interests',
  life_events = 'life_events',
  education_statuses = 'education_statuses',
  industries = 'industries',
  work_positions = 'work_positions',
}

export interface IFlexibleTargetingItem {
  id: string;
  name: string;
  type?: FlexibleTargetingItemType;
  key?: string;
  conversion_probability?: 'low' | 'medium' | 'high';
}

export type IFlexibleSpecItem = Record<
  FlexibleTargetingItemType,
  Array<IFlexibleTargetingItem | string>
>;

export interface ITargeting {
  age_min: number; // min 18 globally, 20 Thailand, 21 Indonesia, max 25 (because of advantage+ audience)
  age_max: number; // 65, cannot be lower than 65 (because of advantage+ audience)
  age_range: number[]; // 21 to 65
  genders: number[];
  geo_locations: IGeoLocations;
  flexible_spec?: Array<Partial<IFlexibleSpecItem>>;
  // Reach people beyond your detailed targeting selections when it's likely to improve performance.
  targeting_optimization?: 'none' | 'expansion_all';
  // Let Meta ad technology automatically find the best audience for campaign
  targeting_automation?: {
    advantage_audience: 1 | 0;
  };
  publisher_platforms?: string[];
}

export interface ICurrencyBudget {
  exchange_rate?: number;
  lifetime_budget: number;
  daily_budget: number;
}

// In Razorpay order creation, multiply by 100 for currencies with 2 decimal places
export enum Currency {
  INR = 'INR', // 2 decimal (paise)
  USD = 'USD', // 2 decimal (cents)
  IDR = 'IDR', // 0 decimal (Rupiah has no subunits)
  PHP = 'PHP', // 2 decimal (centavo)
  THB = 'THB', // 2 decimal (satang)
  VND = 'VND', // 0 decimal (Dong has no subunits)
  MYR = 'MYR', // 2 decimal (sen)
}
/**
 * We are storing lifetime_budget or daily_budget in smalles subunit, e.g. paise for INR or IDR for IDR
 * This is so because Payment Gateways accept amount in this unit
 * Meta budget also accepts amount in paise
 *
 * For 2 decimal currencies: Divide this amount by 100 when converting into standard currency
 * For 0 decimal currencies: Multiple by 100 when passing amount to Meta / Google campaign budget
 */
export interface IBudgetAndScheduling {
  daily_budget: number; // in paise (currency depends on ad account, Groweasy here)
  start_time: string;
  // lifetime_budget = daily_budget * no_of_days
  lifetime_budget: number; // in paise (smallest sub unit)
  end_time: null | string;
  platform_fee_percentage?: number;
  // for PG - Razorpay/Stripe, Meta will always accept in INR
  // if it is USD, read usd object and so on
  currency?: Currency;
  usd?: ICurrencyBudget; // amounts in USD cents
  idr?: ICurrencyBudget; // amounts in IDR
  php?: ICurrencyBudget; // amounts in PHP centavo
  thb?: ICurrencyBudget; // amounts in THB satang
  vnd?: ICurrencyBudget; // amounts in VND
  myr?: ICurrencyBudget; // amounts in MYR sen
}

enum LeadsAdCtaType {
  APPLY_NOW = 'APPLY_NOW',
  DOWNLOAD = 'DOWNLOAD',
  GET_QUOTE = 'GET_QUOTE',
  LEARN_MORE = 'LEARN_MORE',
  SIGN_UP = 'SIGN_UP',
  SUBSCRIBE = 'SUBSCRIBE',
}

export interface IAdCopy {
  call_to_action_type: LeadsAdCtaType;
  description: string; // (25 chars)
  primary_text: string; // message / primary_text / body (125 chars)
  headline: string; // heading, title, headline, name ((40 chars))
}

export interface IGoogleAdCopy {
  description: string; // 90 characters,	At least 1 with 60 characters or less
  long_headline: string; // 90 characters
  headline: string; // 30 characters,	At least 1 with 15 characters or less
  short_headline: string; // 15 characters or less
  short_description: string; // 60 characters or less
}

export interface IAdBanner {
  image?: {
    hash: string;
    width: number;
    height: number;
    s3_url?: string;
  };
  banner_data?: {
    creative_title: string;
    call_out: string;
    call_to_action: string;
    creative_image_url: string;
    size: string;
    template_id: string;
  };
  hidden?: boolean; // won't be visible to user, for placement optimisation
}

export interface ILeadgenFormQuestion {
  type: 'FULL_NAME' | 'EMAIL' | 'PHONE' | 'CUSTOM';
  key: string;
  label?: string;
  options?: Array<{
    key: string;
    value: string;
  }>;
}

// https://developers.facebook.com/docs/graph-api/reference/page/leadgen_forms/
export interface ILeadgenForm {
  name: string;
  is_optimized_for_quality: boolean;
  // To filter out organic leads
  block_display_for_non_targeted_viewer: boolean;
  tracking_parameters?: Record<string, string>;
  questions: ILeadgenFormQuestion[];
  privacy_policy: {
    url: string;
    link_text: string;
  };
  follow_up_action_url: string;
  follow_up_action_text: string;
  question_page_custom_headline: string;
  context_card: {
    title: string;
    style: 'PARAGRAPH_STYLE' | 'LIST_STYLE';
    content: string[];
  };
}

export interface ICampaignConfig {
  fb_page_id: string;
  page_post_id?: string;
  ad_account_id?: string; // meta
  google_ad_account_id?: string;
  meta_sales_purchase_event_name?: string;
  google_custom_conversion_action_doc_id?: string;
  // enable conversion api sending events
  meta_capi_enabled?: boolean;
  partner?: GrowEasyPartners | null;
  advantage_campaign_budget?: boolean;
  // reading ad_account_id from here will take priority
  self_ad_account_configs?: ISelfAdAccountConfigDetails;
}

// represent language of Ad Copies, Creatives, Form questions
// to add a new language, make an entry here and add font support in canvas (banner)
export enum AdLanguage {
  ENGLISH = 'English',
  HINDI = 'Hindi',
  BENGALI = 'Bengali',
  FRENCH = 'French',
  GERMAN = 'German',
  GUJARATI = 'Gujarati',
  KANNADA = 'Kannada',
  MALAYALAM = 'Malayalam',
  MARATHI = 'Marathi',
  ODIA = 'Odia',
  PORTUGUESE = 'Portuguese',
  RUSSIAN = 'Russian',
  SPANISH = 'Spanish',
  TAMIL = 'Tamil',
  ARABIC = 'Arabic',
  TELUGU = 'Telugu',
  BAHASA_INDONESIA = 'Bahasa Indonesia',
}

export interface IAdVideo {
  id?: string; // meta ad library id
  video_url: string;
  youtube_video_id?: string;
  tiktok?: {
    video_id: string;
    thumbnail: {
      image_id: string;
      image_url: string;
    };
  };
}

export interface ICampaignDetails {
  business_details?: IBusinessDetails;
  targeting?: ITargeting;
  budget_and_scheduling?: IBudgetAndScheduling;
  ad_copies?: IAdCopy[];
  ad_banners?: IAdBanner[];
  leadgen_form?: ILeadgenForm;
  config?: ICampaignConfig;
  ad_videos?: IAdVideo[];
  ad_language?: AdLanguage;
  ai_assisted_product_usps?: string[];
}

export enum GrowEasyCampaignStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  ARCHIVED = 'ARCHIVED',
}

export enum MetaCampaignEffectiveStatus {
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  DELETED = 'DELETED',
  ARCHIVED = 'ARCHIVED',
  IN_PROCESS = 'IN_PROCESS',
  WITH_ISSUES = 'WITH_ISSUES',
}

// https://developers.facebook.com/docs/marketing-api/audiences/special-ad-category/
export enum MetaSpecialAdCategory {
  HOUSING = 'HOUSING',
  CREDIT = 'CREDIT',
  EMPLOYMENT = 'EMPLOYMENT',
  ISSUES_ELECTIONS_POLITICS = 'ISSUES_ELECTIONS_POLITICS',
  NONE = 'NONE',
}

// https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-issues-info/
export interface IMetaCampaignIssueInfo {
  error_code: number;
  error_message: string;
  error_summary: string;
  error_type: string;

  // Indicate level of issue, could be ad set or campaign
  level: string;
}

// https://developers.google.com/google-ads/api/performance-max/asset-requirements
export enum GoogleAssetFieldType {
  HEADLINE = 'HEADLINE',
  LONG_HEADLINE = 'LONG_HEADLINE',
  DESCRIPTION = 'DESCRIPTION',
  BUSINESS_NAME = 'BUSINESS_NAME',
  MARKETING_IMAGE = 'MARKETING_IMAGE',
  SQUARE_MARKETING_IMAGE = 'SQUARE_MARKETING_IMAGE',
  LOGO = 'LOGO', // 1:1, min 128 x 128
  PORTRAIT_MARKETING_IMAGE = 'PORTRAIT_MARKETING_IMAGE',
  YOUTUBE_VIDEO = 'YOUTUBE_VIDEO',
}

export interface IGoogleAdsData {
  media_assets?: {
    [field in
      | GoogleAssetFieldType.MARKETING_IMAGE
      | GoogleAssetFieldType.SQUARE_MARKETING_IMAGE
      | GoogleAssetFieldType.PORTRAIT_MARKETING_IMAGE
      | GoogleAssetFieldType.LOGO
      | GoogleAssetFieldType.YOUTUBE_VIDEO]: Array<{
      resource_name: string;
    }>;
  };
  ad_copies?: IGoogleAdCopy[];
  ad_videos?: Array<{
    video_url: string;
    youtube_video_id: string;
  }>;
  text_assets?: {
    [field in
      | GoogleAssetFieldType.BUSINESS_NAME
      | GoogleAssetFieldType.HEADLINE
      | GoogleAssetFieldType.LONG_HEADLINE
      | GoogleAssetFieldType.DESCRIPTION]: Array<{
      text: string;
      resource_name: string;
    }>;
  };
  campaign_resource?: string;
  campaign_budget_resource?: string;
  conversion_action_resource?: string;
  lead_form_url?: string;
  geo_locations?: IGoogleLocationDetails[];
  search_keywords?: string[];
  asset_group_resource?: string;
  ad_group_resource?: string;
  custom_conversion_goal_resource?: string;

  // format AW-CONVERSION_ID/CONVERSION_LABEL, e.g. AW-***********/1Rf3CN69vPsZEKz3i_Aq
  conversion_action_event_label?: string;

  lead_form_content?: {
    heading: string;
    subheading: string;
    description: string;
    value_props: string[];
    cta_title: string;
    cta_label: string;
  };

  search_keywords_suggestions?: Record<string, string[]>;
}

export interface ITiktokLocationDetails {
  geo: {
    description: string;
    geo_id: string;
    geo_type: string;
    parent_id: string;
    region_code: string;
  };
  name: string;
  status_info: {
    status: string;
  };
  targeting_type: string;
}

export interface ITiktokAdsData {
  geo_locations?: ITiktokLocationDetails[];
  campaign_id?: string;
  advertiser_id?: string;
  ad_ids?: string[];
  adgroup_id?: string;
}

// https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/
export interface ICampaign {
  id: string;
  name: string; // for BE, Ads manager
  friendly_name?: string; // For customers to avoid confusion when having same categories
  created_at: Timestamp;
  updated_at: Timestamp;
  status: GrowEasyCampaignStatus;

  // populate meta fields while interacting with Meta Marketing APIs
  meta_id?: string;
  details?: ICampaignDetails;
  uid: string; // firebase uid
  meta_created_time?: string;
  special_ad_categories: MetaSpecialAdCategory[];
  country_code?: string; // US/IN etc

  // https://developers.facebook.com/docs/marketing-api/adset/budget-limits/v18.0
  currency?: string;

  // Issues for this campaign that prevented it from delivering
  meta_issues_info?: IMetaCampaignIssueInfo[];
  meta_adset_id?: string;
  meta_ad_ids?: string[];
  meta_leadgen_form_id?: string;
  order_id?: string; // very first order, i.e. type=launch
  type: GROWEASY_CAMPAIGN_TYPE; // LEAD_FORM by default

  google_ads_data?: IGoogleAdsData;
  tiktok_ads_data?: ITiktokAdsData;
  platform?: AdPlatforms;
}

// https://developers.facebook.com/docs/graph-api/webhooks/reference/page/
export interface IWebhookLeadEventEntryChange {
  field: 'leadgen';
  value: {
    ad_id: string;
    form_id: string;
    leadgen_id: string;
    created_time: number;
    page_id: string;
    adgroup_id: string;
  };
}

export interface IMetaWebhookLeadEventBody {
  object: 'page';
  // An array containing an object describing the changes.
  // Multiple changes from different objects that are of the same type may be batched together.
  entry: Array<{
    id: string;
    time: number;
    changes: IWebhookLeadEventEntryChange[];
  }>;
}

export interface IGoogleLeadUserColumnData {
  column_name?: string;
  column_value?: {
    string_value?: string;
  };
  column_id?: string;
}

export interface IGoogleWebhookLeadEventBody {
  lead_id?: string;
  user_column_data?: IGoogleLeadUserColumnData[];
  api_version?: string;
  form_id?: number;
  campaign_id?: number;
  google_key?: string;
  is_test?: boolean;
  gcl_id?: string;
  adgroup_id?: number;
  creative_id?: number;
}

export enum GROWEASY_CAMPAIGN_TYPE {
  LEAD_FORM = 'LEAD_FORM',
  CTWA = 'CTWA',
  GOOGLE_P_MAX = 'GOOGLE_P_MAX',
  GOOGLE_SEARCH = 'GOOGLE_SEARCH',
  META_SALES = 'META_SALES',
  GOOGLE_CALL = 'GOOGLE_CALL',
  TIKTOK_CALL = 'TIKTOK_CALL',
  TIKTOK_WHATSAPP = 'TIKTOK_WHATSAPP',
}

export interface ICampaignInsightDetails {
  clicks: string;
  impressions: string;
  publisher_platform?: string;
  spend: string;
  reach: string;
  date_start: string;
  date_stop: string;
  account_id: string;
  cpc: string;
  ctr: string;
  leads?: number;
  conversions?: number; // google only
  // meta only
  // onsite_conversion.lead_grouped: All On-Facebook Leads
  // lead: All offsite leads plus all On-Facebook leads
  // offsite_conversion.fb_pixel_custom: Custom fb event
  // purchase: Standard Purchase event
  cost_per_action_type?: Array<{
    action_type: string;
    value: string;
  }>;
  // meta only: break_downs
  region?: string;
  body_asset?: {
    text: string;
  };
  media_asset?: {
    asset_type: 'image_asset' | 'video_asset';
    hash: string;
    // thumbnail url in case of video from BE, Meta might return actual video url
    thumbnail_url?: string;
    url: string;
  };
  age?: string;
  gender?: string;
  hourly_stats_aggregated_by_audience_time_zone?: string;
}

export type GoogleAdsMutateOperation =
  | {
      campaignBudgetOperation: {
        create: {
          resourceName: string;
          name: string;
          deliveryMethod: string;
          amountMicros: number;
          explicitlyShared: boolean;
        };
      };
    }
  | {
      campaignBudgetOperation: {
        update: {
          resourceName: string;
          amountMicros: number;
        };
        updateMask: 'amountMicros';
      };
    }
  | {
      campaignOperation: {
        create: {
          resourceName: string;
          status: string;
          advertisingChannelType: string;
          maximizeConversions?: Record<string, unknown>;
          urlExpansionOptOut?: boolean;
          name: string;
          campaignBudget: string;
          startDate: string;
          endDate: string;
          networkSettings?: {
            targetGoogleSearch: boolean;
            targetSearchNetwork: boolean;
            targetContentNetwork: boolean;
            targetPartnerSearchNetwork: boolean;
          };
          containsEuPoliticalAdvertising: 'DOES_NOT_CONTAIN_EU_POLITICAL_ADVERTISING';
        };
      };
    }
  | {
      // https://developers.google.com/google-ads/api/rest/reference/rest/v20/Campaign
      campaignOperation: {
        update: {
          resourceName: string;
          status?: string;
          endDate?: string;
        };
        updateMask: string;
      };
    }
  | {
      campaignCriterionOperation: {
        create: {
          campaign: string;
          location: {
            geoTargetConstant: string;
          };
        };
      };
    }
  | {
      assetGroupOperation: {
        create: {
          name: string;
          resourceName: string;
          campaign: string;
          finalUrls: string[];
        };
      };
    }
  | {
      assetGroupSignalOperation: {
        create: {
          assetGroup: string;
          searchTheme: {
            text: string;
          };
        };
        exemptPolicyViolationKeys?: Array<{
          policyName: string;
          violatingText: string;
        }>;
      };
    }
  | {
      assetGroupAssetOperation: {
        create: {
          assetGroup: string;
          asset: string;
          fieldType: GoogleAssetFieldType; // or specific string types representing asset types
        };
      };
    }
  | {
      conversionActionOperation: {
        create: {
          resourceName: string;
          name: string;
          type: 'WEBPAGE' | 'CLICK_TO_CALL' | 'AD_CALL';
          category: 'SUBMIT_LEAD_FORM' | 'PURCHASE' | 'PHONE_CALL_LEAD';
          status: 'ENABLED';
          valueSettings: {
            defaultValue: number;
            alwaysUseDefaultValue: boolean;
          };
          countingType: 'ONE_PER_CLICK';
          attributionModelSettings: {
            attributionModel: 'GOOGLE_ADS_LAST_CLICK';
          };
        };
      };
    }
  | {
      customConversionGoalOperation: {
        create: {
          name: string;
          conversionActions: string[];
        };
      };
    }
  | {
      conversionGoalCampaignConfigOperation: {
        updateMask: 'customConversionGoal';
        update: {
          goalConfigLevel: 'CAMPAIGN';
          customConversionGoal: string;
          resourceName: string;
        };
      };
    }
  | {
      adGroupOperation: {
        create: {
          name: string;
          resourceName: string;
          campaign: string;
          status: 'ENABLED';
        };
      };
    }
  | {
      adGroupCriterionOperation: {
        create: {
          adGroup: string;
          status: 'ENABLED';
          keyword?: {
            text: string;
            matchType: 'EXACT' | 'BROAD' | 'PHRASE';
          };
          ageRange?: {
            type: string;
          };
          gender?: {
            type: string;
          };
        };
        exemptPolicyViolationKeys?: Array<{
          policyName: string;
          violatingText: string;
        }>;
      };
    }
  | {
      adGroupAdOperation: {
        create: {
          adGroup: string;
          status: 'ENABLED';
          ad: {
            responsiveSearchAd?: {
              // upto 15 headlines, max 30 characters
              headlines: Array<{
                text: string;
              }>;
              // max 4 descriptions, upto 90 chars
              descriptions: Array<{
                text: string;
              }>;
            };
            callAd?: {
              businessName: string; // Max 25 characters
              headline1: string; // Max 30 characters
              headline2: string; // Max 30 characters
              description1: string; // Max 90 characters
              description2: string; // Max 90 characters
              countryCode: string; // e.g., "IN"
              phoneNumber: string; // e.g., "+************"
              phoneNumberVerificationUrl?: string; // Required if account is not whitelisted
              callTracked?: boolean;
              conversionAction?: string; // Resource name of conversion action
              conversionReportingState?:
                | 'DISABLED'
                | 'USE_ACCOUNT_LEVEL_CALL_CONVERSION_ACTION'
                | 'USE_RESOURCE_LEVEL_CALL_CONVERSION_ACTION';
              disableCallConversion?: boolean;
            };
            finalUrls: string[];
          };
        };
      };
    }
  | {
      assetOperation: {
        create: {
          resourceName: string;
          callAsset: {
            countryCode: string;
            phoneNumber: string;
            callConversionAction: string;
            callConversionReportingState:
              | 'USE_RESOURCE_LEVEL_CALL_CONVERSION_ACTION'
              | 'USE_ACCOUNT_LEVEL_CALL_CONVERSION_ACTION';
          };
        };
      };
    }
  | {
      campaignAssetOperation: {
        create: {
          campaign: string;
          asset: string;
          fieldType: 'CALL';
        };
      };
    };

interface IGoogleCampaignMetrics {
  clicks: string;
  costMicros: string;
  ctr: number;
  averageCpc: number;
  impressions: string;
  conversions: number;
  allConversions: number;
}

interface IGoogleCallMetrics {
  callDurationSeconds: string;
  startCallDateTime: string;
  endCallDateTime: string;
  callStatus: string;
}
export interface IGoogleCampaignInsights {
  campaign: {
    resourceName: string;
    status: string;
    name: string;
  };
  metrics: IGoogleCampaignMetrics;
  segments: {
    device: 'MOBILE' | 'TABLET' | 'CONNECTED_TV';
  };
}

export interface IGoogleSearchKeywordsInsights {
  metrics: IGoogleCampaignMetrics;
  adGroupCriterion: {
    resourceName: string;
    keyword: {
      matchType: 'EXACT' | 'BROAD' | 'PHRASE';
      text: string;
    };
  };
  keywordView: {
    resourceName: string;
  };
}

export interface IGoogleSearchLocationsInsights {
  metrics: IGoogleCampaignMetrics;
  segments: {
    geoTargetCity: string;
    geoTargetCityCanonicalName?: string;
  };
  geographicView: {
    resourceName: string;
  };
}

export interface IGoogleCallsInsights {
  campaign: {
    resourceName: string;
  };
  callView: IGoogleCallMetrics;
}

export interface IGoogleLocationDetails {
  geoTargetConstant: {
    resourceName: string;
    id: string;
    name: string;
    countryCode: string;
    canonicalName: string;
  };
}

export interface IGoogleMediaAssetDetails {
  asset: {
    resourceName: string;
    type: string;
    youtubeVideoAsset?: {
      youtubeVideoId: string;
      youtubeVideoTitle: string;
    };
    imageAsset?: {
      mimeType: string;
      fullSize: {
        url: string;
      };
      fileSize: string;
    };
    id: string;
    name: string;
  };
}

export interface IGoogleKeywordIdeas {
  keywordIdeaMetrics: {
    competition: string;
    monthlySearchVolumes: Array<{
      month: string;
      year: string;
      monthlySearches: string;
    }>;
    avgMonthlySearches: string | number;
    competitionIndex: string;
    lowTopOfPageBidMicros: string;
    highTopOfPageBidMicros: string;
  };
  text: string;
  closeVariants?: string[];
}

export interface IGoogleCustomConversionAction {
  id: string;
  conversion_action_resource: string;
  created_at: Timestamp;
  category: 'SUBMIT_LEAD_FORM' | 'PURCHASE';
  conversion_action_event_label: string;
  // No two custom goals can have the same conversion action list.
  custom_conversion_goal_resource: string;
  ad_account_id: string;
}

export interface ITiktokCampaignInsights {
  dimensions: {
    campaign_id: string;
  };
  metrics: {
    cpc: string;
    clicks: string;
    ctr: string;
    conversion: string;
    impressions: string;
    spend: string;
    conversion_rate: string;
  };
}
