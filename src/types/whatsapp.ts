import { type Timestamp } from 'firebase-admin/firestore';
import {
  type ICampaignDetails,
  type ILeadgenFormQuestion,
} from './campaign_details';

export enum WaMessageType {
  text = 'text',
  reaction = 'reaction',
  image = 'image',
  location = 'location',
  contacts = 'contacts',
  sticker = 'sticker',
  interactive = 'interactive',
  template = 'template',
  button = 'button',
}

export interface IWaMessageReferral {
  // The Meta URL that leads to the ad or post clicked by the customer.
  source_url: string;
  // The type of the ad’s source; ad or post.
  source_type: string;
  // Meta ID for an ad or a post.
  source_id: string;
  // Headline used in the ad or post.
  headline: string;
  // Body for the ad or post.
  body: string;
  // URL of the image, when media_type is an image.
  image_url: string;
  // URL of the video, when media_type is a video.
  video_url: string;
  // URL for the thumbnail, when media_type is a video.
  thumbnail_url: string;
  // Click ID generated by Meta for ads that click to WhatsApp.
  ctwa_clid: string;
}

interface IWaBaseMessage {
  from: string;
  id: string;
  timestamp: string;
  referral?: IWaMessageReferral;
}

export interface IWaTextMessage extends IWaBaseMessage {
  text: {
    body: string;
  };
  type: WaMessageType.text;
}

export interface IWaButtonMessage extends IWaBaseMessage {
  button: {
    text: string;
  };
  type: WaMessageType.button;
}

export interface IWaReactionMessage extends IWaBaseMessage {
  reaction: {
    message_id: string;
  };
  type: WaMessageType.reaction;
}

export interface IWaImageMessage extends IWaBaseMessage {
  image: {
    mime_type: string;
    sha256: string;
    id: string;
  };
  type: WaMessageType.image;
}

export interface IWaLocationMessage extends IWaBaseMessage {
  location: {
    latitude: number;
    longitude: number;
  };
  type: WaMessageType.location;
}

export interface IWaContactsMessage extends IWaBaseMessage {
  contacts: Array<{
    name: {
      first_name: string;
      middle_name: string;
      last_name: string;
      formatted_name: string;
      prefix: string;
    };
    phones: Array<{
      phone: string;
      wa_id: string;
      type: 'MOBILE';
    }>;
  }>;
  type: WaMessageType.contacts;
}

export interface IWaStickerMessage extends IWaBaseMessage {
  sticker: {
    mime_type: string;
    sha256: string;
    id: string;
    animated: boolean;
  };
  type: WaMessageType.sticker;
}

export interface IWaInteractiveMessage extends IWaBaseMessage {
  interactive: {
    type: 'list_reply' | 'button_reply';
    list_reply?: {
      id: string;
      title: string;
      description: string;
    };
    button_reply?: {
      id: string;
      title: string;
    };
  };
  type: WaMessageType.interactive;
}

export type IWaMessage =
  | IWaTextMessage
  | IWaReactionMessage
  | IWaImageMessage
  | IWaLocationMessage
  | IWaContactsMessage
  | IWaStickerMessage
  | IWaInteractiveMessage
  | IWaButtonMessage;

// either contacts + messages or just statuses
export interface IWebhookWhatsappEventEntryChange {
  value: {
    messaging_product: 'whatsapp';
    metadata: {
      display_phone_number: string;
      phone_number_id: string;
    };
    contacts?: Array<{
      profile: {
        name: string;
      };
      wa_id: string;
    }>;
    messages?: IWaMessage[];
    statuses?: Array<{
      id: string;
      status: string;
      timestamp: string;
      recipient_id: string;
      conversation: {
        id: string;
        origin: {
          type: string;
        };
      };
      pricing: {
        billable: boolean;
        pricing_model: string;
        category: string;
      };
    }>;
  };
  field: 'messages';
}

export interface IMetaWebhookWhatsappEventBody {
  object: 'whatsapp_business_account';
  // An array containing an object describing the changes.
  // Multiple changes from different objects that are of the same type may be batched together.
  entry: Array<{
    id: string;
    changes: IWebhookWhatsappEventEntryChange[];
  }>;
}

export enum CtwaChatState {
  'NONE_ASKED' = 'NONE_ASKED',
  'QUESTION4_ASKED' = 'QUESTION4_ASKED',
  'QUESTION5_ASKED' = 'QUESTION5_ASKED',
  'ALL_ASKED' = 'ALL_ASKED',
}

export interface ICtwaContextDetails {
  referral: IWaMessageReferral;
  meta_ad_ids: string[];
  meta_adset_id: string;
  meta_id: string;
  campaign_id: string;
  client_details: {
    uid: string;
  };
  business_details: ICampaignDetails['business_details'];
  leadgen_form: {
    questions: ILeadgenFormQuestion[];
    follow_up_action_url: string;
    follow_up_action_text: string;
    // answers of leadgen form questions
    field_data: Array<{
      name: string;
      label: string;
      values: string[];
    }>;
  };
  created_at: Timestamp;
  updated_at: Timestamp;
  chat_state: CtwaChatState;
}
export interface ICtwaConversationDetails {
  ctwa_context: ICtwaContextDetails;
  created_at: Timestamp;
  updated_at: Timestamp;
  previous_ctwa_contexts: ICtwaContextDetails[];
}

export interface IChatMessageCtaUrlAction {
  name: 'cta_url';
  parameters: {
    display_text: string;
    url: string;
  };
}

export interface IChatMessageButtonsAction {
  buttons: Array<{
    type: 'reply';
    reply: {
      id: string;
      title: string;
    };
  }>;
}

export interface IChatMessageDropdownAction {
  button: string;
  sections: Array<{
    rows: Array<{
      description: string;
      id: string;
      title: string;
    }>;
  }>;
}

export interface ICtwaChatMessage {
  messaging_product?: 'whatsapp';
  type: WaMessageType;
  time?: Timestamp;
  to?: string;
  text?: {
    body?: string;
  };
  interactive?: {
    type: 'cta_url' | 'button' | 'list';
    header?: {
      type: 'text';
      text: string;
    };
    body: {
      text?: string;
    };
    action?:
      | IChatMessageCtaUrlAction
      | IChatMessageButtonsAction
      | IChatMessageDropdownAction;
  };
  template?: {
    name: string;
    language: {
      code: string;
    };
    components?: Array<{
      type: 'body';
      parameters: Array<{
        type: 'text';
        text: string;
        parameter_name?: string;
      }>;
    }>;
  };
}

export interface IGrowEasyWaConversationDetails {
  created_at: Timestamp;
  updated_at: Timestamp;
  profile_name: string;
  mobile: string;
  // last received or last sent message
  last_message: IWaMessage | ICtwaChatMessage;
}
