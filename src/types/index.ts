import { type Request } from 'express';
import { type Timestamp } from 'firebase-admin/firestore';
import {
  type AdLanguage,
  type Currency,
  type IBusinessDetails,
  type ITargeting,
} from './campaign_details';
import { type GrowEasyPaymentSource } from './payments_invoices';

export interface IAdCreativeData {
  creative_title: string;
  call_out: string;
  call_to_action: string;
  creative_image_keywords: string[];
  creative_image_url?: string;
}

export interface IGenericErrorResponse {
  error: {
    message: string;
  };
}

export interface IAuthUser {
  name: string;
  uid: string;
  email?: string;
  mobile?: string;
  partner?: GrowEasyPartners;
}

export interface IApiRequest extends Request {
  user?: IAuthUser;
}

export interface IParamsForAdCreative {
  business_details: IBusinessDetails;
  targeting: ITargeting;
  ad_language?: AdLanguage;
}

export interface IParamsForPromptGeneration {
  business_details: IBusinessDetails;
  ad_language?: AdLanguage;
  ai_assisted_product_usps: string[];
}

export interface IBusinessCategory {
  'business-category': string;
  id: number;
  'targeting-keywords': string[];
  'top-3-domestic-players': string[];
  'top-3-global-players': string[];
}

export interface IVerifyPaymentPayload {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
  razorpay_webhook_body?: string;
  payment_source?: GrowEasyPaymentSource; // it will be available in webhook but not yet in verify-payment API
}

export enum GrowEasyPartners {
  NIVIDA = 'NIVIDA',
  ZENDOT = 'ZENDOT',
  GENIUS_ADS = 'GENIUS_ADS',
  AD_GLOBAL_AI = 'AD_GLOBAL_AI',
}

export interface IIncrementalCounter {
  count: number;
  updated_at: Timestamp;
}

export interface IUserProfile {
  name: string;
  uid: string;
  email: string;
  mobile?: string;
  business_name?: string;
  created_at: Timestamp;
  updated_at: Timestamp;
  is_affiliate_marketing?: boolean;
  monthly_marketing_budget_currency?: string;
  mobile_dial_code?: string;
  whatsapp_opt_in?: boolean;
  acquisition_source?: 'web' | 'android' | 'ios';
  feature_flags?: {
    agenteasy_enabled?: boolean;
  };
  partner?: GrowEasyPartners;
  number_of_employees?: string;
  has_calling_team?: 'yes' | 'no' | 'online_sales';
  monthly_marketing_budget?: string; // free text
  whatsapp_id?: string; // to trace back user from whatsapp message
}

export interface IProcessedFbLoginResponse {
  assigned_pages?: Array<{
    name: string;
    id: string;
    tasks: string[];
    access_token: string; // page access token
  }>;
  account_details?: {
    name: string;
    id: string;
  };
}

export type IProcessedFbLoginDbData = {
  login_result: IFbLoginStatusResult;
  access_token?: string;
  access_token_type?: 'SUAT' | 'UAT';
  created_at: Timestamp;
  updated_at: Timestamp;
} & IProcessedFbLoginResponse;

export interface IFbLoginStatusResult {
  status: 'connected' | 'not_authorized' | 'unknown';
  authResponse?: {
    accessToken: string; // for UAT
    expiresIn: number | null; // null in case of SUAT, can be 0 too (never expire in UAT)
    reauthorize_required_in: string;
    signedRequest: string;
    userID: string;
    code?: string; // for SUAT, i.e. System User Access Token
  };
}

export interface IAdCreditsTransaction {
  type: 'CREDIT' | 'DEBIT';
  created_at?: Timestamp;
  currency: Currency;
  value: number;
  description: string;
  campaign_id: string;
}

export interface IAdCreditsBalance {
  usd: number;
  inr: number;
  idr: number;
  php: number;
  thb: number;
  vnd: number;
  myr: number;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export interface IVideoProductionResponse {
  en: IVideoScriptData[];
  hi: IVideoScriptData[];
}

export interface IVideoScriptData {
  script: string;
  narration: string;
}

export interface IVideoOrdersReqPayload {
  user_details: {
    name: string;
    email: string;
    mobile: string;
  };
  video_details: Array<{
    script: string;
    actor_id: string;
  }>;
}

export interface IVideoOrdersResPayload {
  razorpay_order_id: string;
  price_per_video: number;
  quantity: number;
  gst_percentage: number;
  amount: number;
  currency: 'INR' | 'USD';
}

export interface IPaymentLinkPayload {
  amount: number; // in Rs, internally will get converted into paise
  customer: {
    name: string;
    contact: string;
    email: string;
  };
  description: string;
}

export interface IVideoOrderDetails {
  razorpay_payment_id?: string;
  razorpay_order_id: string;
  razorpay_signature?: string;
  status: 'created' | 'attempted' | 'paid';
  amount: number;
  currency: 'INR' | 'USD';
  created_at: Timestamp;
  updated_at: Timestamp;
  user_details: IVideoOrdersReqPayload['user_details'];
  video_details: IVideoOrdersReqPayload['video_details'];
}

export interface IGoogleAdsError {
  code: number;
  message: string;
  status: string;
  details: Array<{
    '@type': string;
    errors: Array<{
      errorCode: {
        campaignError?: string;
        mutateError?: string;
        conversionActionError?: string;
        assetGroupSignalError?: string;
      };
      message: string;
      details?: {
        policyViolationDetails: {
          externalPolicyDescription: string;
          key: {
            policyName: string;
            violatingText: string;
          };
          externalPolicyName: string;
          isExemptible: boolean;
        };
      };
    }>;
    requestId: string;
  }>;
}

export interface IMasterClassRegistrationDetails {
  name: string;
  email: string;
  mobile: string;
  profession?: string;
}

export interface IMasterClassOrderDetails {
  razorpay_payment_id?: string;
  razorpay_order_id: string;
  razorpay_signature?: string;
  status: 'created' | 'attempted' | 'paid';
  amount: number;
  currency: 'INR' | 'USD';
  created_at: Timestamp;
  updated_at: Timestamp;
  registration_details: IMasterClassRegistrationDetails;
}

export interface IBusinessDetailsFromWebsite {
  business_name: string;
  business_square_logo_url: string;
  business_logo_url: string;
  business_description: string;
  business_usp: string;
}

export enum AdPlatforms {
  GOOGLE = 'GOOGLE',
  META = 'META',
  TIKTOK = 'TIKTOK',
}

export interface IMetaAdCreativeDetails {
  thumbnail_url: string;
  id: string;
  image_hash?: string; // placement not optimised
  video_id?: string;
  object_story_spec?: {
    video_data?: {
      video_id: string;
      image_url: string;
      image_hash: string;
    };
  };
}

export interface IMetaTokenDebugDetails {
  app_id: string;
  type: string;
  application: string;
  data_access_expires_at: number;
  expires_at: number;
  is_valid: boolean;
  issued_at: number;
  scopes: string[];
  granular_scopes: Array<{
    scope: string;
    target_ids: string[];
  }>;
  user_id: string;
}

export interface IWabaPhoneNumberDetails {
  verified_name: string;
  code_verification_status: string;
  display_phone_number: string;
  quality_rating: string;
  platform_type: string;
  last_onboarded_time: string;
  id: string;
}

export interface IProcessedWabaOnboardingResponse {
  access_token: string;
  token_details: IMetaTokenDebugDetails;
  waba_id: string;
  phone_numbers: IWabaPhoneNumberDetails[];
  pin: string;
}

export interface ISelfAdAccountConfigDetails {
  ad_account_id: string;
  currency: Currency;
}

export type ISelfAdAccountConfigs = {
  [key in AdPlatforms]?: ISelfAdAccountConfigDetails;
};

export interface ITiktokApiError {
  code: number;
  message: string;
}
