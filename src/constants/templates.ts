import { type IBannerTemplate } from '../types/banner_template';

const templates: IBannerTemplate[] = [
  /* {
    id: '1l',
    description: 'Landscape banner with yellow and white theme',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/1/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 872,
          y: 130,
          width: 820,
          height: 820,
        },
        imageProps: {
          borderRadius: 410,
          borderWidth: 0,
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=853&h=853',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 73,
          y: 126,
          width: 727,
          height: 198,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 700,
            fontSize: 108,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 73,
          y: 405,
          width: 727,
          height: 234,
        },
        texts: [
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 400,
            fontSize: 54,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 73,
          y: 676,
          width: 426,
          height: 116,
        },
        buttonProps: {
          borderRadius: 16,
          backgroundColor: '#2A2829',
          paddingX: 50,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 700,
          fontSize: 60,
          fontColor: '#FEAA07',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '1s',
    description: 'Square banner with yellow and white theme',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/1/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 519,
          y: 406,
          width: 727,
          height: 727,
        },
        imageProps: {
          borderRadius: 363.5,
          borderWidth: 0,
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=727&h=727',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 77,
          y: 129,
          width: 792,
          height: 212,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 77,
          y: 437,
          width: 445,
          height: 239,
        },
        texts: [
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 400,
            fontSize: 36,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 77,
          y: 875,
          width: 382,
          height: 103,
        },
        buttonProps: {
          borderRadius: 16,
          backgroundColor: '#2A2829',
          paddingX: 40,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 700,
          fontSize: 44,
          fontColor: '#FEAA07',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '1p',
    description: 'Portrait banner with yellow and white theme',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/1/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 249,
          y: 895,
          width: 1024,
          height: 1024,
        },
        imageProps: {
          borderRadius: 512,
          borderWidth: 0,
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=727&h=727',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 89,
          y: 169,
          width: 427,
          height: 212,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 89,
          y: 477,
          width: 870,
          height: 245,
        },
        texts: [
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 400,
            fontSize: 36,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 90,
          y: 757,
          width: 382,
          height: 103,
        },
        buttonProps: {
          borderRadius: 16,
          backgroundColor: '#2A2829',
          paddingX: 40,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 700,
          fontSize: 44,
          fontColor: '#FEAA07',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  }, */
  {
    id: '2l',
    description: 'Landscape banner with dark theme',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/2/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 129,
          y: 140,
          width: 1327,
          height: 423,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 700,
            fontSize: 120,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 700,
            fontSize: 60,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 129,
          y: 606,
          width: 351,
          height: 128,
        },
        buttonProps: {
          borderRadius: 8,
          backgroundColor: '#EBB941',
          paddingX: 44,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 700,
          fontSize: 68,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '2s',
    description: 'Square banner with dark theme',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/2/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 75,
          y: 191,
          width: 863,
          height: 485,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 700,
            fontSize: 44,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 75,
          y: 725,
          width: 271,
          height: 96,
        },
        buttonProps: {
          borderRadius: 8,
          backgroundColor: '#EBB941',
          paddingX: 28,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 700,
          fontSize: 44,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '2p',
    description: 'Portrait banner with dark theme',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/2/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 99,
          y: 163,
          width: 843,
          height: 638,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 700,
            fontSize: 44,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 99,
          y: 1499,
          width: 271,
          height: 96,
        },
        buttonProps: {
          borderRadius: 8,
          backgroundColor: '#EBB941',
          paddingX: 28,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 700,
          fontSize: 44,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  /* {
    id: '3l',
    description: '',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696670311878',
        zIndex: 0,
        type: 'image',
        container: {
          x: 940,
          y: 98,
          width: 493,
          height: 735,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=493&h=735',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696670330905',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/3/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696670411315',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 748,
          height: 404,
          x: 95,
          y: 125,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 800,
            fontSize: 90,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 58,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696670469736',
        zIndex: 2,
        type: 'button',
        container: {
          x: 95,
          y: 661,
          width: 461,
          height: 115,
        },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#ffffff',
          paddingX: 34,
          align: 'center',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '3s',
    description: '',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696676281704',
        zIndex: 0,
        type: 'image',
        container: {
          x: 549,
          y: 178,
          width: 472,
          height: 703,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=472&h=703',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696676341176',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/3/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696676376052',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 445,
          height: 603,
          x: 68,
          y: 133,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 800,
            fontSize: 92,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 44,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696676464311',
        zIndex: 2,
        type: 'button',
        container: {
          x: 68,
          y: 894,
          width: 355,
          height: 78,
        },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#ffffff',
          paddingX: 28,
          align: 'center',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '3p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696708643779',
        zIndex: 0,
        type: 'image',
        container: {
          x: 225,
          y: 765,
          width: 642,
          height: 957,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=642&h=957',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696708671146',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/3/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696708727396',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 712,
          height: 350,
          x: 90,
          y: 150,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 800,
            fontSize: 123,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696708804562',
        zIndex: 2,
        type: 'button',
        container: {
          x: 88,
          y: 588,
          width: 374,
          height: 88,
        },
        buttonProps: {
          borderRadius: 7,
          backgroundColor: '#ffffff',
          paddingX: 28,
          align: 'center',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 50,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '4l',
    description: 'Landscape banner with dark-blue and green theme',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 852,
          y: 160,
          width: 778,
          height: 743,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=778&h=743',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/4/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 72,
          y: 173,
          width: 700,
          height: 325,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 500,
            fontSize: 56,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 72,
          y: 632,
          width: 500,
          height: 121,
        },
        buttonProps: {
          borderRadius: 6,
          backgroundColor: '#ffffff',
          paddingX: 64,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 50,
          fontColor: '#022C44',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '4s',
    description: 'Square banner with dark-blue and green theme',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 571,
          width: 728,
          height: 509,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=728&h=509',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/4/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 200,
          y: 173,
          width: 680,
          height: 232,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 400,
            fontSize: 36,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 290,
          y: 496,
          width: 500,
          height: 92,
        },
        buttonProps: {
          borderRadius: 6,
          backgroundColor: '#ffffff',
          paddingX: 28,
          align: 'center',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 40,
          fontColor: '#022C44',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '4p',
    description: 'Portrait banner with dark-blue and green theme',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 187,
          y: 134,
          width: 968,
          height: 709,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=968&h=709',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/4/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 2,
        type: 'textArr',
        container: {
          x: 114,
          y: 983,
          width: 644,
          height: 410,
        },
        texts: [
          {
            value: 'This is a Header',
            fontWeight: 700,
            fontSize: 120,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 400,
            fontSize: 44,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 114,
          y: 1610,
          width: 500,
          height: 129,
        },
        buttonProps: {
          borderRadius: 6,
          backgroundColor: '#ffffff',
          paddingX: 76,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 45,
          fontColor: '#022C44',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '5l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696916314273',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/5/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696916341346',
        zIndex: 1,
        type: 'image',
        container: {
          x: 812,
          y: 95,
          width: 720,
          height: 613,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=720&h=613',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696916468787',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 637,
          height: 466,
          x: 111,
          y: 199,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 93,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 400,
            fontSize: 45,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696916592898',
        zIndex: 2,
        type: 'button',
        container: {
          x: 808,
          y: 710,
          width: 727,
          height: 123,
        },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#eeb860',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'GET IT NOW',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '5s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696916314273',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/5/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696916341346',
        zIndex: 1,
        type: 'image',
        container: {
          x: 155,
          y: 478,
          width: 768,
          height: 428,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=773&h=431',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696916468787',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 775,
          height: 326,
          x: 152,
          y: 131,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 88,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 36,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696916592898',
        zIndex: 2,
        type: 'button',
        container: {
          x: 153,
          y: 907,
          width: 773,
          height: 66,
        },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#eeb860',
          paddingX: 22,
          align: 'left',
        },
        textProps: {
          value: 'GET IT NOW',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '5p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696916314273',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/5/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696916341346',
        zIndex: 1,
        type: 'image',
        container: {
          x: 209,
          y: 817,
          width: 870,
          height: 842,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=873&h=842',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696916468787',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 867,
          height: 344,
          x: 131,
          y: 306,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 104,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 400,
            fontSize: 44,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696916592898',
        zIndex: 2,
        type: 'button',
        container: {
          x: 207,
          y: 1673,
          width: 773,
          height: 86,
        },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#eeb860',
          paddingX: 33,
          align: 'left',
        },
        textProps: {
          value: 'GET IT NOW',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '6l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696855823287',
        zIndex: 0,
        type: 'image',
        container: {
          x: 616,
          y: 0,
          width: 1000,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1000&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696855846149',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/6/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696856198723',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 694,
          height: 423,
          x: 63,
          y: 171,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 72,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 50,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696916592898',
        zIndex: 2,
        type: 'button',
        container: {
          x: 80,
          y: 680,
          width: 220,
          height: 104,
        },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#7DE708',
          paddingX: 16,
          align: 'center',
        },
        textProps: {
          value: 'GET IT NOW',
          fontWeight: 600,
          fontSize: 60,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '6s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696856471251',
        zIndex: 0,
        type: 'image',
        container: {
          x: 289,
          y: 0,
          width: 934,
          height: 1176,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=934&h=1176',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696856498276',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/6/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696856603447',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 443,
          height: 353,
          x: 70,
          y: 418,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 88,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 36,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696916592898',
        zIndex: 2,
        type: 'button',
        container: {
          x: 80,
          y: 866,
          width: 220,
          height: 102,
        },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#7DE708',
          paddingX: 16,
          align: 'center',
        },
        textProps: {
          value: 'GET IT NOW',
          fontWeight: 600,
          fontSize: 60,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '6p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696856471251',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 661,
          width: 1080,
          height: 1407,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1407',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696856498276',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/6/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696856603447',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 822,
          height: 386,
          x: 72,
          y: 142,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 120,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696916592898',
        zIndex: 2,
        type: 'button',
        container: {
          x: 80,
          y: 614,
          width: 220,
          height: 102,
        },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#7DE708',
          paddingX: 16,
          align: 'center',
        },
        textProps: {
          value: 'GET IT NOW',
          fontWeight: 600,
          fontSize: 60,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '7l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696698772454',
        zIndex: 0,
        type: 'image',
        container: {
          x: 892,
          y: 157,
          width: 592,
          height: 587,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 16,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=592&h=587',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696698785582',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 16,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/7/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696699084537',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 604,
          height: 382,
          x: 83,
          y: 205,
        },
        texts: [
          {
            value: 'This is a HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#51fffc',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696699271257',
        zIndex: 2,
        type: 'button',
        container: {
          x: 83,
          y: 683,
          width: 313,
          height: 96,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ff5733',
          paddingX: 50,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 46,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '7s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696698772454',
        zIndex: 0,
        type: 'image',
        container: {
          x: 449,
          y: 454,
          width: 588,
          height: 584,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=588&h=584',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696698785582',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/7/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696699084537',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 488,
          height: 230,
          x: 60,
          y: 134,
        },
        texts: [
          {
            value: 'This is a HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#51fffc',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1696699084537',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 326,
          height: 176,
          x: 60,
          y: 448,
        },
        texts: [
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696699271257',
        zIndex: 2,
        type: 'button',
        container: {
          x: 60,
          y: 748,
          width: 325,
          height: 105,
        },
        buttonProps: {
          borderRadius: 8,
          backgroundColor: '#ff5733',
          paddingX: 40,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 46,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '7p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696871409455',
        zIndex: 0,
        type: 'image',
        container: {
          x: 91,
          y: 644,
          width: 899,
          height: 891,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=696&h=723',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696871491456',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/7/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696871638960',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 651,
          height: 395,
          x: 214,
          y: 118,
        },
        texts: [
          {
            value: 'This is a Heading',
            fontWeight: 600,
            fontSize: 96,
            fontColor: '#51fffc',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696871899995',
        zIndex: 2,
        type: 'button',
        container: {
          x: 357,
          y: 1667,
          width: 365,
          height: 109,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ff5733',
          paddingX: 60,
          align: 'left',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '8l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696916314273',
        zIndex: 0,
        type: 'image',
        container: {
          x: 1010,
          y: 172,
          width: 556,
          height: 556,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=556&h=556',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696916341346',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/8/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696916468787',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 604,
          height: 373,
          x: 183,
          y: 217,
        },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'This is possibly a longer version, smaller and descriptive.',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696916592898',
        zIndex: 2,
        type: 'button',
        container: {
          x: 183,
          y: 640,
          width: 324,
          height: 86,
        },
        buttonProps: {
          borderRadius: 8,
          backgroundColor: '#ff5733',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 700,
          fontSize: 40,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '8s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696698772454',
        zIndex: 0,
        type: 'image',
        container: {
          x: 477,
          y: 374,
          width: 542,
          height: 542,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=542&h=542',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696698785582',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/8/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696699084537',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 426,
          height: 348,
          x: 80,
          y: 120,
        },
        texts: [
          {
            value: 'This is a HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696699271257',
        zIndex: 2,
        type: 'button',
        container: {
          x: 80,
          y: 716,
          width: 362,
          height: 100,
        },
        buttonProps: {
          borderRadius: 8,
          backgroundColor: '#ff5733',
          paddingX: 64,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 46,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '8p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696708643779',
        zIndex: 0,
        type: 'image',
        container: {
          x: 184,
          y: 720,
          width: 950,
          height: 950,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=950&h=950',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696708671146',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/8/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696708727396',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 616,
          height: 482,
          x: 234,
          y: 180,
        },
        texts: [
          {
            value: 'This is a Heading',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#fcfcfc',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696708804562',
        zIndex: 2,
        type: 'button',
        container: {
          x: 607,
          y: 1709,
          width: 416,
          height: 112,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ff5733',
          paddingX: 64,
          align: 'left',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 50,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '9l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696698772454',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/9/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696698785582',
        zIndex: 1,
        type: 'image',
        container: {
          x: 783,
          y: 83,
          width: 733,
          height: 733,
        },
        imageProps: {
          borderRadius: 350,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=733&h=733',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696699084537',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 564,
          height: 286,
          x: 83,
          y: 216,
        },
        texts: [
          {
            value: 'This is a HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696699271257',
        zIndex: 2,
        type: 'button',
        container: {
          x: 83,
          y: 600,
          width: 340,
          height: 90,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 48,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#3b9ecb',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '9s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696856471251',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/9/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696856498276',
        zIndex: 1,
        type: 'image',
        container: {
          x: 450,
          y: 265,
          width: 550,
          height: 550,
        },
        imageProps: {
          borderRadius: 264,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=550&h=550',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696856603447',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 380,
          height: 564,
          x: 80,
          y: 160,
        },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 36,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696857150170',
        zIndex: 2,
        type: 'button',
        container: {
          x: 80,
          y: 905,
          width: 356,
          height: 102,
        },
        buttonProps: {
          borderRadius: 16,
          backgroundColor: '#ffffff',
          paddingX: 56,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#3b9ecb',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '9p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696708643779',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/9/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696708671146',
        zIndex: 1,
        type: 'image',
        container: {
          x: 215,
          y: 722,
          width: 650,
          height: 650,
        },
        imageProps: {
          borderRadius: 325,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=650&h=650',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696708727396',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 809,
          height: 482,
          x: 135,
          y: 160,
        },
        texts: [
          {
            value: 'This is a Heading',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696708804562',
        zIndex: 2,
        type: 'button',
        container: {
          x: 316,
          y: 1601,
          width: 448,
          height: 120,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 64,
          align: 'center',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#3b9ecb',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '10l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696949097093',
        zIndex: 0,
        type: 'image',
        container: {
          x: 795,
          y: 75,
          width: 725,
          height: 750,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=725&h=750',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696955132831',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/10/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 520,
          height: 380,
          x: 83,
          y: 183,
        },
        texts: [
          {
            value: 'This is a Heading',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 36,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696955550566',
        zIndex: 2,
        type: 'button',
        container: {
          x: 83,
          y: 625,
          width: 436,
          height: 100,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 64,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 700,
          fontSize: 48,
          fontColor: '#1d3b84',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '10s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1697049621875',
        zIndex: 0,
        type: 'image',
        container: {
          x: 464,
          y: 232,
          width: 568,
          height: 560,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=580&h=600',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1697049648865',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/10/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1697049730821',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 564,
          height: 200,
          x: 90,
          y: 90,
        },
        texts: [
          {
            value: 'This is a Heading',
            fontWeight: 600,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1697049730821',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 340,
          height: 240,
          x: 90,
          y: 524,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1697049827871',
        zIndex: 2,
        type: 'button',
        container: {
          x: 562,
          y: 895,
          width: 380,
          height: 100,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 48,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 44,
          fontColor: '#1d3b84',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '10p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696708643779',
        zIndex: 0,
        type: 'image',
        container: {
          x: 105,
          y: 160,
          width: 870,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=870&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696708671146',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/10/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696708727396',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 920,
          height: 296,
          x: 80,
          y: 1140,
        },
        texts: [
          {
            value: 'This is a Heading',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#fcfcfc',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696708804562',
        zIndex: 2,
        type: 'button',
        container: {
          x: 328,
          y: 1516,
          width: 436,
          height: 116,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 48,
          align: 'center',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 50,
          fontColor: '#1d3b84',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '11l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696698772454',
        zIndex: 0,
        type: 'image',
        container: {
          x: 837,
          y: 0,
          width: 800,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=800&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696698785582',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/11/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696699084537',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 840,
          height: 256,
          x: 83,
          y: 238,
        },
        texts: [
          {
            value: 'This is a HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#5abddc',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696699271257',
        zIndex: 2,
        type: 'button',
        container: {
          x: 83,
          y: 597,
          width: 380,
          height: 102,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#5abddc',
          paddingX: 40,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '11s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696698772454',
        zIndex: 0,
        type: 'image',
        container: {
          x: 320,
          y: 0,
          width: 760,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=760&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696698785582',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/11/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696699084537',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 571,
          height: 470,
          x: 100,
          y: 196,
        },
        texts: [
          {
            value: 'This is a HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#5abddc',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696699271257',
        zIndex: 2,
        type: 'button',
        container: {
          x: 295,
          y: 857,
          width: 490,
          height: 124,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#5abddc',
          paddingX: 72,
          align: 'center',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '11p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696708643779',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 986,
          width: 1080,
          height: 934,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=934',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696708671146',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/11/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696708727396',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 839,
          height: 444,
          x: 121,
          y: 196,
        },
        texts: [
          {
            value: 'This is a Heading',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#5abddc',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696708804562',
        zIndex: 2,
        type: 'button',
        container: {
          x: 295,
          y: 712,
          width: 490,
          height: 124,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#5abddc',
          paddingX: 64,
          align: 'center',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 50,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '12l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696698772454',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/12/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696698785582',
        zIndex: 1,
        type: 'image',
        container: {
          x: 998,
          y: 179,
          width: 535,
          height: 540,
        },
        imageProps: {
          borderRadius: 270,
          borderWidth: 16,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=535&h=540',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696699084537',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 680,
          height: 520,
          x: 132,
          y: 132,
        },
        texts: [
          {
            value: 'This is a HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#e01d25',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696699271257',
        zIndex: 2,
        type: 'button',
        container: {
          x: 133,
          y: 709,
          width: 308,
          height: 94,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#e01d25',
          paddingX: 48,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '12s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696698772454',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/12/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696698785582',
        zIndex: 1,
        type: 'image',
        container: {
          x: 486,
          y: 465,
          width: 580,
          height: 580,
        },
        imageProps: {
          borderRadius: 290,
          borderWidth: 16,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=580&h=580',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696699084537',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 498,
          height: 420,
          x: 100,
          y: 50,
        },
        texts: [
          {
            value: 'This is a HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#e01d25',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696699271257',
        zIndex: 2,
        type: 'button',
        container: {
          x: 100,
          y: 691,
          width: 366,
          height: 112,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#e01d25',
          paddingX: 60,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '12p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696708643779',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/12/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696708671146',
        zIndex: 1,
        type: 'image',
        container: {
          x: 190,
          y: 40,
          width: 700,
          height: 700,
        },
        imageProps: {
          borderRadius: 350,
          borderWidth: 20,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=700&h=700',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696708727396',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 600,
          height: 520,
          x: 240,
          y: 1140,
        },
        texts: [
          {
            value: 'This is a Heading',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ec1c26',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, smaller and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696708804562',
        zIndex: 2,
        type: 'button',
        container: {
          x: 356,
          y: 1739,
          width: 368,
          height: 108,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#e01d25',
          paddingX: 64,
          align: 'center',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '13l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696960202356',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/13/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696960232854',
        zIndex: 1,
        type: 'image',
        container: {
          x: 901,
          y: 189,
          width: 520,
          height: 520,
        },
        imageProps: {
          borderRadius: 265,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=520&h=520',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696960309253',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 640,
          height: 312,
          x: 133,
          y: 233,
        },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#bd3009',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696960639354',
        zIndex: 2,
        type: 'button',
        container: {
          x: 133,
          y: 674,
          width: 316,
          height: 94,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#bd3009',
          paddingX: 48,
          align: 'left',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 42,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '13s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696960202356',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/13/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696960232854',
        zIndex: 1,
        type: 'image',
        container: {
          x: 520,
          y: 520,
          width: 424,
          height: 424,
        },
        imageProps: {
          borderRadius: 212,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=424&h=424',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696960309253',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 800,
          height: 332,
          x: 100,
          y: 100,
        },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#bd3009',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696960639354',
        zIndex: 2,
        type: 'button',
        container: {
          x: 100,
          y: 896,
          width: 366,
          height: 104,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#bd3009',
          paddingX: 48,
          align: 'left',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '13p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696960202356',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/13/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696960232854',
        zIndex: 1,
        type: 'image',
        container: {
          x: 226,
          y: 814,
          width: 626,
          height: 626,
        },
        imageProps: {
          borderRadius: 313,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=626&h=626',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696960309253',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 840,
          height: 500,
          x: 120,
          y: 106,
        },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 96,
            fontColor: '#bd3009',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696960639354',
        zIndex: 2,
        type: 'button',
        container: {
          x: 296,
          y: 1673,
          width: 488,
          height: 123,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#bd3009',
          paddingX: 64,
          align: 'center',
        },
        textProps: {
          value: 'Get It Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  }, */

  /*
  {
    id: '14l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1697036529114',
        zIndex: 0,
        type: 'image',
        container: {
          x: 937,
          y: 0,
          width: 663,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=663&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1697036577488',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/14/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1697036771641',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 677,
          height: 300,
          x: 120,
          y: 260,
        },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1697036937865',
        zIndex: 2,
        type: 'button',
        container: {
          x: 120,
          y: 640,
          width: 384,
          height: 100,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 48,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#603814',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '14s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1697036529114',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 540,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=540',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1697036577488',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/14/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1697036771641',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 620,
          height: 200,
          x: 230,
          y: 622,
        },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 600,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1697036937865',
        zIndex: 2,
        type: 'button',
        container: {
          x: 320,
          y: 940,
          width: 440,
          height: 112,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 48,
          align: 'center',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#603814',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '14p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1697036529114',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 834,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=535&h=540',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1697036577488',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/14/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1697036771641',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 706,
          height: 705,
          x: 187,
          y: 160,
        },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 600,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1697036937865',
        zIndex: 2,
        type: 'button',
        container: {
          x: 310,
          y: 1707,
          width: 460,
          height: 116,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 48,
          align: 'center',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#603814',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */

  /* {
    id: '15l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1697036529114',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/15/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1697036577488',
        zIndex: 1,
        type: 'image',
        container: {
          x: 932,
          y: 193,
          width: 500,
          height: 500,
        },
        imageProps: {
          borderRadius: 250,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=500&h=500',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1697036771641',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 540,
          height: 184,
          x: 166,
          y: 240,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1697040906196',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 768,
          height: 100,
          x: 100,
          y: 540,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#0f4662',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1697036937865',
        zIndex: 2,
        type: 'button',
        container: {
          x: 166,
          y: 712,
          width: 308,
          height: 90,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#46c7f4',
          paddingX: 40,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '15s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1697036529114',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/15/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1697036577488',
        zIndex: 1,
        type: 'image',
        container: {
          x: 82,
          y: 524,
          width: 460,
          height: 460,
        },
        imageProps: {
          borderRadius: 230,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=460&h=460',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1697036771641',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 648,
          height: 184,
          x: 216,
          y: 96,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1697040906196',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 710,
          height: 112,
          x: 186,
          y: 366,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#0f4662',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1697036937865',
        zIndex: 2,
        type: 'button',
        container: {
          x: 688,
          y: 920,
          width: 364,
          height: 100,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#46c7f4',
          paddingX: 44,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '15p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1697036529114',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/15/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1697036577488',
        zIndex: 1,
        type: 'image',
        container: {
          x: 148,
          y: 192,
          width: 784,
          height: 784,
        },
        imageProps: {
          borderRadius: 392,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=535&h=540',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1697036771641',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 648,
          height: 184,
          x: 216,
          y: 1120,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1697040906196',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 524,
          height: 168,
          x: 278,
          y: 1440,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#0f4662',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1697036937865',
        zIndex: 2,
        type: 'button',
        container: {
          x: 348,
          y: 1671,
          width: 384,
          height: 104,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#46c7f4',
          paddingX: 40,
          align: 'center',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '16l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696949097093',
        zIndex: 0,
        type: 'image',
        container: {
          x: 865,
          y: 75,
          width: 696,
          height: 716,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=696&h=716',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696955132831',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/16/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 680,
          height: 100,
          x: 82,
          y: 182,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 700,
            fontSize: 83,
            fontColor: '#35b1d5',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 696,
          height: 120,
          x: 82,
          y: 332,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#137592',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696955550566',
        zIndex: 2,
        type: 'button',
        container: {
          x: 82,
          y: 508,
          width: 336,
          height: 96,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#137592',
          paddingX: 45,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 700,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '16s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696949097093',
        zIndex: 0,
        type: 'image',
        container: {
          x: 488,
          y: 40,
          width: 572,
          height: 588,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=572&h=588',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696955132831',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/16/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 300,
          height: 180,
          x: 80,
          y: 120,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#35b1d5',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 432,
          height: 164,
          x: 80,
          y: 372,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#137592',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },

      {
        id: '1696955550566',
        zIndex: 2,
        type: 'button',
        container: {
          x: 80,
          y: 636,
          width: 380,
          height: 108,
        },
        buttonProps: {
          borderRadius: 11,
          backgroundColor: '#137592',
          paddingX: 45,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 700,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '16p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696949097093',
        zIndex: 0,
        type: 'image',
        container: {
          x: 122,
          y: 740,
          width: 836,
          height: 858,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=836&h=858',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696955132831',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/16/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 816,
          height: 120,
          x: 132,
          y: 175,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#35b1d5',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 716,
          height: 116,
          x: 182,
          y: 372,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#137592',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696955550566',
        zIndex: 2,
        type: 'button',
        container: {
          x: 324,
          y: 544,
          width: 432,
          height: 120,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#137592',
          paddingX: 48,
          align: 'center',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 700,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '17l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1697036529114',
        zIndex: 0,
        type: 'image',
        container: {
          x: 800,
          y: -20,
          width: 710,
          height: 710,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=710&h=710',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1697036577488',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/17/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1697036771641',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 656,
          height: 300,
          x: 100,
          y: 248,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#d85c15',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1697036937865',
        zIndex: 2,
        type: 'button',
        container: {
          x: 100,
          y: 620,
          width: 324,
          height: 90,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#d05914',
          paddingX: 45,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '17s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1697036529114',
        zIndex: 0,
        type: 'image',
        container: {
          x: 390,
          y: 390,
          width: 650,
          height: 650,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=650&h=650',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1697036577488',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/17/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1697036771641',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 800,
          height: 116,
          x: 80,
          y: 160,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#d85c15',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1697036771641',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 580,
          height: 220,
          x: 80,
          y: 312,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1697036937865',
        zIndex: 2,
        type: 'button',
        container: {
          x: 80,
          y: 837,
          width: 388,
          height: 100,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#d05914',
          paddingX: 45,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '17p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1697036529114',
        zIndex: 0,
        type: 'image',
        container: {
          x: 116,
          y: 248,
          width: 848,
          height: 848,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=848&h=848',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1697036577488',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/17/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1697036771641',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 800,
          height: 280,
          x: 140,
          y: 1320,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#d85c15',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1697036937865',
        zIndex: 2,
        type: 'button',
        container: {
          x: 340,
          y: 1690,
          width: 400,
          height: 100,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#d05914',
          paddingX: 45,
          align: 'center',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '18l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        id: '1696949097093',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/18/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696955132831',
        zIndex: 1,
        type: 'image',
        container: {
          x: 884,
          y: 0,
          width: 716,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=716&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 720,
          height: 100,
          x: 84,
          y: 200,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#f18534',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 460,
          height: 180,
          x: 84,
          y: 324,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696955550566',
        zIndex: 2,
        type: 'button',
        container: {
          x: 84,
          y: 575,
          width: 348,
          height: 96,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#f5832c',
          paddingX: 44,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 700,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '18s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        id: '1696949097093',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/18/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696955132831',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 564,
          width: 1080,
          height: 516,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=516',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 864,
          height: 284,
          x: 108,
          y: 180,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 700,
            fontSize: 96,
            fontColor: '#f18534',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696955550566',
        zIndex: 2,
        type: 'button',
        container: {
          x: 334,
          y: 850,
          width: 412,
          height: 110,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#f5832c',
          paddingX: 44,
          align: 'center',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '18p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        id: '1696949097093',
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/18/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        id: '1696955132831',
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1020,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1020',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 864,
          height: 120,
          x: 108,
          y: 1256,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 88,
            fontColor: '#f18534',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        id: '1696955454582',
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 648,
          height: 196,
          x: 216,
          y: 1412,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 40,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        id: '1696955550566',
        zIndex: 2,
        type: 'button',
        container: {
          x: 300,
          y: 1696,
          width: 480,
          height: 122,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#f5832c',
          paddingX: 48,
          align: 'center',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  }, */

  /*
  {
    id: '19l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/19/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 620,
          height: 400,
          x: 900,
          y: 80,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 56,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 900,
          y: 580,
          width: 460,
          height: 112,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#6D5648',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '19s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/19/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 532,
          height: 200,
          x: 480,
          y: 70,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 72,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 400,
          height: 200,
          x: 650,
          y: 300,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 32,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 740,
          y: 860,
          width: 300,
          height: 112,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#6D5648',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '19p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/19/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 620,
          height: 400,
          x: 420,
          y: 1120,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 420,
          y: 1560,
          width: 460,
          height: 112,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#6D5648',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */

  /* {
    id: '20l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1600,
          height: 900,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/20/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 605,
          height: 380,
          x: 100,
          y: 160,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 56,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 100,
          y: 653,
          width: 460,
          height: 112,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '20s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/20/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 532,
          height: 200,
          x: 75,
          y: 392,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 72,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          }
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 573,
          height: 124,
          x: 75,
          y: 841,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 32,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 748,
          y: 860,
          width: 258,
          height: 100,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '20p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: {
          x: 0,
          y: 0,
          width: 1080,
          height: 1920,
        },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/20/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 533,
          height: 198,
          x: 75,
          y: 692,
        },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          }
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: {
          width: 573,
          height: 124,
          x: 75,
          y: 1656,
        },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 32,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: {
          x: 748,
          y: 1675,
          width: 258,
          height: 100,
        },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#ffffff',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  }, */
];

export default templates;
