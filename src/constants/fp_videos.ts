import { type IFpVideoData } from '../modules/fp-video-service/types';

export const TEMPLATES_CONFIG: Record<
  IFpVideoData['template_id'],
  {
    thumbnailTimeInSec: number;
    fps: number;
    durationInSec: number;
  }
> = {
  p1: {
    // temp
    durationInSec: 20,
    fps: 30,
    thumbnailTimeInSec: 1,
  },
  p2: {
    // temp
    durationInSec: 20,
    fps: 30,
    thumbnailTimeInSec: 1,
  },
  p3: {
    durationInSec: 21.1,
    fps: 60,
    thumbnailTimeInSec: 1,
  },
  p4: {
    durationInSec: 18,
    fps: 30,
    thumbnailTimeInSec: 1,
  },
  p5: {
    durationInSec: 16,
    fps: 30,
    thumbnailTimeInSec: 1,
  },
  p6: {
    durationInSec: 15,
    fps: 30,
    thumbnailTimeInSec: 1,
  },
  p7: {
    durationInSec: 21,
    fps: 30,
    thumbnailTimeInSec: 1,
  },
  p8: {
    durationInSec: 19,
    fps: 30,
    thumbnailTimeInSec: 1,
  },
  p9: {
    durationInSec: 20,
    fps: 30,
    thumbnailTimeInSec: 1,
  },
  p10: {
    durationInSec: 20,
    fps: 30,
    thumbnailTimeInSec: 1,
  },
  p11: {
    durationInSec: 20,
    fps: 30,
    thumbnailTimeInSec: 1,
  },
};
