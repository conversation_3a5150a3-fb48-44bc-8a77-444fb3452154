import { GrowEasyPartners } from '../types';

export const DEV = process.env.NODE_ENV !== 'production';
export const TMP_ADIMAGES_UPLOAD_DIR = '/tmp/groweasy-be/adimages';
export const TMP_BANNERBOT_AUDIO_UPLOAD_DIR =
  '/tmp/groweasy-be/bannerbot/audios';
export const BANNERBOT_S3_PUBLIC_BUCKET_NAME = 'designeasy-public';
export const S3_BANNERBOT_VIDEO_MAKER_AUDIO_UPLOAD_DIR = 'video-maker/audios';
export const S3_BANNERBOT_VIDEO_MAKER_VIDEO_UPLOAD_DIR = 'video-maker/videos';
export const S3_GROWEASY_META_VIDEOS_UPLOAD_DIR = 'groweasy-meta/videos';
export const S3_GROWEASY_GOOGLE_VIDEOS_UPLOAD_DIR = 'groweasy-google/videos';
export const S3_UPLOADED_ASSETS_DIR = 'uploaded-assets';

export const GROWEASY_S3_PUBLIC_BUCKET_NAME = 'groweasy-public';
export const S3_FC_VIDEOS_DIR = 'fc-videos';
export const S3_FP_VIDEOS_DIR = 'fp-videos';
export const S3_FP_VIDEOS_THUMBNAIL_DIR = 'fp-videos/thumbnails';
export const S3_AI_BANNERS_DIR = 'ai-banners'; // ideogram or Chat GPT

export const TMP_FC_VIDEOS_DIR = '/tmp/groweasy-be/fc-videos';
export const TMP_FP_VIDEOS_DIR = '/tmp/groweasy-be/fp-videos';

export const FIRESTORE_COLLECTIONS = {
  CAMPAIGNS: 'campaigns',
  META_WEBHOOK_LEADS: 'metaWebhookLeads',
  BUSINESS_CATEGORIES: 'businessCategories',
  ORDERS: 'orders',
  LEADS_CRM: 'leadsCrm',
  PARTNER_LEADS_WEBHOOKS: 'partnerLeadsWebhooks',
  INVOICES: 'invoices',
  BILLING_DETAILS: 'billingDetails',
  // for invoice#
  INCREMENTAL_COUNTERS: 'incrementalCounters',
  USER_PROFILE: 'userProfile',
  CTWA_LEADS: 'ctawLeads',
  RUNNING_CAMPAIGNS: 'runningCampaigns',
  FB_LOGINS: 'fbLogins',
  AD_CREDITS: 'adCredits',
  VIDEOS_ORDERS: 'videosOrders',
  MASTER_CLASS_ORDERS: 'masterClassOrders',
  PLATFORM_DATA: 'platformData', // campaign details sub collection
  GOOGLE_FORM_LEADS: 'googleFormLeads',
  WABA_ONBOARDING: 'wabaOnboarding',
  GOOGLE_CUSTOM_CONVERSION_ACTONS: 'googleCustomConversionActions',
  TRANSACTIONS: 'transactions',
  AD_INSIGHTS: 'adInsights',
  EXCHANGE_RATE: 'exchangeRate',
  AGENTS: 'agents',
  SELF_AD_ACCOUNT_CONFIGS: 'selfAdAccountConfigs',
  AGENTEASY_DATA: 'agenteasyData',
  AGENTEASY: 'agenteasy',
  MESSAGES: 'messages',
};

export const BANNERBOT_FIRESTORE_COLLECTIONS = {
  PROJECTS: 'projects',
  USER_PROFILE: 'userProfile',
  SUBSCRIPTION_PLANS: 'subscriptionPlans',
  TEMPLATIZED_VIDEOS: 'templatizedVideos',
  USER_SUBSCRIPTIONS: 'userSubscriptions',
  AI_AD_BANNERS: 'aiAdBanners',
  NEW_VIDEO_ERRORS: 'newVideoErrors',
  COUPON_CODES: 'couponCodes',
};

export enum INCREMENTAL_COUNTER_FIELDS {
  INVOICE = 'invoice',
}

// 20% GrowEasy fee, 1.8% GrowEasy GST, 18% Meta GST
/** 
  User Budget on Groweasy: 100	
  Groweasy Fees: 20
  Groweasy GST:	3.6
  Remaining Budget:	76.4
  Meta GST: 18%
  Spending Budget: 64.75
  Effective Mark Down: 35.25
*/
export const GROW_EASY_PLATFORM_FEE_PERCENTAGE = 35.25;

/**
 * 100 customer gave
 * 25 we deducted
 * 75 we pay to Facebook
 * 75/1.18 = 63.55 k ad chalege
 * Facebook deducts (75-63.55 =11.45 as tax)
 */
export const AD_GLOBAL_AI_PLATFORM_FEE_PERCENTAGE = 36.45;

export const LEADS_FROM_META_WEBHOOK_SQS_URL =
  'https://sqs.ap-south-1.amazonaws.com/926249367436/leads-from-meta-webhook';

export const BCN_WHATSAPP_NO = '919896006742';
export const BCN_WHATSAPP_ID = '233088726558780';
export const GROWEASY_WHATSAPP_NO = '919315639185';
export const GROWEASY_WHATSAPP_ID = '361145143741889';

export const META_BASE_URL = 'https://graph.facebook.com/v22.0';
export const GOOGLE_ADS_BASE_URL =
  'https://googleads.googleapis.com/v21/customers';
export const TIKTOK_API_BASE_URL =
  'https://business-api.tiktok.com/open_api/v1.3';

export const BE_MASTER_GOOGLE_SHEET_ID =
  '1SQ7m_b-kwPEZVGr1lObNpRqCFIaJdxaJsem7tZUoFrE';
export const VIDEO_ORDERS_SHEET_ID =
  '1mFC6nc64L5q_2O9g4Dllo_8t7XvtUuHY82XLsVEqt-Q';
export const GROWEASY_META_DATASET_ID = '1344024836297864';
export const GROWEASY_BUSINESS_ID = '267359665521931'; // Autotme
export const GROWEASY_ADMIN_SYSTEM_USER_ID = '122107458284340030'; // business scoped user id
export const GROWEASY_LEADS_CAPI_GENERIC_DATASET_ID = '1665745678160158';
export const ADGLOBAL_AI_BE_MASTER_GOOGLE_SHEET_ID =
  '1Ub04tVqqwfBVVwLuC0uNQ0AtMTbuQIpuo8GhHf3FRkI';

export const partnerConfig = {
  [GrowEasyPartners.ZENDOT]: {
    RAZORPAY_KEY_ID: DEV
      ? 'rzp_test_2y0Cs8fYikC11o'
      : '***********************',
    RAZORPAY_KEY_SECRET: DEV
      ? '7kiejPko4xuebeFEblM9P3aC'
      : process.env.ZENDOT_RAZORPAY_PROD_KEY_SECRET,
  },
  [GrowEasyPartners.NIVIDA]: {
    RAZORPAY_KEY_ID: '',
    RAZORPAY_KEY_SECRET: '',
  },
};

export const PRICE_PER_VIDEO = 3000; // + 18% GST

// const BANNERBOT_GOOGLE_AD_ACCOUNT_ID = '**********';
const CONNECTFORM_GOOGLE_AD_ACCOUNT_ID = '**********';
export const GOOGLE_AD_ACCOUNT_ID = CONNECTFORM_GOOGLE_AD_ACCOUNT_ID; // BANNERBOT_GOOGLE_AD_ACCOUNT_ID;
export const TIKTOK_UAE_AD_ACCOUNT_ID = '7529856109411336208';
export const TIKTOK_ID_AD_ACCOUNT_ID = '7540940343672684560';
export const TIKTOK_AD_ACCOUNT_ID = TIKTOK_ID_AD_ACCOUNT_ID;

export const GROWEASY_CLOUDFLARE_URL = 'https://groweasy.ai/cdn-cgi/image';
export const GROWEASY_SALES_CAMPAIGNS_DATASET_ID = '***************';

export const GROWEASY_ADMINS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const AD_GLOBAL_AI_ADMINS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const GROWEASY_MAIN_AD_ACC_ID = '***************';
