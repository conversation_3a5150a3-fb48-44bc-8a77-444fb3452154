import { type IBannerTemplate } from '../types/banner_template';

const templates: IBannerTemplate[] = [
  {
    id: '21l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/21/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 70, y: 16, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 366, height: 167, x: 70, y: 118 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 435, height: 200, x: 70, y: 500 },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 144, y: 25 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 70, y: 750, width: 460, height: 112 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#000000',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '21s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/21/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 70, y: 147, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 366, height: 167, x: 70, y: 273 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 72,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 435, height: 200, x: 71, y: 700 },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 144, y: 156 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 71, y: 940, width: 258, height: 100 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#000000',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '21p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/21/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 70, y: 364, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 366, height: 167, x: 70, y: 516 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 435, height: 300, x: 70, y: 1350 },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 144, y: 373 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 70, y: 1700, width: 258, height: 100 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#000000',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  {
    id: '22l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/22/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 70, y: 40, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 629, height: 100, x: 110, y: 220 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 611, height: 124, x: 110, y: 340 },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 144, y: 49 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 110, y: 600, width: 280, height: 100 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#FF9D00',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '22s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/22/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 70, y: 70, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 400, height: 198, x: 110, y: 365 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 72,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 543, height: 124, x: 110, y: 555 },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 144, y: 79 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 110, y: 760, width: 280, height: 100 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#FF9D00',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '22p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/22/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 70, y: 70, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 629, height: 198, x: 110, y: 1000 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 543, height: 124, x: 110, y: 1200 },
        texts: [
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 144, y: 79 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 110, y: 1640, width: 280, height: 100 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#FF9D00',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  {
    id: '23l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/23/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 166, y: 92, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 723, height: 400, x: 166, y: 200 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#FF9D00',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 240, y: 101 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 166, y: 626, width: 306, height: 85 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#FF9D00',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '23s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/23/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 104, y: 177, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 733, height: 400, x: 104, y: 283 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 72,
            fontColor: '#FF9D00',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 178, y: 186 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 104, y: 731, width: 306, height: 85 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#FF9D00',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '23p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/23/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 104, y: 189, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 733, height: 400, x: 104, y: 315 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#FF9D00',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 178, y: 198 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 104, y: 743, width: 306, height: 85 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#FF9D00',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  {
    id: '24l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/24/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 121, y: 33, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 920, height: 258, x: 121, y: 145 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 195, y: 42 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 121, y: 461, width: 280, height: 100 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#252525',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '24s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/24/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 62, y: 487, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 780, height: 280, x: 62, y: 583 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 72,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 136, y: 496 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 62, y: 941, width: 312, height: 84 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#252525',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '24p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/24/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 68, y: 1173, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 795, height: 304, x: 68, y: 1279 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 142, y: 1182 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 68, y: 1631, width: 312, height: 84 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#252525',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  {
    id: '25l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/25/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 107, y: 107, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 700, height: 360, x: 107, y: 244 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 181, y: 116 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 107, y: 700, width: 280, height: 100 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#298083',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '25s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/25/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 79, y: 79, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 700, height: 349, x: 79, y: 280 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 72,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 153, y: 88 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 79, y: 677, width: 280, height: 100 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#298083',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '25p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/25/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 79, y: 79, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 700, height: 360, x: 79, y: 269 },
        texts: [
          {
            value: 'THIS IS HEADING',
            fontWeight: 800,
            fontSize: 80,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 400,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 153, y: 88 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 79, y: 677, width: 280, height: 100 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#298083',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Get it Now',
          fontWeight: 600,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  /*
  {
    id: '26l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/26/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 70, y: 413, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 629, height: 323, x: 70, y: 507 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 75,
            fontColor: '#6D5648',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            textTransform: 'uppercase',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#6D5648',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 144, y: 422 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#6D5648',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 1180, y: 762, width: 350, height: 68 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#6D5648',
          paddingX: 36,
          align: 'right',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 500,
          fontSize: 28,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
          textTransform: 'uppercase',
        },
      },
    ],
  },
  {
    id: '26s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/26/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 70, y: 638, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 600, height: 261, x: 70, y: 744 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 75,
            fontColor: '#6D5648',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            textTransform: 'uppercase',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#6D5648',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 144, y: 647 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#6D5648',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 710, y: 942, width: 300, height: 68 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#6D5648',
          paddingX: 36,
          align: 'right',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 500,
          fontSize: 28,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
          textTransform: 'uppercase',
        },
      },
    ],
  },
  {
    id: '26p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/26/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 70, y: 1382, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 600, height: 354, x: 70, y: 1496 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 75,
            fontColor: '#6D5648',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            textTransform: 'uppercase',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#6D5648',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 144, y: 1391 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#6D5648',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 710, y: 1782, width: 300, height: 68 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#6D5648',
          paddingX: 36,
          align: 'right',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 500,
          fontSize: 28,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
          textTransform: 'uppercase',
        },
      },
    ],
  },
  */

  {
    id: '27l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/27/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 769, y: 52, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 1036, height: 284, x: 282, y: 138 },
        texts: [
          {
            value: 'Interior Design',
            fontWeight: 800,
            fontSize: 90,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 625, y: 470, width: 350, height: 85 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#282828',
          paddingX: 40,
          align: 'center',
        },
        textProps: {
          value: 'Visit now',
          fontWeight: 500,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '27s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/27/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 519, y: 52, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 910, height: 371, x: 95, y: 186 },
        texts: [
          {
            value: 'Interior Design',
            fontWeight: 800,
            fontSize: 90,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 364, y: 629, width: 350, height: 85 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#282828',
          paddingX: 40,
          align: 'center',
        },
        textProps: {
          value: 'Visit now',
          fontWeight: 500,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '27p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/27/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 509, y: 252, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 910, height: 367, x: 85, y: 402 },
        texts: [
          {
            value: 'Interior Design',
            fontWeight: 800,
            fontSize: 90,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 857, width: 350, height: 85 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#282828',
          paddingX: 40,
          align: 'center',
        },
        textProps: {
          value: 'Visit now',
          fontWeight: 500,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  /*
  {
    id: '28l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/28/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 1447, y: 167, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 745, height: 270, x: 764, y: 253 },
        texts: [
          {
            value: 'This is heading',
            fontWeight: 800,
            fontSize: 84,
            fontColor: '#000000',
            textAlign: 'right',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'Lorem ipsum dolor sit amet consectetur. Enim purus sapien metus lacus dui odio viverra. Convallis magna tincidunt ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'right',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 1198, y: 599, width: 312, height: 84 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#338CBC',
          paddingX: 32,
          align: 'right',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 500,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '28s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/28/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 987, y: 523, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 745, height: 250, x: 304, y: 609 },
        texts: [
          {
            value: 'This is heading',
            fontWeight: 800,
            fontSize: 84,
            fontColor: '#000000',
            textAlign: 'right',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'Lorem ipsum dolor sit amet consectetur. Enim purus sapien metus lacus dui odio viverra. Convallis magna tincidunt ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'right',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 737, y: 903, width: 312, height: 84 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#338CBC',
          paddingX: 32,
          align: 'right',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 500,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '28p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/28/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 946, y: 1145, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 745, height: 288, x: 263, y: 1255 },
        texts: [
          {
            value: 'This is heading',
            fontWeight: 800,
            fontSize: 84,
            fontColor: '#000000',
            textAlign: 'right',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value:
              'Lorem ipsum dolor sit amet consectetur. Enim purus sapien metus lacus dui odio viverra. Convallis magna tincidunt ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'right',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 699, y: 1621, width: 312, height: 84 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#338CBC',
          paddingX: 32,
          align: 'right',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 500,
          fontSize: 48,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */

  /*
  {
    id: '29l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/29/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 86, y: 341, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 795, height: 266, x: 86, y: 427 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 84,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 160, y: 350 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 86, y: 761, width: 312, height: 84 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#002A40',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 500,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '29s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/29/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 68, y: 507, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 795, height: 260, x: 68, y: 600 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 84,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 142, y: 516 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 68, y: 937, width: 312, height: 84 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#002A40',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 500,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '29p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/29/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 68, y: 1168, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 795, height: 369, x: 68, y: 1278 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 84,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 142, y: 1177 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 68, y: 1695, width: 312, height: 84 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#002A40',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 500,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */

  /*
  {
    id: '30l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/30/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 140, y: 92, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 722, height: 270, x: 140, y: 208 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 84,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 214, y: 101 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 140, y: 588, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#ffffff',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '30s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/30/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 115, y: 135, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 722, height: 270, x: 115, y: 269 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 84,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 186, y: 144 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 115, y: 637, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#ffffff',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '30p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/30/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 115, y: 166, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 722, height: 370, x: 115, y: 276 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 84,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 189, y: 175 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 115, y: 694, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#ffffff',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */

  {
    id: '31l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/31/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 539, y: 189, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 1002, height: 227, x: 539, y: 283 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 84,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 613, y: 198 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 539, y: 575, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#042A2B',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '31s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/31/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 550, y: 249, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 760, height: 268, x: 201, y: 346 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 84,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 410, y: 690, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#042A2B',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '31p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/31/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 923, y: 384, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 760, height: 268, x: 225, y: 478 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 84,
            fontColor: '#ffffff',
            textAlign: 'right',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'right',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 644, y: 815, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#042A2B',
          paddingX: 36,
          align: 'right',
        },
        textProps: {
          value: 'Call To Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  {
    id: '32l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/32/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 89, y: 62, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 722, height: 284, x: 89, y: 196 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 75,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 163, y: 71 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 89, y: 612, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '32s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/32/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 89, y: 89, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 800, height: 284, x: 89, y: 231 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 75,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 163, y: 98 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 89, y: 613, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '32p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/32/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 84, y: 204, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 800, height: 284, x: 89, y: 399 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 700,
            fontSize: 75,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 158, y: 213 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 89, y: 775, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#000000',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  {
    id: '33l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/33/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 147, y: 80, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 970, height: 254, x: 147, y: 200 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 75,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 221, y: 89 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 147, y: 533, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#000000',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '33s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/33/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 77, y: 53, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 975, height: 254, x: 77, y: 200 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 75,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 151, y: 62 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 77, y: 580, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#000000',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '33p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/33/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 77, y: 56, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 975, height: 254, x: 77, y: 200 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 75,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 151, y: 65 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 77, y: 560, width: 350, height: 89 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#000000',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  {
    id: '34l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1600,
    height: 900,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1600&h=900',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1600, height: 900 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/34/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 1508, y: 33, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 804, height: 272, x: 766, y: 539 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 75,
            fontColor: '#ffffff',
            textAlign: 'right',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'right',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 1220, y: 406, width: 350, height: 73 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#000000',
          paddingX: 32,
          align: 'right',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '34s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/34/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 988, y: 50, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 728, height: 272, x: 322, y: 718 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 75,
            fontColor: '#ffffff',
            textAlign: 'right',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'right',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 700, y: 585, width: 350, height: 73 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#000000',
          paddingX: 32,
          align: 'right',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '34p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/34/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 988, y: 1250, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 804, height: 272, x: 246, y: 1548 },
        texts: [
          {
            value: 'This is Heading',
            fontWeight: 800,
            fontSize: 75,
            fontColor: '#ffffff',
            textAlign: 'right',
            variableName: 'CREATIVE_TITLE_TEXT',
          },
          {
            value: 'This is possibly a longer version, small and descriptive',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'right',
            variableName: 'CALL_OUT_TEXT',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 663, y: 1415, width: 387, height: 73 },
        buttonProps: {
          borderRadius: 10,
          backgroundColor: '#000000',
          paddingX: 32,
          align: 'right',
        },
        textProps: {
          value: 'Call to Action',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  /*
  {
    id: '35l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1200,
    height: 628,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 16, y: 18, width: 1166, height: 470 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1200&h=628',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/35/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 646, height: 125, x: 31, y: 450 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#0079A4',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#005F3A',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: true,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 987, y: 534, width: 182, height: 61 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#005F3A',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '35s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 25, y: 25, width: 1030, height: 800 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/35/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 862, height: 140, x: 70, y: 771 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#0079A4',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter!',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#005F3A',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: true,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 70, y: 975, width: 249, height: 84 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#005F3A',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '35p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 25, y: 25, width: 1030, height: 1270 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/35/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 862, height: 322, x: 70, y: 1316 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 62,
            fontColor: '#0079A4',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#005F3A',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: true,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 70, y: 1686, width: 249, height: 84 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#005F3A',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */

  /* {
    id: '36l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1200,
    height: 628,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 548, y: 31, width: 620, height: 566 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1200&h=628',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/36/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 411, height: 215, x: 31, y: 180 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 32,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 0, y: 478, width: 160, height: 52 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079A4',
          paddingX: 32,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '36s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 70, y: 70, width: 940, height: 694 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/36/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 857, height: 169, x: 112, y: 779 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 996, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079A4',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '36p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 100, y: 100, width: 880, height: 1279 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/36/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 738, height: 280, x: 171, y: 1508 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 1836, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079A4',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },

  {
    id: '37l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1200,
    height: 628,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 549, y: 36, width: 618, height: 503 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1200&h=628',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/37/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 450, height: 259, x: 52, y: 196 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 48,
            fontColor: '#0079A4',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 32,
            fontColor: '#005F3A',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: -46, y: 503, width: 450, height: 60 },
        buttonProps: {
          borderRadius: 34,
          backgroundColor: '#005F3A',
          paddingX: 96,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '37s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 89, y: 274, width: 926, height: 696 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/37/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 911, height: 162, x: 85, y: 38 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 240, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 48,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#0079A4',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '37p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 89, y: 885, width: 926, height: 696 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/37/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 680, height: 381, x: 200, y: 200 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 693, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 48,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#0079A4',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  }, */

  /*
  {
    id: '38l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1200,
    height: 628,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1200&h=628',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/38/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 353, height: 154, x: 776, y: 318 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 32,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 792, y: 546, width: 333, height: 31 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079a4',
          paddingX: 0,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 26,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '38s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/38/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 472, height: 228, x: 512, y: 664 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 32,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 532, y: 968, width: 447, height: 44 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079a4',
          paddingX: 0,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '38p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/38/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 616, height: 310, x: 343, y: 1378 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 373, y: 1789, width: 596, height: 44 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079a4',
          paddingX: 0,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */

  /*
  {
    id: '39l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1200,
    height: 628,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1200&h=628',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/39/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 501, height: 223, x: 44, y: 262 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 32,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 44, y: 521, width: 300, height: 60 },
        buttonProps: {
          borderRadius: 32,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 26,
          fontColor: '#0079A4',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '39s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/39/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 455, height: 351, x: 70, y: 465 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 72,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 70, y: 896, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 48,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#0079A4',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '39p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/39/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 759, height: 286, x: 161, y: 438 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 72,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 824, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 48,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#0079A4',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */
];

export default templates;
