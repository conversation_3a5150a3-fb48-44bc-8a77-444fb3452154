import { type IBannerTemplate } from '../types/banner_template';

const templates: IBannerTemplate[] = [
  /*
  {
    id: '40l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1200,
    height: 628,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1200&h=628',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/40/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 127, y: 10, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 501, height: 223, x: 44, y: 262 },
        texts: [
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 32,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 68, y: 84 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 44, y: 521, width: 300, height: 60 },
        buttonProps: {
          borderRadius: 32,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 26,
          fontColor: '#0079A4',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '40s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/40/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 154, y: 10, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 455, height: 351, x: 70, y: 465 },
        texts: [
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 72,
            fontColor: '#ffffff',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 94, y: 84 },
        texts: [
          {
            value: 'GrowEasy supser super long GrowEasy supser super long',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 70, y: 896, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 48,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#0079A4',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '40p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/40/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 509, y: 10, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 759, height: 286, x: 161, y: 438 },
        texts: [
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 72,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 450, y: 84 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 824, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 48,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#0079A4',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */
  /*
  {
    id: '41l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1200,
    height: 628,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1200&h=628',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/41/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 122, y: 71, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 353, height: 154, x: 776, y: 318 },
        texts: [
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 32,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 160, height: 44, x: 73, y: 145 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 792, y: 546, width: 333, height: 31 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079a4',
          paddingX: 0,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 26,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '41s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/41/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 132, y: 81, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 472, height: 228, x: 512, y: 664 },
        texts: [
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 32,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 160, height: 44, x: 83, y: 155 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 532, y: 968, width: 447, height: 44 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079a4',
          paddingX: 0,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '41p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/41/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 132, y: 81, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 616, height: 310, x: 343, y: 1378 },
        texts: [
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 160, height: 44, x: 83, y: 155 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 373, y: 1789, width: 596, height: 44 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079a4',
          paddingX: 0,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */
  /* {
    id: '42l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1200,
    height: 628,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 548, y: 31, width: 620, height: 566 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=620&h=566',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/42/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 31, y: 31, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 411, height: 215, x: 31, y: 180 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 32,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 105, y: 40 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'left',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 0, y: 478, width: 160, height: 52 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079A4',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '42s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 70, y: 70, width: 940, height: 694 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/42/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 509, y: 10, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 857, height: 169, x: 112, y: 779 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 450, y: 84 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 996, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079A4',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '42p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 100, y: 100, width: 880, height: 1279 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/42/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 509, y: 10, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 738, height: 280, x: 171, y: 1508 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 174, height: 44, x: 450, y: 84 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 1836, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 0,
          backgroundColor: '#0079A4',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  }, */
  /*
  {
    id: '43l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1200,
    height: 628,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 16, y: 18, width: 1166, height: 470 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1200&h=628',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/43/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 114, y: 10, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 646, height: 125, x: 31, y: 450 },
        texts: [
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#005F3A',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: true,
          },
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#0079A4',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 55, y: 84 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 987, y: 534, width: 182, height: 61 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#005F3A',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '43s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 25, y: 25, width: 1030, height: 800 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/43/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 509, y: 10, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 862, height: 140, x: 70, y: 771 },
        texts: [
          {
            value: 'Make Every Celebration Sweeter!',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#005F3A',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: true,
          },
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#0079A4',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 450, y: 84 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 70, y: 975, width: 249, height: 84 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#005F3A',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '43p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 25, y: 25, width: 1030, height: 1270 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/43/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 509, y: 10, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 862, height: 322, x: 70, y: 1316 },
        texts: [
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 42,
            fontColor: '#005F3A',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: true,
          },
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 62,
            fontColor: '#0079A4',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 450, y: 84 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 70, y: 1686, width: 249, height: 84 },
        buttonProps: {
          borderRadius: 12,
          backgroundColor: '#005F3A',
          paddingX: 36,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  */
  /* {
    id: '44l',
    description: 'Landscape banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'landscape',
    tags: [],
    width: 1200,
    height: 628,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 549, y: 36, width: 618, height: 503 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1200&h=628',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1200, height: 628 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/44/landscape.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 841, y: 441, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 450, height: 259, x: 52, y: 196 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 48,
            fontColor: '#0079A4',
            textAlign: 'left',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 32,
            fontColor: '#005F3A',
            textAlign: 'left',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 130, height: 44, x: 807, y: 513 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: -46, y: 503, width: 450, height: 60 },
        buttonProps: {
          borderRadius: 34,
          backgroundColor: '#005F3A',
          paddingX: 96,
          align: 'left',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#ffffff',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '44s',
    description: 'Square banner',
    minVersionCode: 46,
    category: 'banner',
    size: 'square',
    tags: [],
    width: 1080,
    height: 1080,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 89, y: 274, width: 926, height: 696 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1080',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1080 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/44/square.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 508, y: 846, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 911, height: 162, x: 85, y: 38 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 450, y: 919 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 240, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 48,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#0079A4',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  },
  {
    id: '44p',
    description: 'Portrait',
    minVersionCode: 46,
    category: 'banner',
    size: 'portrait',
    tags: [],
    width: 1080,
    height: 1920,
    withLogoOnly: true,
    elements: [
      {
        zIndex: 0,
        type: 'image',
        container: { x: 89, y: 885, width: 926, height: 696 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://images.unsplash.com/photo-1511920170033-f8396924c348?auto=format&fit=crop&w=1080&h=1920',
          variableName: 'CREATIVE_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 0, y: 0, width: 1080, height: 1920 },
        imageProps: {
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/templates/44/portrait.png',
          variableName: 'BACKGROUND_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'image',
        container: { x: 510, y: 1466, width: 62, height: 62 },
        imageProps: {
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#ffffff',
          url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/groweasy-logo-square.png',
          variableName: 'LOGO_IMAGE',
        },
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 680, height: 381, x: 200, y: 200 },
        texts: [
          {
            value: 'Freshly baked, beautifully crafted',
            fontWeight: 700,
            fontSize: 52,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CREATIVE_TITLE_TEXT',
            italic: false,
          },
          {
            value: 'Make Every Celebration Sweeter! ',
            fontWeight: 500,
            fontSize: 48,
            fontColor: '#ffffff',
            textAlign: 'center',
            variableName: 'CALL_OUT_TEXT',
            italic: false,
          },
        ],
      },
      {
        zIndex: 1,
        type: 'textArr',
        container: { width: 180, height: 44, x: 451, y: 1539 },
        texts: [
          {
            value: 'GrowEasy',
            fontWeight: 500,
            fontSize: 24,
            fontColor: '#000000',
            textAlign: 'center',
            variableName: 'BUSINESS_NAME_TEXT',
            italic: false,
            verticalAlign: 'center',
          },
        ],
      },
      {
        zIndex: 2,
        type: 'button',
        container: { x: 365, y: 693, width: 350, height: 84 },
        buttonProps: {
          borderRadius: 48,
          backgroundColor: '#ffffff',
          paddingX: 36,
          align: 'center',
        },
        textProps: {
          value: 'Shop Now',
          fontWeight: 600,
          fontSize: 36,
          fontColor: '#0079A4',
          variableName: 'CTA_TEXT',
        },
      },
    ],
  }, */
];

export default templates;
