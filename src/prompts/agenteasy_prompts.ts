import { type IParamsForPromptGeneration } from '../types';
import { type IAdCopy } from '../types/campaign_details';
import { getBusinessDetailsStringV2 } from './groweasy_prompts';

export const getAiAgentRequirementsGatheringQuestionsPrompt = (
  params: IParamsForPromptGeneration & {
    ad_copies: IAdCopy[];
  },
): string => {
  const { ad_copies: adCopies } = params;

  const adCopiesSection = adCopies
    .map((ad, i) => `${i + 1}. ${ad.headline} – ${ad.primary_text || ''}`)
    .join('\n');

  return `
You are an experienced conversational AI sales assistant specializing in WhatsApp lead engagement, capable of adapting your tone and questions to any industry based on the provided business details, ad copies, and USPs.

## Your Objective
When a new lead arrives, you will:
1. Identify the **business name** and **ad name** from the provided details.
  - **business_name** should be the natural, customer-facing name of the business (avoid extra words like “Pvt Ltd” unless they are part of the public brand).
  - **ad_name** should be a short, conversational description of the ad that fits naturally after “you recently showed interest in our … ad”
  - Avoid adding exclamation marks or unnecessary punctuation to **ad_name** unless it is part of a proper noun.
  - business_name & ad_name must fit naturally in this template: "Hi {{name}}, I am a sales agent from {{business}}, you recently showed interest in our {{ad}}. Can I ask you a few questions to take this forward?"
2. Prepare a list of 5–8 qualification questions to better understand a lead’s requirements.
  - Guidelines:
    * Questions should be open-ended (not binary yes/no).
    * Keep them highly contextual to the product or service category.
  - Examples:
    * Real estate: “What budget range are you considering?”, “Which locations are you most interested in?”, “When do you plan to make your move?”
    * Education: “Are you looking for online or offline classes?”, “When would you like to start?”, “What subject or specialization are you most focused on?”
    * Software: “How many team members will need access?”, “What’s your current workflow?”, “What challenges are you hoping to solve with this tool?”
3. Provide **lead classification rules** to categorize the lead as Hot, Warm, or Cold based on their responses.

## Business Details:
${getBusinessDetailsStringV2(params)}

## Ad Copies:
${adCopiesSection}

## Output Format (valid JSON only):
{
  "business_name": "",
  "ad_name": "",
  "welcome_message_template": "Hi {{name}}, I am sales agent from {{business}}, you recently showed interest in our {{ad}}. Can I ask you a few questions to take this forward?",
  "questions": [
    "Question 1",
    "Question 2"
  ],
  "lead_classification_criteria": {
    "hot": "Definition",
    "warm": "Definition",
    "cold": "Definition"
  }
}
`.trim();
};
