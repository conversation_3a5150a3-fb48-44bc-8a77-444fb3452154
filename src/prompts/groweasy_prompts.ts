import {
  type IParamsForPromptGeneration,
  type IParamsForAdCreative,
  AdPlatforms,
} from '../types';
import {
  type IGoogleLocationDetails,
  type IBusinessDetails,
  AdLanguage,
  GROWEASY_CAMPAIGN_TYPE,
  type ITiktokLocationDetails,
} from '../types/campaign_details';
import { type IImageData } from '../types/stock_image';
import { getAbsoluteUrl } from '../utils';

const getGenderString = (params: IParamsForAdCreative): string => {
  if (params.targeting.genders && params.targeting.genders.length === 1) {
    return params.targeting.genders.includes(1) ? 'Male' : 'Female';
  }
  return 'All';
};

const getBusinessDetailsString = (params: IParamsForAdCreative): string => {
  return `
  - Business Category: ${params.business_details.business_category}
  - Product / Service Description: ${
    params.business_details.product_or_service_description
  }
  - Product / Service Offers: ${
    params.business_details.product_or_service_offers_or_usp ?? ''
  }
  - Targeting Age: [${params.targeting?.age_min}, ${params.targeting?.age_max}]
  - Targeting Gender: ${getGenderString(params)}  
  - Consumer Type: ${params.business_details?.consumer_type}
  - Ideal customers: ${params.business_details?.ideal_customers}
`;
};

export const getBusinessDetailsStringV2 = (
  params: IParamsForPromptGeneration,
): string => {
  return `
  - Business Category: ${params.business_details.business_category}
  - Product / Service Description: ${
    params.business_details.product_or_service_description
  }
  - Product / Service USPs: ${params.ai_assisted_product_usps
    .map((usp, i) => `${i + 1}. ${usp}`)
    .join('\n')}
  - Consumer Type: ${params.business_details?.consumer_type}
  - Ideal customers: ${params.business_details?.ideal_customers}
  - Website: ${params.business_details?.website ?? 'NA'}
  - Contact: ${params.business_details?.mobile ?? 'NA'}
`;
};

const getLocationDetailsString = (
  params: IParamsForAdCreative & {
    google_geo_locations?: IGoogleLocationDetails[];
    tiktok_geo_locations?: ITiktokLocationDetails[];
  },
): string => {
  if (params.google_geo_locations?.length) {
    return `
  ${params.google_geo_locations
    .map(
      (item, index) =>
        `${index + 1}. ${item.geoTargetConstant?.name} (${item.geoTargetConstant
          ?.canonicalName})`,
    )
    .join('\n')}
  `;
  }
  if (params.tiktok_geo_locations?.length) {
    return `
    ${params.tiktok_geo_locations
      .map((item, index) => `${index + 1}. ${item.name}`)
      .join('\n')}
    `;
  }
  return `
  1. Countries: ${(params.targeting?.geo_locations?.countries ?? [])
    ?.map((item) => item)
    .join('; ')}
  2. Regions: ${(params.targeting?.geo_locations?.regions ?? [])
    ?.map((item) => `${item.name} (in ${item.country})`)
    .join('; ')}
  3. Cities: ${(params.targeting?.geo_locations?.cities ?? [])
    ?.map((item) => `${item.name} (in ${item.region}, ${item.country})`)
    .join('; ')}
  4. Neighborhoods: ${(params.targeting?.geo_locations?.neighborhoods ?? [])
    ?.map((item) => `${item.name} (in ${item.region}, ${item.country})`)
    .join('; ')}
  5. Places: ${(params.targeting?.geo_locations?.places ?? [])
    ?.map((item) => `${item.name} (in ${item.country})`)
    .join('; ')}
  `;
};

export const getAdBannersPrompt = (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
  },
): string => {
  const adLanguage = params.ad_language ?? AdLanguage.ENGLISH;

  const uspSection = params.ai_assisted_product_usps?.length
    ? params.ai_assisted_product_usps
        .map((usp, i) => `${i + 1}. ${usp}`)
        .join('\n')
    : '';

  return `
You are a performance marketing and lead generation focused creative designer and copy writer with 15+ years of experience in  ${
    params.business_details.business_category
  } industry.

You need to create 10 ads for below mentioned product, to run campaigns on Meta ads. Each banner should consist of Title , Call Out  CTA, Image keywords.

Each banner should be returned in the following **valid JSON** structure:

\`\`\`json
{
  "banners": [
    {
      "creative_title": "",
      "call_out": "",
      "call_to_action": "",
      "creative_image_keywords": [],
    }
  ]
}
\`\`\`

### Banner Element Guidelines:
- Each ad talks about a unique USP, i.e. focused_usp and all other banner elements should revolve around it.
- Title talks about benefit or problem or product or target Buyer. It should instantly tells buyers what we’re offering. Avoid creativity and keep it straightforward focusing on clarity and intent. It should be in ${adLanguage} Examples: "Freshly Made Biryani – Get 30% Off Today!," "Generate Leads via Instagram Ads – Free Guide", "Affordable Luxury Homes Starting at ₹2 Cr!
- Call out is reason to buy or believe. It should provide concise product/service details. It should be in ${adLanguage}
- CTA  is the action or persuasion. It should be in ${adLanguage} Examples: "Order Now," "Click Here," "Hurry", "Schedule a Visit", "Book a Demo"
- Try to create different types of titles across benefit type, problem type, product type and target buyer type
- Return only the JSON structure. No explanation or commentary.

### Focused USPs:
${uspSection}

### Product Details:
${getBusinessDetailsString(params)}

### Important Instruction for creative_image_keywords:

- The **creative_image_keywords** are used to fetch relevant images from stock libraries.
- Keywords should be chosen to maximize the chances of finding high-quality stock images.
- Avoid technical or UI-related keywords that are unlikely to have stock images.
- Use **generic yet relevant** keywords that represent real-world concepts and visuals.
- It should be in English.
- example: "young woman smiling", "organic skincare product", "modern workspace", etc.

`;
};

export const getAdCopiesPrompt = (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
  },
): string => {
  const resolvedLanguage = params.ad_language ?? AdLanguage.ENGLISH;

  const uspSection = params.ai_assisted_product_usps?.length
    ? params.ai_assisted_product_usps
        .map((usp, i) => `${i + 1}. ${usp}`)
        .join('\n')
    : '';

  return `
You are an intelligent AI assistant dedicated to generating ad copies.

A ad copy has following elements:

1. primary_text - The main content of the ad that provides more details about the product or service, highlights its features and benefits, and addresses the audience's needs or pain points. It should be of 2 lines. First line should not exceed 125 characters.
2. headline - The attention-grabbing title or phrase at the top of the ad, designed to immediately capture the viewer's interest. It should not exceed 40 characters.
3. description - It provides additional information or context. It complements the primary_text and headline, offering more details about the advertised content. It should not exceed 25 characters. 
4. call_to_action_type - It can be one of these values: "APPLY_NOW", "DOWNLOAD", "GET_QUOTE", "LEARN_MORE", "SIGN_UP", "SUBSCRIBE", "SHOP_NOW"

The combination of a strong headline, informative primary text, and relevant description contributes to the overall effectiveness of the ad.

Now consider the following product (or service) details provided by the user:

${getBusinessDetailsString(params)}

### Provided Product USPs:
${uspSection}

Based on the above product details and your training, please return an array of 10 copies, in the following JSON format:

{
  copies: [
    {
      "primary_text": "",
      "headline": "",
      "description": "",
      "call_to_action_type": ""
    },
    {
      "primary_text": "",
      "headline": "",
      "description": "",
      "call_to_action_type": ""
    },
    {
      "primary_text": "",
      "headline": "",
      "description": "",
      "call_to_action_type": ""
    }
  ]
}

primary_text should be in ${resolvedLanguage} language, and all remaining elements should be in English.
Please provide a valid JSON response without any special characters. Remember, your response should be JSON-parsable.
`;
};

export const getAdCopiesPromptV2 = (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
    platform?: AdPlatforms;
  },
): string => {
  const resolvedLanguage = params.ad_language ?? AdLanguage.ENGLISH;

  const uspSection = params.ai_assisted_product_usps?.length
    ? params.ai_assisted_product_usps
        .map((usp, i) => `${i + 1}. ${usp}`)
        .join('\n')
    : '';
  const copiesForTiktok = params.platform === AdPlatforms.TIKTOK;

  return `
  You are a Meta ads performance copywriter focused on lead generation campaigns. Prioritize direct-response principles over clarity and targeting over creativity or emotion. Your job is to write conversion-focused ad copies that act as targeting filters and lead magnets.

  ## Ad Copy Structure
  Each ad copy has the following elements:
  1. **primary_text** - The main content. Provides details about the product/service, highlights features and benefits, addresses audience pain points. ${
    copiesForTiktok ? 'It **should not** exceed 100 characters.' : ''
  }
  2. **headline** - Attention-grabbing title (max 40 characters) designed to immediately capture viewer interest.
  3. **description** - Additional context/information (max 25 characters) that complements the primary_text and headline.
  4. **call_to_action_type** - Must be one of: "APPLY_NOW", "DOWNLOAD", "GET_QUOTE", "LEARN_MORE", "SIGN_UP", "SUBSCRIBE"

  ## Required Copy Formats
  Generate exactly 5 ad copy variants, each following one of these structures:

  1. **Direct short-form** (2–3 lines) - Straight to the point, clear value proposition
  2. **Problem-solution format** - Identify pain point, present solution
  3. **Listicle format** - Benefits or features in list-style format
  4. **Testimonial-based format** - Social proof or customer success focus
  5. **Urgency + offer push format** - Time-sensitive language with clear offer

  ## Writing Guidelines
  - Use simple language (Grade 6–8 reading level)
  - Assume audience is seeing the ad for the first time
  - No metaphors, stories, or jokes
  - Boldly repeat the offer or CTA in all variants
  - Each copy should be ≤ 4 lines for mobile friendliness
  - Do not use "we" - make it about the user (you/your business)
  - Write the primary text in ${resolvedLanguage}, keeping all other elements in English. **Do not translate widely recognized English terms** (e.g., 'Sales team' should remain 'Sales team' in Hindi, not 'बिक्री टीम').
  - Maintain character limits strictly

  ## Output Format
  Return a valid JSON response in this exact format:

  \`\`\`json
  {
    "copies": [
      {
        "format_type": "Direct short-form",
        "primary_text": "",
        "headline": "",
        "description": "",
        "call_to_action_type": ""
      },
      {
        "format_type": "Problem-solution format",
        "primary_text": "",
        "headline": "",
        "description": "",
        "call_to_action_type": ""
      },
      {
        "format_type": "Listicle format",
        "primary_text": "",
        "headline": "",
        "description": "",
        "call_to_action_type": ""
      },
      {
        "format_type": "Testimonial-based format",
        "primary_text": "",
        "headline": "",
        "description": "",
        "call_to_action_type": ""
      },
      {
        "format_type": "Urgency + offer push format",
        "primary_text": "",
        "headline": "",
        "description": "",
        "call_to_action_type": ""
      }
    ]
  }
  \`\`\`

  Ensure the response is JSON-parsable without any special characters or formatting issues.

  Now consider the following product (or service) details provided by the user:

  ${getBusinessDetailsString(params)}

  ### Provided Product USPs:
  ${uspSection}
  
`;
};

// though id will suffice, OpenAI is returning name as well
// This is on purpose, It helps in debugging since model can hallucinate
export const getFlexibleSpecTargetingPrompts = (
  params: IParamsForAdCreative,
  targetingData: string,
): string => {
  return `
You are an intelligent AI assistant specialized in optimizing Facebook and Instagram Ad targeting.

TASK:
Classify each audience segment from the provided list based on how likely they are to convert for the given product or service. Do NOT remove or filter out any targeting option — your job is only to evaluate and label them.

INSTRUCTIONS:
- Evaluate each audience from the targeting data based on relevance to the product.
- Assign one of the following values to "conversion_probability": "low", "medium", or "high".
- Do NOT add or invent any new entries. Only use what's in the original targeting data.
- Keep your response clean and valid JSON.

TARGETING DATA:
${targetingData}

PRODUCT DETAILS:
${getBusinessDetailsString(params)}

OUTPUT FORMAT:
Return ALL entries from the targeting data, with each one structured like this:

{
  "targeting": [
    {
      "id": "123456",
      "name": "Interior Design",
      "conversion_probability": "high"
    },
    ...
  ]
}
`;
};

export const getLeadgenFormPrompt = (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
  },
): string => {
  const { ai_assisted_product_usps: aiAssistedProductUsps } = params;

  const businessDetailsString = getBusinessDetailsString(params);

  const uspSection = aiAssistedProductUsps?.length
    ? aiAssistedProductUsps.map((usp, i) => `${i + 1}. ${usp}`).join('\n')
    : '';

  const resolvedLanguage = params.ad_language ?? AdLanguage.ENGLISH;

  return `
  You are an top 1% performance marketing expert for ${
    params.business_details.business_category
  } industry , specialised in lead generation campaigns on Meta Ads (Facebook Ads)

  We need to promote ${businessDetailsString} ${
    uspSection ? `with ${uspSection}` : uspSection
  }

  Create 2 Lead Qualifying Questions to make sure we get right high intent leads and irrelevant leads don't fill in the meta lead form

  Generate two questions in Free Text

  Free text format: Input question having format: {"type":"CUSTOM", "key": "question4", "label": ""}.

  Output format:

  \`{
    "questions": [
      {
        "type": "CUSTOM",
        "key": "",
        "label": ""
      }
    ]
  }\`

  label should be in ${resolvedLanguage} language. 
  Please provide a valid JSON response without any special characters. Remember, your response should be JSON-parsable.`;
};

const getCategoryDescription = (category: string): string => {
  switch (category) {
    case 'CREDIT':
      return `Credit cards, Brand ads for credit cards (regardless of offer), Auto loans, Personal/business loans, Mortgage loans, Long-term financing, Debt recovery`;
    case 'EMPLOYMENT':
      return `Full-time/part-time jobs, Freelance, Internships, Franchising opportunities, Apprenticeships, Professional certification programs, Job fairs, Job board/aggregation services, Guaranteed interviews or career placement, Promotion of company perks alongside employment opportunities`;
    case 'HOUSING':
      return `Sale/rental/temporary housing listings, Homeowners insurance offers (including insurance bundles), Renters insurance offers (including insurance bundles), Mortgage insurance offers (including insurance bundles), Financing options offers (including mortgage loans), Home equity or appraisal services, House hunting services, Aggregator services`;
    case 'ISSUES_ELECTIONS_POLITICS':
      return `Made by/about a candidate, political figure, party, PAC, or advocates for election outcome, about election, referendum, ballot initiative, regulated as political advertising, social issues`;
    default:
      return '';
  }
};

const getCategoryRules = (): string => `
1. CREDIT: ${getCategoryDescription('CREDIT')}
2. EMPLOYMENT: ${getCategoryDescription('EMPLOYMENT')}
3. HOUSING: ${getCategoryDescription('HOUSING')}
4. ISSUES_ELECTIONS_POLITICS: ${getCategoryDescription(
  'ISSUES_ELECTIONS_POLITICS',
)}
`;

export const getSpecialAdCategoriesPrompt = (params: {
  business_details: IBusinessDetails;
}): string => {
  return `
  You are an intelligent AI assistant dedicated to categorizing advertisement campaigns into special categories. Your goal is to avoid categorizing ads into these special categories unless necessary. Focus on the exact definitions, not broader categories.

An ad campaign can be categorized into 4 special categories:

${getCategoryRules()}

Now consider the following product (or service) details provided by the user:

- Product / Service Description: ${
    params.business_details.product_or_service_description
  }
- Product / Service Offers or USP: ${
    params.business_details.product_or_service_offers_or_usp ?? ''
  }

Based on the above product details and your training, please categorize the ad and return the result in the following JSON format:

{
  "category": "",
  "reason_for_categorization": ""
}

Leave the category as empty if it cannot be categorized into above 4 categories.

Please provide a valid JSON response without any special characters. Remember, your response should be JSON-parsable.
`;
};

export const getPromptForFilteringImages = (params: {
  imageKeywords: string[];
  imagesData: IImageData[];
  productOrServiceDescription?: string;
}): string => {
  const { imageKeywords, imagesData, productOrServiceDescription } = params;

  return `
You are an intelligent image filtering engine. 

Your task is to **select and rank at least 10 of the most relevant images**, prioritizing the **product/service description**, followed by keyword matching.

### **Input Data:**
- **Product/Service Description (Primary Factor)**: ${
    productOrServiceDescription ?? 'N/A'
  }
- **Image Keywords (Supporting Factor)**: ${imageKeywords.join(', ')}
- **Images Data**: ${JSON.stringify(
    imagesData.map((item) => ({
      id: item.id,
      alt_description: item.alt_description,
    })),
  )}

### **Relevance Criteria (Ranked by Priority):**
1. **Context Alignment (Highest Weight)**: How well does the image relate to the **product/service description**?
2. **Semantic Similarity**: Does the alt description meaningfully align with the product/service?
3. **Keyword Presence (Supporting Factor)**: Do the **image keywords** appear in the description?
4. **Keyword Frequency (Tie-Breaker)**: If multiple images match, prefer those where keywords appear more frequently.

❗ **Rules:**
- **Select at least 10 images** (if available), ranked by strongest relevance.
- **If fewer than 10 relevant images exist, return only those available** (no random selections).
- **Images must be sorted from most relevant to least relevant.**

### **Output Format:**
Please return a JSON object in the following structure:
{
  "images": [
    {
      "id": "image_id_1",
      "alt_description": "description_1",
      "selection_reason": ""
    },
    {
      "id": "image_id_2",
      "alt_description": "description_2",
      "selection_reason": ""
    }
    // At least 10 images in total (if available)
  ]
}

Generate the JSON object strictly following this format.
`;
};

export const getVideoMakerPrompt = (query: string): string => {
  return `
You are a video ad director, You will be supplied a prompt from the business to create a video ad. 
You need to create a video script of 30 seconds.
Divide the script into multiple parts, and for each part suggest a video frame.
Given the prompt "${query}", generate a JSON object with below schema-

{
  "total_duration": 30000, // Total video duration in milliseconds
  "video_caption": "", // video caption/dsecription in less than 100 chars
  "frames": [
    {
      "image_keywords": [], // Keywords related to the image, always in english
      "video_content_description_involving_people": '', // description for ideal video suiting this frame, always in english
      "script_text": "Content script text for the frame" // Video script, this will be played in video
      "duration": 4000 // duration of frame
    }
  ]
}

Instructions-

1. The goal of the video is to promote the business, so the scripts should be professional. 
2. The language of the script should match the language of the prompt.
3. Script text in one frame should be around 100 characters.
4. Generated Ad video is for selling products/services so it should follow journey e.g. Introduction -> Problem Statement -> Solution Introduction -> Benefits -> Call to Action
5. image_keywords should always be in english irrespective the language of prompt
6. video_content_description_involving_people will be used to fetch relevant video from stock videos. It should be generic and yet relevant to frame script
7. Example of video_content_description_involving_people: Group of men discussing in meeting room, young couple opening the door to their new home, Friends chatting in a well-lit living space etc

Please generate the JSON object based on the above structure.
`;
};

export const getPromptForFilteringVideos = (
  videoDescription: string,
  videosList: Array<{
    id: number;
    url: string;
  }>,
): string => {
  return `
You are an intelligent video filter engine. 

Given the following video content description: 
${videoDescription} 

and video list: 
${JSON.stringify(videosList)}, 

return the 2 videos which are most relevant to the given content.

The format should be as follows:
{
  "videos": [
    {
      "id": "",
      "url": ""
    }
  ]
}

Please generate the JSON object based on the above structure.
`;
};

export const getVideoScriptsPrompt = (params: {
  prompt: string;
  language?: 'hi' | 'en';
}): string => {
  const { prompt, language = 'en' } = params;

  const scripts = [
    'problem_solution_script: Address the consumer’s pain point and introduce the product as the solution.',
    'testimonial_script: Present a real-life user story that shows how the product made a difference.',
    'statistic_script: Use relevant statistics to validate the product’s effectiveness.',
    'question_script: Pose a direct question that engages the audience and offers the product as the answer.',
    'narrative_script: Craft a story-driven approach that creates an emotional connection with the viewer.',
    'benefit_focused_script: Emphasize the specific benefits the product delivers to the consumer.',
    'feature_focused_script: Highlight the key features of the product and how they work.',
    "comparison_script: Compare the product to alternatives, showcasing why it's the superior choice.",
    'how_it_works_script: Explain how the product functions in a simple, easy-to-understand manner.',
    'call_to_action_script: End with a strong call to action that motivates the viewer to take immediate steps.',
  ];

  const languageSpecificDetails =
    language === 'hi'
      ? 'Focus on a casual, relatable Hinglish tone, blending Hindi and English. Ensure it resonates with a broader audience while maintaining the product’s message.'
      : 'Focus on clarity and persuasion with a professional English tone.';

  return `You are a highly skilled video script writer known for crafting creative and highly converting 45-60 seconds video ad scripts that drive customer action. Based on the following prompt: "${prompt}", generate 10 unique scripts in the ${
    language === 'en' ? 'English' : 'Hinglish'
  } language:

    ${scripts.join('\n')}

    ${languageSpecificDetails}

    Highlight the product’s unique selling points and ensure each script is engaging, persuasive, and aligned with the brand’s messaging.

    Your response should be structured as JSON in following format-

    {
        "problem_solution_script": {
            "title": "Problem-Solution Script",
            "script": ""
        },
        "testimonial_script": {
            "title": "Testimonial Script",
            "script": ""
        },
        "statistic_script": {
            "title": "Statistic Script",
            "script": ""
        },
        "question_script": {
            "title": "Question Script",
            "script": ""
        },
        "narrative_script": {
            "title": "Narrative Script",
            "script": ""
        },
        "benefit_focused_script": {
            "title": "Benefit-Focused Script",
            "script": ""
        },
        "feature_focused_script": {
            "title": "Feature-Focused Script",
            "script": ""
        },
        "comparison_script": {
            "title": "Comparison Script",
            "script": ""
        },
        "how_it_works_script": {
            "title": "How It Works Script",
            "script": ""
        },
        "call_to_action_script": {
            "title": "Call to Action Script",
            "script": ""
        }
    }
  `;
};

export const getVideoMakerPromptV2 = (params: IParamsForAdCreative): string => {
  return `
    You are a video ad director tasked with creating a video ad from the shared details.

    Consider the following product (or service) details provided by the user:

    ${getBusinessDetailsString(params)}
    
    Based on the above product details, generate a JSON object with the following structure:

    {
      "video_caption": "",  // A brief video description (under 100 characters)
      "frames": [
        {
          "type": "engaging_question",
          "voiceover_text": "",  // Engaging question related to the product/service (under 100 characters)
          "highlighted_text": "",  // Eye-catching text (in English) to highlight in the video
          "image_keywords": [],  // Voiceover related image keywords, always in English
          "video_content_description_involving_people": "" // Voiceover related description, always in english
        },
        {
          "type": "product_or_service_introduction",
          "voiceover_text": "",  // Introduce the product/service (under 100 characters)
          "highlighted_text": "",  // Eye-catching text (in English)
          "image_keywords": [],
          "video_content_description_involving_people": ""
        },
        {
          "type": "content",
          "voiceover_text": "",  // Content related to product/service features or benefits (under 100 characters)
          "highlighted_text": "",
          "image_keywords": [],
          "video_content_description_involving_people": ""
        },
        {
          "type": "content",
          "voiceover_text": "",
          "highlighted_text": "",
          "image_keywords": [],
          "video_content_description_involving_people": ""
        },
        {
          "type": "conclusion",
          "voiceover_text": "",  // Conclusion or call-to-action (under 100 characters)
          "highlighted_text": "",
          "image_keywords": [],
          "video_content_description_involving_people": ""
        }
      ]
    }

    Guidelines:

    1. There will be 5 frames in total- 1 engaging_question, 1 product_or_service_introduction, 2 content and 1 conclusion
    2. The voiceover_text must be professional and match the prompt’s language.
    3. Each voiceover_text should be under 100 characters.
    4. highlighted_text should always be short, eye-catching, and in English.
    5. image_keywords must always be in English, regardless of the prompt’s language.
    6. video_content_description_involving_people will be used to fetch relevant video from stock videos. It should be generic and yet relevant to frame voiceover_text
    7. Example of video_content_description_involving_people: Group of men discussing in meeting room, young couple opening the door to their new home, Friends chatting in a well-lit living space etc
    
    Example:

    {
      "video_caption": "Boost your business with AI-driven ads!",
      "frames": [
        {
          "type": "engaging_question",
          "voiceover_text": "Are you tired of wasting money on ineffective ads? Want an easy way to generate high-quality leads?",
          "highlighted_text": "Tired of ineffective ads?",
          "image_keywords": ["Frustrated business owner", "Worried marketer", "Confused team"],
          "video_content_description_involving_people": "Salesperson talking to an empty room"
        },
        {
          "type": "product_or_service_introduction",
          "voiceover_text": "With GrowEasy, that’s not just a dream—it’s a reality.",
          "highlighted_text": "Introducing GrowEasy",
          "image_keywords": ["Growth chart rising", "Excited business owner", "Happy customers"],
          "video_content_description_involving_people": "Team celebrating"
        },
        {
          "type": "content",
          "voiceover_text": "Our AI-powered tool helps you run tailored campaigns on Facebook and Instagram that bring in the leads you want.",
          "highlighted_text": "Targeted ads on Facebook & Instagram",
          "image_keywords": ["Facebook", "Instagram", "Social media"],
          "video_content_description_involving_people": "Person scrolling Social media"
        },
        {
          "type": "content",
          "voiceover_text": "Say goodbye to uncertainty and hello to reliable results.",
          "highlighted_text":"Hight quality leads that convert",
          "image_keywords": ["Steady growth chart", "Positive business outcomes", "Reliable results"],
          "video_content_description_involving_people": "A marketing team gathers around a table"
        },
        {
          "type": "conclusion",
          "voiceover_text": "Try GrowEasy now and see the difference!",
          "highlighted_text": "Try GrowEasy today!",
          "image_keywords": ["Positive reviews", "Customer testimonials", "Satisfied users],
          "video_content_description_involving_people": "A small business owner smiles"
        }
      ]
    }
  `;
};

export const getGoogleAdCopiesPrompt = (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
    type: GROWEASY_CAMPAIGN_TYPE;
  },
): string => {
  const isCallCampaign = params.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL;
  const uspSection = params.ai_assisted_product_usps?.length
    ? params.ai_assisted_product_usps
        .map((usp, i) => `${i + 1}. ${usp}`)
        .join('\n')
    : '';
  const callOrSearchCampaignInstruction = isCallCampaign
    ? `
You are a professional Google Ads copywriter and performance marketer specializing in Google Call-Only Ads for small and local businesses (SMBs).
Your task is to generate conversion-optimized, minimal-creative ad copy for mobile-only call ads, with a clear goal to drive immediate phone calls from high-intent users.  
`
    : `
You are a professional Google Ads copywriter and performance marketer specializing in Search and Performance Max (P-Max) campaigns for small and local businesses (SMBs).
Your task is to generate conversion-focused, concise ad copy designed to drive immediate action from high-intent users.
  `;
  return `
${callOrSearchCampaignInstruction}

Each ad copy in Google campaigns consists of the following elements:

1. **short_headline** - A super short, attention-grabbing title that **does not exceed 15 characters** (typically 2-3 words).
2. **headline** - A short, attention-grabbing title that **does not exceed 30 characters**.
3. **long_headline** - A longer, descriptive headline that captures more detail. Each long headline **must not exceed 90 characters**.
4. **short_description** - A short informative line that offers more context about the product or service. Each short description **must not exceed 60 characters**
5. **description** - A short informative line that offers more context about the product or service. Each description *must not exceed 90 characters**

### Guidelines for Copies Generation:

1. Avoid creativity, humor, or branding tone. Be direct, sales-focused, and urgency-driven.
2. Highlight value proposition clearly — what the user gets and why they should take action now.
3. Prioritize urgency, offer, trust, and action in that order.
4. Match the searcher’s commercial intent — write like you're answering a search like “emergency plumber near me” or “book skin clinic now”.
5. Always include one of the following call-to-actions: "Call Now", "Book Now", "Get Help", "Talk to Expert", or "Get Quote".

### **Guidelines for Policy Compliance:**
✅ **Capitalization Rules:**
- Avoid excessive capitalization (e.g., "FREE SHIPPING" ❌ → "Free shipping" ✅, "20% OFF" ❌ → "20% Off" ✅)
- Promo codes (e.g., **"SAVE20"**) are allowed.

✅ **Content Accuracy:**
- Do not use misleading phrases (e.g., **"Best in the world!"** ❌).
- Avoid false urgency (e.g., **"Limited stock!"** ❌).

✅ **Punctuation & Symbols:**
- Do not use multiple **!**, **...**, or special symbols (**@, #, &**).
- Proper sentence structure and no unnecessary punctuation.

✅ **Formatting & JSON Compliance:**
- Ensure all fields respect their respective character limits.
- Provide a **valid JSON** response that can be parsed directly.

Now, consider the following product (or service) details provided by the user:

${getBusinessDetailsString(params)}

### Provided Product USPs:
${uspSection}

Based on the product details and your training, please return an array of 20 copies in the following JSON format:

{
  "copies": [
    {
      "short_headline": "",
      "headline": ",
      "long_headline": "",
      "short_description": "",
      "description": ""
    },
    {
      "short_headline": "",
      "headline": ",
      "long_headline": "",
      "short_description": "",
      "description": ""
    }
  ]
}

Ensure that:
- Each short headline is within the 15-character limit
- Each headline is within the 30-character limit
- Each long headline is within the 90-character limit.
- Each short description is within the 60-character limit
- Each description is within the 90-character limit

Please provide a valid JSON response without any special characters. Remember, your response should be JSON-parsable.
`;
};

export const getMetaDataExtractionPrompt = (payload: {
  meta_tags: Record<string, string>;
  title: string;
  apple_touch_icon?: string;
}): string => {
  // Ensure URLs are absolute
  const baseUrl = payload?.meta_tags['og:url'] || '';
  const appleTouchIconUrl = getAbsoluteUrl(
    payload.apple_touch_icon ?? '',
    baseUrl,
  );

  return `
You are an intelligent AI assistant.

Below is the data fetched from the website-

Meta tags-
${JSON.stringify(payload.meta_tags, null, 2)}

Title-
${payload.title}

Apple touch icon-
${appleTouchIconUrl}

Extract the following content from the above meta tags:

{
  "business_name": "",
  "business_square_logo_url": "",
  "business_logo_url": "",
  "business_description": "",
  "business_usp": ""
}

Instructions:
- "business_name" can be extracted from the "og:site_name" or "og:title" meta tag. It should be less than 25 characters.
- "business_square_logo_url" should use the "apple_touch_icon" or any square logo meta tag. Do not use favicon for this purpose.
- "business_logo_url" should use the "og:image" tag or a related meta tag.
- "business_description" can be derived from the "description" or "og:description" tag.
- "business_usp" can be inferred from the "keywords" or a combination of "og:description" and "description".

Ensure all URLs are absolute and complete. Provide a valid JSON response without any special characters. Remember, your response should be JSON-parsable.
`;
};

export const getGoogleLeadFormContentPrompt = (
  params: IParamsForAdCreative,
): string => {
  const examples = [
    {
      industry: 'Car',
      heading:
        'Book a Free Tata Nexon Test Drive – Delivered to Your Doorstep!',
      subheading:
        'Curious about the Tata Nexon? Let the next-level driving experience come to you!',
      description:
        'Enjoy advanced safety, sleek design, and superior performance at your convenience. Fill out the form to schedule your free test drive today!',
      value_props: [
        'Free test drive at your location.',
        'Personalized quote for your preferred model.',
        'Experience advanced features and performance first-hand.',
      ],
      cta_title: 'Get a Free Test Drive',
      cta_label: 'Book Now',
    },
    {
      industry: 'Apartment',
      heading: 'Schedule Your Virtual Tour of Prime Apartments!',
      subheading:
        'Looking for your dream home? Explore every detail without leaving your couch!',
      description:
        'Get a personalized virtual tour of our luxurious apartments, complete with layout options and pricing details. Fill out the form to schedule your tour!',
      value_props: [
        'Access virtual tours of prime apartments.',
        'Get detailed floor plans and pricing.',
        'Explore without leaving your home.',
      ],
      cta_title: 'Get a Personalized Virtual Tour',
      cta_label: 'Schedule Now',
    },
    {
      industry: 'College',
      heading: 'Plan Your Future with a Free Admission Consultation!',
      subheading:
        'Confused about which college is right for you? Get expert guidance today!',
      description:
        'Connect with our counselors to learn about courses, scholarships, and admission steps. Complete the form to book your consultation now!',
      value_props: [
        'Free counseling session with experts.',
        'Detailed admission guide for your desired course.',
        'Scholarship and financial aid information.',
      ],
      cta_title: 'Get Free Consultation',
      cta_label: 'Book Now',
    },
    {
      industry: 'Interior Design',
      heading: 'Transform Your Space with a Free Personalized Mood Board!',
      subheading:
        'Dreaming of a new look for your home? Let us design the perfect style for you!',
      description:
        'Receive a custom mood board tailored to your preferences and budget. Fill out the form to start your design journey!',
      value_props: [
        'Personalized mood board based on your style.',
        'Expert design consultation for free.',
        'Ideas tailored to your budget.',
      ],
      cta_title: 'Receive Customized Mood Board',
      cta_label: 'Start Now',
    },
    {
      industry: 'Software',
      heading: 'Discover the Power of Our Software with a Free Demo!',
      subheading:
        'Frustrated with outdated tools? See how our solution can simplify your work!',
      description:
        'Explore the features and benefits of our platform in a live demo with our experts. Fill out the form to schedule your session!',
      value_props: [
        "Live demo of the software's key features.",
        'Customized consultation for your business needs.',
        'Exclusive tips to maximize productivity.',
      ],
      cta_title: 'Get a Free Demo',
      cta_label: 'Schedule Now',
    },
  ];

  const instructions = `
    You are an expert UX designer and persuasive product marketer. Using proven frameworks like AIDA (Attention, Interest, Desire, Action), PAS (Problem-Agitate-Solution), and FAB (Features, Advantages, Benefits), create engaging content fields for a lead generation form that provides users with a tangible value aligned with the business's core offering.

    The value or offer will be delivered asynchronously (e.g., scheduled demo, consultation, test drive, or personalized plan). Examples of value propositions include:

    - Car: Free test drive at home or a personalized quote.
    - Apartment: A virtual tour or a curated brochure of available units.
    - College: Free consultation with a counselor or a detailed admission guide.
    - Interior Design: A free personalized mood board or a design consultation.
    - Software: A live product demo or a free consultation to explore solutions.

    Now, consider the following product (or service) details provided by the user:

    ${getBusinessDetailsString(params)}

    Based on the business details provided, generate the content fields for the form.

    ### Content Fields to Create:
    1. **Form Heading:**  
      Use AIDA to create a concise, benefit-oriented title that highlights what users will receive (e.g., demo, consultation, test drive) and how it aligns with their needs or desires.

    2. **Subheading:**  
      Apply PAS to identify the primary problem or aspiration, build urgency or excitement, and position the value offer as the solution or enabler.

    3. **Description:**  
      Use FAB to outline the key features and advantages of the value offer, linking them to the user’s direct benefits. Add a clear, action-oriented statement encouraging users to complete the form.

    4. **Value Props:**  
      Create an array of short, engaging value propositions highlighting what the user will gain by completing the form.

    5. **CTA Title:**  
      Write a short and persuasive title representing the offering itself (e.g., “Book Free Consultation,” “Get a Free Demo,” “Receive Customized Mood Board”).

    6. **CTA Label:**  
      Provide an action-oriented label that emphasizes urgency (e.g., “Book Now,” “Schedule Now,” “Start Now,” “Explore Now”, Contact Now”).

    Your response should be in the following JSON format:

    \`\`\`json
    {
      "heading": "",
      "subheading": "",
      "description": "",
      "value_props": [],
      "cta_title": "",
      "cta_label": ""
    }
    \`\`\`

    ### Example Outputs:
    ${examples
      .map(
        (example) => `
    **${example.industry}:**

    Form Heading: "${example.heading}"  
    Subheading: "${example.subheading}"  
    Description: "${example.description}"  
    Value Props: ${JSON.stringify(example.value_props, null, 2)}  
    CTA Title: "${example.cta_title}"  
    CTA Label: "${example.cta_label}"  
    `,
      )
      .join('\n')}
  `;

  return instructions;
};

export const getGoogleSearchKeywordsPrompt = (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
  },
): string => {
  const uspSection = params.ai_assisted_product_usps?.length
    ? params.ai_assisted_product_usps
        .map((usp, i) => `${i + 1}. ${usp}`)
        .join('\n')
    : '';

  return `
      You are an expert search marketing strategist with deep knowledge of crafting effective keyword strategies for search ad campaigns. 

    Using frameworks like direct intent (e.g., service-related keywords) and indirect intent (e.g., pain points, benefits, and solutions), generate a detailed, categorized keyword list for the given business. Ensure the keywords are aligned with various search intents and user needs.

    The keyword categories should include:
    1. **service_related**: Keywords directly tied to the services or products offered by the business (e.g., "digital marketing agency," "lead generation services").
    2. **customer_pain_points**: Keywords reflecting challenges, frustrations, or unmet needs of potential customers (e.g., "high cost of ads," "low ROI marketing solutions").
    3. **industry_specific**: Keywords specific to the industry in which the business operates (e.g., "B2B lead generation," "e-commerce marketing").
    4. **technology_related**: Keywords that highlight relevant technologies or tools (e.g., "AI-powered lead generation," "marketing automation tools").
    5. **compliance_and_regulatory**: Keywords addressing industry rules, certifications, or standards (if applicable).
    6. **marketing_and_branding**: Keywords focused on enhancing customer engagement or showcasing the business’s brand (e.g., "ROI-driven marketing," "expert marketing solutions").
    7. **miscellaneous**: General keywords covering broader business needs or related searches (e.g., "top marketing agencies," "best lead generation platforms").
    8. **long_tail**: Longer, highly specific keywords addressing niche search intents or specific user actions (e.g., "affordable lead generation agency for startups").

    ---

    Enforcements:
    - Each category must contain exactly 3 keywords.
    - Each keyword must not exceed 60 characters in length.

    ---

    Now, consider the following product (or service) details provided by the user:
    
    ${getBusinessDetailsString(params)}

    ### Provided Product USPs:
    ${uspSection}

    ---

    Provide the output in the following JSON format:

    {
      "service_related": ["", "", ""],
      "customer_pain_points": ["", "", ""],
      "industry_specific": ["", "", ""],
      "technology_related": ["", "", ""],
      "compliance_and_regulatory": ["", "", ""],
      "marketing_and_branding": ["", "", ""],
      "miscellaneous": ["", "", ""],
      "long_tail": ["", "", ""]
    }

    ---

    Example Output for a Lead Generation Marketing Agency:

    {
      "service_related": ["lead generation services", "digital marketing agency", "B2B lead solutions"],
      "customer_pain_points": ["high ad costs", "low marketing ROI", "poor lead quality"],
      "industry_specific": ["e-commerce lead generation", "B2B marketing campaigns", "startup marketing solutions"],
      "technology_related": ["AI-powered lead generation", "marketing automation tools", "predictive analytics tools"],
      "compliance_and_regulatory": ["GDPR-compliant marketing", "data privacy rules", "marketing compliance tips"],
      "marketing_and_branding": ["ROI-driven marketing", "expert marketing agency", "trusted lead gen partner"],
      "miscellaneous": ["top marketing agencies", "best lead gen companies", "affordable marketing services"],
      "long_tail": ["affordable lead generation agency", "how to generate SaaS leads", "best B2B marketing agency"]
    }
  `;
};

export const getGoogleSearchKeywordsPromptV2 = (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
    type: GROWEASY_CAMPAIGN_TYPE;
  },
): string => {
  const isCallCampaign = params.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL;
  const uspSection = params.ai_assisted_product_usps?.length
    ? params.ai_assisted_product_usps
        .map((usp, i) => `${i + 1}. ${usp}`)
        .join('\n')
    : 'None provided';
  const callOrSearchCampaignInstruction = isCallCampaign
    ? `
Your task is to generate **5 to 10 high-intent seed keywords** for a **Google Call-Only Ad campaign** targeting **mobile users** who are most likely to **call immediately**.  
  `
    : `
Your task is to generate 5 to 10 high-intent seed keywords for a Google Search Ad campaign targeting users actively looking for this product or service, with a strong purchase or inquiry intent.  
  `;

  return `
    You are a Google Ads keyword strategist and expert performance marketer.
    ${callOrSearchCampaignInstruction}
    ### Keyword Generation Rules:
    - Only include **high-commercial-intent, call-worthy** keywords.
    - Keywords must reflect **urgent**, **localized**, or **service-seeking** intent.
    - Use modifiers such as: “**near me**”, “**open now**”, “**same day**”, “**call now**”, “**emergency**”, “**24x7**”.
    - **Avoid** informational or low-conversion phrases (e.g., “how to fix…”, “best brands…”, “benefits of…”).
    - Return only keywords, not explanations.

    ---

    Now, consider the following product (or service) details provided by the user:
    ${getBusinessDetailsString(params)}

    ### Provided Product USPs:
    ${uspSection}

    ---

    Output the keywords in **this strict JSON format**:

    {
      "all": [
        "keyword 1",
        "keyword 2",
        ...
      ]
    }
  `;
};

export const getAiBannerImageGenPrompt = (
  params: IParamsForAdCreative & {
    banner_details: {
      creative_title: string;
      call_out: string;
      call_to_action: string;
      focused_usp: string;
      imagery: string;
    };
    google_geo_locations?: IGoogleLocationDetails[];
    tiktok_geo_locations?: ITiktokLocationDetails[];
  },
): string => {
  const { banner_details: bannerDetails } = params;
  const locations = getLocationDetailsString(params);

  return `
Create a single visual prompt for generating a **Meta ad banner** using the details below.

### Banner Elements:
- **USP**: ${bannerDetails.focused_usp}
- **Title**: ${bannerDetails.creative_title}
- **Call Out**: ${bannerDetails.call_out}
- **CTA**: ${bannerDetails.call_to_action}
- **Imagery Description**: ${bannerDetails.imagery}

The prompt should describe a realistic, visually compelling banner scene that includes the above text elements and imagery in a mobile-friendly format.

### Business Context:
${getBusinessDetailsString(params)}

### Targeted Locations:
${locations}

### Visual & Textual Requirements:
- Use natural-looking faces representing the target location's demographics (age 20–45)
- Based on the location data, select faces that reflect the typical appearance of people from that region
- Ensure all text is legible (minimum 48pt equivalent)
- Language must be simple, clear, and grammatically correct
- Text (Title, Call Out, CTA) must be clearly positioned
- Imagery must visually reflect the product/service and support the messaging
- Typeface and brand colors should be borrowed from the product image if available
- Ensure the visual layout is coherent and realistic for ad rendering

# Examples for Face Selection Based on Location:

For India: Use faces with South Asian features common in the Indian subcontinent
For USA: Use a diverse mix of faces reflecting American ethnic diversity (Caucasian, African American, Hispanic, Asian American, etc.)
For Indonesia: Use faces with Southeast Asian features common in Indonesia
For Nigeria: Use faces with West African features common in Nigeria
For Brazil: Use faces reflecting Brazil's diverse population (mixed European, African, and indigenous heritage)

### Output Format (JSON):
{
  "prompt": "[Scene description. Title: '[title]' [placement/style]. Call Out: '[text]' [placement/style]. CTA: '[text]' [placement/style]. Style: [type]. Mood: [mood].]"
}

Return only the prompt object as JSON.
`;
};

/**
 * @deprecated
 */
export const getAiBannerImageGenPromptV2 = (
  params: Omit<IParamsForAdCreative, 'targeting'> & {
    ai_assisted_product_usps?: string[];
  },
): string => {
  const promptTemplate = {
    meta: {
      ad_type: 'Meta Sponsored Post',
      size: '1080x1080',
      goal: 'Generate a clean, high-converting Meta ad banner layout for a product or service using input variables and layout instructions.',
    },
    layout: {
      headline: {
        position: 'top',
        generation_instruction:
          "Generate a bold, direct headline that clearly states what is being sold, the price, and location. No creativity. Be factual and grammar-perfect. Format: '{{Product}} - ₹{{Price}} | {{Location}}'",
      },
      subheadline: {
        position: 'below_headline',
        generation_instruction:
          'Generate 2–3 short, high-intent USP bullet points from the product details. Focus on benefits like warranty, EMI, certification, usage, features, etc.',
      },
      main_visual: {
        position: 'center',
        generation_instruction:
          "Describe the ideal real photo to use for the ad. Mention product angle, lighting, clean background, and context appropriate for '{{Category}}' and '{{Location}}'.",
      },
      cta_button: {
        position: 'bottom_center',
        generation_instruction:
          "Generate a 2–5 word high-intent CTA. Must be actionable and context-specific. Examples: 'Book Test Drive Now', 'Enroll Today', 'Buy Now at ₹{{Price}}'.",
      },
      contact_info: {
        position: 'bottom_bar',
        generation_instruction:
          "Present contact details as clean plain text. Format: 'Phone: {{Phone}} | WhatsApp: {{WhatsApp}} | Location: {{Location}}'. No emojis or icons.",
      },
      trust_badge: {
        position: 'below_visual',
        generation_instruction:
          "Write 1 short credibility/trust line based on '{{DealerInfo}}'. Examples: years of experience, certification, platform rating, brand trust, etc.",
      },
    },
    design_style: {
      theme: 'flat_modern',
      font_family: 'sans-serif',
      accent_color: '#1E73BE',
      background_color: '#FFFFFF',
      mobile_friendly: true,
      rules: [
        'Use clear, modern fonts only (no cursive).',
        'No emojis or icons — all content must be written.',
        'Balance spacing — keep it readable even on mobile.',
        'Stick to a clean layout — whitespace is good.',
        'Use consistent text alignment throughout.',
      ],
    },
    variables: {
      'Business Category': params.business_details.business_category,
      'Product / Service Description':
        params.business_details.product_or_service_description,
      USPs: params.ai_assisted_product_usps,
      Phone: params.business_details.mobile,
      website: params.business_details.website,
    },
  };
  return JSON.stringify(promptTemplate, null, 2);
};

export const getUspAndBannerElementDetailsPromptV2 = (params: {
  business_details: IBusinessDetails;
  ai_assisted_product_usps?: string[];
}): string => {
  const uspSection = params.ai_assisted_product_usps?.length
    ? params.ai_assisted_product_usps
        .map((usp, i) => `${i + 1}. ${usp}`)
        .join('\n')
    : '';

  const businessAndProductDetails = `
### Business & Product Details:
${getBusinessDetailsString({
  business_details: params.business_details,
  targeting: {
    age_min: 21,
    age_max: 65,
    genders: [1, 2],
    geo_locations: {},
    flexible_spec: [],
    age_range: [],
  },
})}
- phone: ${params.business_details.mobile}
${
  params.business_details.website
    ? `- website: ${params.business_details.website}`
    : ''
}
`.trim();

  return `

You are a **performance-focused graphic designer specializing in Meta Ads for SMBs**.

Using the **high-conversion banner ad playbook**, generate a Meta ad banner **copy concept** with **layout instructions** and **text content** tailored for high engagement and conversion.

## Variables:

### Key USPs:
${uspSection}

${businessAndProductDetails}

## Guidelines:
1. **Be direct, not overly creative, in the headline** — clearly state what is being sold and who it’s for. *“Creative is the new targeting”* — so be clear about the **target audience**.
2. **Always include**: price, location, product name, contact info, and a compelling CTA.
3. **Verify that all user-provided details (e.g., company name, phone number, website, location) are correct and relevant before including them in the output.** If something seems missing or incorrect, dont make any assumptions and just don't include it in the output.
4. **Do not use placeholders** like "XXX-XXX-XXXX", "Lorem Ipsum", "Product Name", "TBD", etc.  
   - No assumptions or fabricated values are allowed.  
   - Only use real provided data or omit the field.  
5. Stick to the following **banner layout**:

* **Headline (Top):** Bold, clear, and value-driven.
* **Main Visual:** Suggest a realistic image style (e.g., product-in-use, happy customer, etc.).
* **Offer Details:** Highlight 2–3 specific USPs or benefits.
* **Trust & Branding:** Add elements like badges, years of experience, ratings, etc.
* **Contact Info Bar:** Phone, WhatsApp, location.
* **CTA Button:** Clear, action-oriented text.

## Output Format:

\`\`\`json
{
  "banners": [
    {
      "headline_text": "",
      "subheadlines_or_usp": ["", "", ...],
      "call_to_action": "",
      "visual_background_image_type": ["", "", ...],
      "contact_details": "",
    }
  ]
}
\`\`\`

`;
};

export const getAiBannerImageGenPromptV3 = (params: {
  banner_details: {
    headline_text: string;
    subheadlines_or_usp: string[];
    call_to_action: string;
    visual_background_image_type: string[];
    contact_details: string;
    design_notes: string[];
  };
}): string => {
  return `

Create a clean, professional Meta ad banner (1080x1080 px) using the details below.

Content and Layout:

1. HEADLINE (top section, bold, no spelling or grammar errors):  
   ${params.banner_details.headline_text}

2. SUBHEADLINE / USPs (just below headline in smaller font):  
   - ${params.banner_details.subheadlines_or_usp.join('\n -')}  

3. MAIN VISUAL (center area):  
   - ${params.banner_details.visual_background_image_type.join('\n -')}

4. CTA BUTTON (bottom center in bright blue, white bold text):  
   ${params.banner_details.call_to_action}

5. CONTACT INFO (bottom bar, plain text — no icons):  
   ${params.banner_details.contact_details}  


Design Style:
- Flat, modern design  
- Use readable fonts (no stylized or cursive text)  
- No spelling or grammatical mistakes  
- Use neutral color palette with accents of blue  
- Keep spacing balanced and mobile-optimized  
- No icons or emojis — all info must be written as clean, readable text

`;
};

export const getAdLanguageSuggestonsPrompt = (
  params: IParamsForAdCreative & {
    google_geo_locations?: IGoogleLocationDetails[];
    tiktok_geo_locations?: ITiktokLocationDetails[];
  },
): string => {
  const allowedLanguages = Object.values(AdLanguage).join(', ');
  const locations = getLocationDetailsString(params);

  return `
You are an AI language expert helping advertisers determine the most appropriate languages for their campaign creatives.

Based on the target locations, please suggest **max 3 languages** from the following list only:

**Allowed Languages:** ${allowedLanguages}

### Targeted Locations:
${locations}

### Your Response Format:
Return a JSON object in the following format:

{
  "language_suggestions": [
    {
      "language": "English",
      "reason": "English is widely understood and used in digital advertising."
    },
    {
      "language": "Hindi",
      "reason": "Campaign targets North Indian states where Hindi is the primary spoken language."
    }
  ]
}

### Language Selection Guidelines:
  - Always select languages only from the allowed list provided above.
  - English must always be the first language in the suggestions.
  - Include only the most widely spoken/ primary languages for a given location; avoid minority or less spoken languages to ensure better ad performance.
    Examples:
      1. India → English, Hindi, Bengali
      2. Indore → English, Hindi
      3. Gujarat → English, Gujarati, Hindi
      4. United States → English
  - The final output must be valid JSON without any special characters, emojis, or formatting errors.
`;
};

// this function will generate Initial USPs as well as Ad Banner elements
// while generating USPs, keep ad_language in English and ai_assisted_product_usps empty
export const getUspAndBannerElementDetailsPrompt = (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
  },
): string => {
  const {
    business_details: businessDetails,
    ad_language: adLanguage,
    ai_assisted_product_usps: aiAssistedProductUsps,
  } = params;

  const resolvedLanguage = adLanguage ?? AdLanguage.ENGLISH;

  const uspSection = aiAssistedProductUsps?.length
    ? aiAssistedProductUsps.map((usp, i) => `${i + 1}. ${usp}`).join('\n')
    : '';

  const businessAndProductDetails = `
### Business & Product Details:
${getBusinessDetailsString(params)}
`.trim();

  return `
You are a performance marketing and lead generation–focused creative designer and copywriter with 15+ years of experience in the ${businessDetails.business_category} industry.

Your task: **Create 5 highly specific ad banners** for the product/service described below, to run campaigns on Meta Ads.

---

### Product & USP Details:
${uspSection}

${businessAndProductDetails}

---

### Banner Creation Rules:

**1. Creative Title**
- Must explicitly mention the product/service name or core offering **AND** either a key benefit or problem it solves.
- Must be direct, descriptive, and crystal clear to the target buyer.
- Avoid vague words like "best", "amazing", "top".
- It should be in ${resolvedLanguage}
- Examples:
  - "Generate 3X More Leads with Our AI-powered CRM – Free 7-Day Trial"
  - "Affordable Luxury Villas in Goa – Starting ₹1.9 Cr"
  - "Fix Back Pain in 2 Weeks with Our Ergonomic Chairs"
  - "Interiors Designing – Starting at ₹2 L"
  - "Tata Punch – Exclusive Diwali Discounts"
  - "Best School in Gurgaon for Your Kid"
  - "Easiest Weight Loss – Without Dieting"

**2. Call Out**
- Explain why they should trust or buy.
- Focus on a unique benefit, proof, or problem resolution.
- Include numbers, timelines, or differentiators where possible.
- It should be in ${resolvedLanguage}
- Examples:
  - "Trusted by 5,000+ Indian SMEs"
  - "Free Shipping Across India"
  - "Clinically Proven Relief in 7 Days"

**3. CTA (Call to Action)**
- Short, urgent, and action-oriented.
- Must be in ${resolvedLanguage}.
- Examples: "Order Now", "Book a Demo", "Sign Up Today", "Claim Offer"

**4. Imagery**
- Should match the above copy context.
- It should be in English
- Keep it simple, realistic, and with negative space for UI overlays.

**5. Image Keywords**
- 3–5 generic but relevant stock image descriptions (in English).
- Must align with buyer persona and scenario.

**6. Focused USP**
- Use one unique USP from the provided list.
- If fewer than 5 USPs are given, create new hooks yourself.

---

### Output Format (Valid JSON only):

\`\`\`json
{
  "banners": [
    {
      "creative_title": "",
      "call_out": "",
      "call_to_action": "",
      "creative_image_keywords": [],
      "imagery": "",
      "focused_usp": ""
    }
  ]
}
\`\`\`

---

### Text Rendering Optimization
- Avoid complex text effects.
- Use realistic lighting/shadow patterns.
- Leave negative space for platform UI overlays.
`.trim();
};

export const getAdInsightCardsPrompt = (params: {
  overallInsightsData: object[];
  regionWiseInsightsData: object[];
  demographicsWiseInsightsData: object[];
  copiesWiseInsightsData: object[];
  hourlyInsightsData: object[];
}): string => {
  const overallInsightsDataSection = `
  # Overall Insights

  ${JSON.stringify(params.overallInsightsData, null, 2)}
  `;

  const regionWiseInsightsDataSection = `
  # Region-wise Insights

  ${JSON.stringify(params.regionWiseInsightsData, null, 2)}
  `;

  const demographicsWiseInsightsDataSection = `
  # Demographics-wise Insights

  ${JSON.stringify(params.demographicsWiseInsightsData, null, 2)}
  `;

  const copiesWiseInsightsDataSection = `
  # Ad Copies-wise Insights

  ${JSON.stringify(params.copiesWiseInsightsData, null, 2)}
  `;

  const hourlyInsightsDataSection = `
  # Hourly Insights

  ${JSON.stringify(params.hourlyInsightsData, null, 2)}
  `;

  return `
  You are a performance marketing analytics expert with 10 years of performance marketing experience. Your task is to analyze the raw Meta Ads data and generate actionable insights across the following dimensions:

  1. Location-Based Performance → Identify the best and worst-performing states, cities, or regions. Highlight regional preferences, cost variations, and engagement trends.

  2. Content-Based Performance → Determine which ad creatives (copy, format, visuals, CTAs) drive the highest engagement and conversions. Identify patterns in high vs. low-performing content.

  3. Gender & Age-Based Performance → Analyze performance by gender and age groups, highlighting key differences in engagement and conversion rates. Identify the most responsive audience segments.

  4. Hidden Trends & Optimization Opportunities → Surface unexpected insights such as time-of-day performance, device preferences, or behavioral patterns that can improve campaign efficiency.

  5. Best Ad Hooks & USPs → Based on top-performing ads, suggest compelling USPs, messaging angles, and ad hooks that resonate most with the engaged audience.

  Ensure the insights are structured, data-driven, and provide clear recommendations for future campaign optimization.

  Required JSON output format:

  {
    "insights_cards": [
      {
        "title": "Insight Title",
        "description": "Insight Description with reasoning from data."
      },
      ...
    ]
  }

  ${overallInsightsDataSection}
  ${regionWiseInsightsDataSection}
  ${demographicsWiseInsightsDataSection}
  ${copiesWiseInsightsDataSection}
  ${hourlyInsightsDataSection}
  `;
};

export const getIdealCustomersPrompt = (params: {
  business_details: IBusinessDetails;
}): string => {
  return `
  You are a top 1% performance marketing expert for the ${
    params.business_details.business_category
  } industry, specializing in high-ROI lead generation campaigns on Meta Ads (Facebook & Instagram).

  Given the following product or business details, generate **5 to 6 concise and specific ideal customer profiles** that we should target with Meta Ads.

  Each customer profile should:
  - Be clear and short (5–6 words)
  - Represent a **targetable audience segment**
  - Be **highly relevant**, likely to convert, and compatible with Meta Ads targeting
  - Avoid generic or vague terms like “everyone”, “anyone”, “all people”, or overly broad descriptors

  In addition to customer profiles, provide the following fields:

  ### Required Metadata:

  1. **age_min** – The minimum age most relevant to the product/service.
    - Must be ≥ 21
    - Must not always default to 21
    - Be **specific and realistic**, based on the likely buyer or decision-maker age

  2. **age_max** – The maximum relevant age (≤ 65)
    - Should reflect realistic upper age of conversion
    - Do not always default to 65 – be **strategic based on the offering**

  3. **genders** – Use numeric format:
    - [1] for male
    - [2] for female
    - [1, 2] for both

  4. **consumer_type** – Indicate if the ideal target audience is:
    - \`"B2B"\` (selling to businesses)
    - \`"B2C"\` (selling to individual consumers)
    - \`"All"\` only if **both apply equally**
    - Do **not default to ‘All’** unless clearly justified

  ---

  ### Business Details:

  - **Business Category:** ${params.business_details.business_category}
  - **Product / Service Description:** ${
    params.business_details.product_or_service_description
  }
  - **Product / Service Offers / USPs:** ${
    params.business_details.product_or_service_offers_or_usp ?? ''
  }

  ---

  ### Output JSON Format

  \`\`\`json
  {
    "ideal_customers": "Customer Type 1, Customer Type 2, Customer Type 3, Customer Type 4, Customer Type 5",
    "age_min": ,
    "age_max": ,
    "genders": [],
    "consumer_type": ""
  }
  \`\`\`
  `;
};

export const getMetaAudienceKeywordsPrompt = (
  params: IParamsForAdCreative,
): string => {
  return `
ROLE:
You are a Meta ads targeting expert. Your task is to suggest exactly 5 valid interest keywords that are directly available in Meta Ads Manager's interest targeting options.

CONSTRAINTS:
- Only return **interests that exactly match real entries in Meta Ads Manager**.
- DO NOT generate descriptive or non-targetable phrases like “Luxury Real Estate” or “Residential Areas”.
- Stick to known, commonly used interests (like “Real Estate”, “Interior Design”, “Property Management”).
- Avoid adjectives and combinations unless they are **confirmed interest terms in Meta**.

BUSINESS DETAILS:
${getBusinessDetailsString(params)}

RESPONSE FORMAT:
Only return clean JSON as shown below — no explanation, no commentary.

{
  "keywords": [
    "Real Estate",
    "Property Management",
    "Interior Design",
    "Home Improvement",
    "Home Buyers"
  ]
}
`.trim();
};
