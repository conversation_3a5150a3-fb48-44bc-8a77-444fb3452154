import { ElevenLabsClient } from '@elevenlabs/elevenlabs-js';
import { Readable } from 'stream';
import { pipeline } from 'stream/promises';
import fs from 'fs';
import {
  type CharacterAlignmentResponseModel,
  type SpeechToTextConvertResponse,
} from '@elevenlabs/elevenlabs-js/api';
import util from 'util';

const elevenLabsClient = new ElevenLabsClient({
  apiKey: process.env.ELEVENLABS_API_KEY,
});

export const generateSpeechFromText = async ({
  text,
  outputFilePath,
}: {
  text: string;
  outputFilePath: string;
}): Promise<void> => {
  const voiceId = 'JBFqnCBsd6RMkjVDRZzb';

  const audio = await elevenLabsClient.textToSpeech.convert(voiceId, {
    outputFormat: 'mp3_44100_128',
    text,
    modelId: 'eleven_multilingual_v2',
  });
  // Convert ReadableStream to Node.js Readable stream
  const nodeStream = Readable.fromWeb(audio as any);

  // Save to file using pipeline
  await pipeline(nodeStream, fs.createWriteStream(outputFilePath));
};

export const generateTranscriptFromAudioUrl = async ({
  fileUrl,
}: {
  fileUrl: string;
}): Promise<SpeechToTextConvertResponse> => {
  return await elevenLabsClient.speechToText.convert({
    modelId: 'scribe_v1',
    cloudStorageUrl: fileUrl,
    tagAudioEvents: false,
  });
};

export const generateSpeechWithTimingFromText = async ({
  text,
  outputFilePath,
}: {
  text: string;
  outputFilePath: string;
}): Promise<{
  alignment: CharacterAlignmentResponseModel | undefined;
  normalizedAlignment: CharacterAlignmentResponseModel | undefined;
}> => {
  const voiceId = 'tnSpp4vdxKPjI9w0GnoV'; // Female Eng + Hindi voice
  // const voiceId = 'qSV5UqvHBC0Widy71Esh';
  // const voiceId = 'SZfY4K69FwXus87eayHK';

  const audioResponse =
    await elevenLabsClient.textToSpeech.convertWithTimestamps(voiceId, {
      outputFormat: 'mp3_44100_128',
      text,
      modelId: 'eleven_multilingual_v2',
      // applyTextNormalization: 'on',
    });

  const audioBytes = Buffer.from(audioResponse.audioBase64, 'base64');
  const writeFile = util.promisify(fs.writeFile);

  // Write the binary audio content to a local file
  await writeFile(outputFilePath, audioBytes, 'binary');

  return {
    alignment: audioResponse.alignment,
    normalizedAlignment: audioResponse.normalizedAlignment,
  };
};

export const getWordsAlignmentFromCharAlignment = (
  alignment: CharacterAlignmentResponseModel | undefined,
): Array<{
  text: string;
  start: number;
  end: number;
}> => {
  const wordAlignmentData: Array<{ text: string; start: number; end: number }> =
    [];

  let tempData: Array<{ char: string; start: number; end: number }> = [];

  if (
    !alignment?.characters ||
    !alignment.characterStartTimesSeconds ||
    !alignment.characterEndTimesSeconds
  ) {
    throw new Error(
      'missing data in alignment in getWordsAlignmentFromCharAlignment',
    );
  }

  for (let i = 0; i < alignment?.characters.length; i++) {
    const currChar = alignment.characters[i];
    const currStart = alignment.characterStartTimesSeconds[i];
    const currEnd = alignment.characterEndTimesSeconds[i];

    if (currChar === ' ') {
      if (tempData.length > 0) {
        const text = tempData.map((item) => item.char).join('');
        const start = tempData[0].start;
        const end = tempData[tempData.length - 1].end;

        wordAlignmentData.push({ text, start, end });
        tempData = [];
      }
    } else {
      tempData.push({ char: currChar, start: currStart, end: currEnd });
    }
  }

  if (tempData.length > 0) {
    const text = tempData.map((item) => item.char).join('');
    const start = tempData[0].start;
    const end = tempData[tempData.length - 1].end;

    wordAlignmentData.push({ text, start, end });
  }

  return wordAlignmentData;
};

export const removeLongFormWordsFromTranscript = (
  transcript: Array<{
    text: string;
    start: number;
    end: number;
  }>,
  script: string,
): Array<{
  text: string;
  start: number;
  end: number;
}> => {
  const newTranscript: Array<{
    text: string;
    start: number;
    end: number;
  }> = [];

  let transcriptCursor = 0;
  const scriptArr = script.split(' ');

  let i = 0;
  while (i < scriptArr.length) {
    if (scriptArr[i].startsWith('{')) {
      const startTime = transcript[transcriptCursor].start;
      let wholeText = scriptArr[i];
      i++;

      while (i < scriptArr.length) {
        if (wholeText.match(/\{([^}]+)\}\[([^\]]+)\]/)) {
          break;
        } else {
          wholeText += ` ${scriptArr[i]}`;
          i++;
        }
      }

      let spokenText = '';
      let visualText = '';

      const match = wholeText.match(/\{([^}]+)\}\[([^\]]+)\](.*)/);
      if (match) {
        spokenText = match[1];
        visualText = match[2];
        visualText += match[3] ?? '';
      }

      transcriptCursor += spokenText.split(' ').length;

      if (!visualText) {
        throw new Error("unable to find pattern '{...}}[...]' pattern");
      }

      const endTime = transcript[transcriptCursor - 1].end;

      newTranscript.push({
        text: visualText,
        start: startTime,
        end: endTime,
      });
    } else if (scriptArr[i] === transcript[transcriptCursor].text) {
      // Regular word, just copy from transcript
      if (transcriptCursor >= transcript.length) {
        throw new Error('Transcript cursor exceeded transcript length');
      }
      if (scriptArr[i] !== '...')
        newTranscript.push(transcript[transcriptCursor]);
      transcriptCursor++;
      i++;
    } else {
      i++;
    }
  }

  return newTranscript;
};
