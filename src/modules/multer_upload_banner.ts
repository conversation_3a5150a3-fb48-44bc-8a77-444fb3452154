import { existsSync, mkdirSync } from 'fs';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

import { TMP_ADIMAGES_UPLOAD_DIR } from '../constants';

const storage = multer.diskStorage({
  destination: function (req, file, callback) {
    if (!existsSync(TMP_ADIMAGES_UPLOAD_DIR)) {
      mkdirSync(TMP_ADIMAGES_UPLOAD_DIR, { recursive: true });
    }
    callback(null, TMP_ADIMAGES_UPLOAD_DIR);
  },
  filename: function (req, file, callback) {
    const fileExtension = path.extname(file.originalname) || '';
    // generate unique filename using the uuid.v4
    const safeFilename = `${Date.now()}-${uuidv4()}${fileExtension}`;
    callback(null, safeFilename);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50 MB
  },
}).single('file');

export default upload;
