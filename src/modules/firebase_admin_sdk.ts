import firebaseAdmin, { type ServiceAccount } from 'firebase-admin';

const CERT = process?.env?.GROWEASY_FIREBASE_SERVICE_ACC_PRIVATE_KEY?.replace(
  /\\n/g,
  '\n',
);

const BANERBOT_CERT =
  process?.env?.BANNERBOT_FIREBASE_SERVICE_ACC_PRIVATE_KEY?.replace(
    /\\n/g,
    '\n',
  );

export const GROWEASY_SERVICE_ACCOUNT = {
  type: 'service_account',
  project_id: 'groweasy-a8ba3',
  private_key_id: '8d5229e40b9da02047b27866964226904ca79d5f',
  private_key: CERT,
  client_email:
    '<EMAIL>',
  client_id: '101239762135154254812',
  auth_uri: 'https://accounts.google.com/o/oauth2/auth',
  token_uri: 'https://oauth2.googleapis.com/token',
  auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
  client_x509_cert_url:
    'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-9k60e%40groweasy-a8ba3.iam.gserviceaccount.com',
  universe_domain: 'googleapis.com',
};

const BANNERBOT_SERVICE_ACCOUNT = {
  type: 'service_account',
  project_id: 'banner-bot-675a8',
  private_key_id: 'e1986e9c1a1f67c2dc06a67d70b3a7aa555a1412',
  private_key: BANERBOT_CERT,
  client_email:
    '<EMAIL>',
  client_id: '105571820079715830735',
  auth_uri: 'https://accounts.google.com/o/oauth2/auth',
  token_uri: 'https://oauth2.googleapis.com/token',
  auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
  client_x509_cert_url:
    'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-jqjno%40banner-bot-675a8.iam.gserviceaccount.com',
  universe_domain: 'googleapis.com',
};

const groweasyApp = firebaseAdmin.initializeApp(
  {
    credential: firebaseAdmin.credential.cert(
      GROWEASY_SERVICE_ACCOUNT as ServiceAccount,
    ),
  },
  'groweasy.ai',
);

const bannerbotApp = firebaseAdmin.initializeApp(
  {
    credential: firebaseAdmin.credential.cert(
      BANNERBOT_SERVICE_ACCOUNT as ServiceAccount,
    ),
  },
  'bannerbot.xyz',
);

export const db = groweasyApp.firestore();
export const auth = groweasyApp.auth();

export const bannerbotDb = bannerbotApp.firestore();
// TODO : To be removed after addVideoDataErrorToFirebase
bannerbotDb.settings({ ignoreUndefinedProperties: true });
export const bannerbotAuth = bannerbotApp.auth();

export const growEasyLangchainMemoryConfig = {
  projectId: GROWEASY_SERVICE_ACCOUNT.project_id,
  credential: firebaseAdmin.credential.cert(
    GROWEASY_SERVICE_ACCOUNT as ServiceAccount,
  ),
};
