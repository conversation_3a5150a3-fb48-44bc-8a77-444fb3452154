import ffmpeg from 'fluent-ffmpeg';
import { existsSync, mkdirSync } from 'fs';
import path from 'path';

import {
  type IBannerBasedVideoTemplate,
  type IVideoFrameMediaProps,
  type IVideoFrameTextProps,
  type IVideoTemplate,
} from '../../types/video_template';
import {
  TMP_BANNERBOT_AUDIO_UPLOAD_DIR,
  BANNERBOT_S3_PUBLIC_BUCKET_NAME,
  S3_BANNERBOT_VIDEO_MAKER_VIDEO_UPLOAD_DIR,
  S3_BANNERBOT_VIDEO_MAKER_AUDIO_UPLOAD_DIR,
} from '../../constants';
import logger from '../logger';
import { convertTextToSpeech, type IScriptDuration } from '../google_tts';
import { fsUnlink } from '../../controllers/util';
import { uploadFileToS3 } from '../aws/s3/index';

/* const getVideoDimensions = (inputVideo: string) => {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(inputVideo, (err, metadata) => {
      if (err) {
        return reject(err);
      }

      const { width: videoWidth, height: videoHeight } = metadata.streams[0];
      resolve({ videoWidth, videoHeight });
    });
  });
}; */

enum TextAnimation {
  'FADE_IN' = 'FADE_IN',
  'LEFT_TO_RIGHT' = 'LEFT_TO_RIGHT',
}

const mergeAudioFiles = async (
  audioFiles: string[],
  params: {
    audioDurations: number[];
    outputFile: string;
  },
): Promise<string> => {
  const { audioDurations, outputFile } = params;
  return await new Promise((resolve, reject) => {
    const ffmpegCommand = ffmpeg();

    // Add each audio file
    audioFiles.forEach((audioFile) => {
      ffmpegCommand.input(audioFile);
    });

    // Apply filters to ensure each audio segment is exactly 8 seconds long
    let filter = audioFiles
      .map((_, i) => {
        return `[${i}:a]apad,atrim=0:${audioDurations[i]}[a${i}]`; // Pad and trim each file to 8 seconds
      })
      .join(';');

    // Concatenate the audio files
    filter += `;${audioFiles.map((_, i) => `[a${i}]`).join('')}concat=n=${
      audioFiles.length
    }:v=0:a=1[out]`;

    // Concatenate the audio files and pad them to 8 seconds if shorter
    ffmpegCommand
      .on('start', (commandLine) => {
        logger.debug('Spawned FFmpeg with command: ' + commandLine);
      })
      .on('error', (err, stdout, stderr) => {
        logger.error('Error: ' + err.message);
        logger.error('FFmpeg stderr: ' + stderr);
        reject(err); // Reject promise on error
      })
      .on('end', () => {
        resolve(outputFile); // Resolve promise on successful completion
      })
      // Use the filter_complex to concatenate and ensure padding
      .complexFilter([filter]) // Apply the filter to pad, trim, and concatenate
      .outputOptions(['-map [out]']) // Use the output of concatenation
      .save(outputFile);
  });
};

// Function to split text into lines based on maxCharsPerLine
const splitTextIntoLines = (
  text: string,
  maxCharsPerLine: number,
): string[] => {
  const words = text.split(' ');
  const lines = [];
  let currentLine = '';

  words.forEach((word) => {
    if ((currentLine + word).length <= maxCharsPerLine) {
      currentLine += word + ' ';
    } else {
      lines.push(currentLine.trim());
      currentLine = word + ' ';
    }
  });

  if (currentLine.length > 0) {
    lines.push(currentLine.trim());
  }

  return lines;
};

// Function to calculate the appropriate font size based on container
const calculateFontSize = (
  text: string,
  containerWidth: number,
  containerHeight: number,
  maxFontSize: number,
): number => {
  let fontSize = maxFontSize;

  const testLineHeight = (): number => fontSize * 1.2;
  const testCharWidth = (): number => fontSize * 0.6; // Rough estimate: 60% of font size per char

  let textWidth, textHeight;
  do {
    const charWidthEstimate = testCharWidth();
    const maxCharsPerLine = Math.floor(containerWidth / charWidthEstimate);

    // Split the text into lines to fit the container width
    const splitText = splitTextIntoLines(text, maxCharsPerLine);
    textHeight = splitText.length * testLineHeight();

    // Estimate text width based on the longest line
    textWidth = Math.max(
      ...splitText.map((line) => line.length * charWidthEstimate),
    );

    // Reduce the font size if it exceeds the container bounds
    fontSize -= 1;
  } while (
    (textWidth > containerWidth || textHeight > containerHeight) &&
    fontSize > 10
  );

  return Math.min(fontSize, maxFontSize);
};

const drawText = (
  textProps: IVideoFrameTextProps,
  params: {
    startTime: number;
    endTime: number;
    animation?: TextAnimation;
  },
): string[] => {
  try {
    const { value, maxFontSize, fontColor = 'white', container } = textProps;
    const { startTime, endTime } = params;
    const { width, height, x, y } = container;

    // Calculate fontSize based on available space
    const fontSize = calculateFontSize(value, width, height, maxFontSize);
    const lineHeight = fontSize * 1.2;

    // Estimate max chars per line based on calculated font size
    const charWidthEstimate = fontSize * 0.6; // Rough estimate: 60% of font size
    const maxCharsPerLine = Math.floor(width / charWidthEstimate);

    // Split text into lines based on available width
    const lines = splitTextIntoLines(value, maxCharsPerLine);

    // Calculate total text height based on the number of lines
    const totalTextHeight = lines.length * lineHeight;

    // Vertically center the text within the container
    const verticalCenterY = y + (height - totalTextHeight) / 2;

    // Horizontally center the text within the container
    const horizontalCenterX = `${x} + ((${width} - text_w) / 2)`;

    const fontFile = path.join(__dirname, './fonts/Roboto-Medium.ttf'); // Use a semibold font

    // Function to handle special characters for FFmpeg
    const sanitizeText = (text: string): string => {
      return text
        .replace(/'/g, '`') // Replace single quote with backtick
        .replace(/"/g, '""') // Replace double quotes with double double-quotes
        .replace(/%/g, ' percent') // Replace % with ' percent'
        .replace(/:/g, '-') // Replace colon with hyphen
        .replace(/\\/g, '/'); // Replace backslash with forward slash
    };

    // Escape and encode lines
    const sanitizedLines = lines.map((line) => sanitizeText(line));

    let alpha = '1';
    if (params.animation === 'FADE_IN') {
      alpha = `'if(lt(t,${startTime + 1}),0,if(lt(t,${startTime + 2}),(t-${
        startTime + 1
      })/1,1))'`;
    }

    let xPosition = horizontalCenterX;
    if (params.animation === 'LEFT_TO_RIGHT') {
      xPosition = `if(lt(t\\,${
        startTime + 1
      })\\,-text_w+(t-${startTime})*(w+text_w)/2\\,${x}+(((${width}-text_w)/2)))`;
    }

    // Generate FFmpeg drawtext filters for each line
    const filters = sanitizedLines.map((line, index) => {
      const yPosition = verticalCenterY + index * lineHeight;
      return `[video]drawtext=text='${line}':x=${xPosition}:y=${yPosition}:fontsize=${fontSize}:fontcolor=${fontColor}:fontfile=${fontFile}:alpha=${alpha}:enable='between(t,${startTime},${endTime})'[video]`;
    });

    return filters;
  } catch (error) {
    logger.error('Error generating drawText:', error);
    return [];
  }
};

const overlayImageWithRoundedCorners = (
  inputImageIndex: number,
  params: {
    imageProps: IVideoFrameMediaProps;
    startTime: number;
    endTime: number;
  },
): string[] => {
  const { startTime, endTime, imageProps } = params;
  const { borderRadius, container } = imageProps;
  const { x, y, width } = container;

  // Create unique variable labels based on inputImageIndex
  const scaledImageLabel = `scaled_image_${inputImageIndex}`;
  const imageWithRoundedCornersLabel = `image_with_rounded_corners_${inputImageIndex}`;

  // Filters to scale the image and create a rounded corner mask
  const filters = [
    // Scale the image to the container dimensions
    `[${inputImageIndex}:v] scale=${width}:-1[${scaledImageLabel}]`,

    // Create a mask with rounded corners using geq expression
    /*
      format=yuva420p: 
        - Converts the image to the pixel format yuva420p, which supports an alpha (transparency) channel.
        - The format yuva420p ensures that the output includes transparency, as the alpha channel is needed for rounded corners to blend correctly with the background.
      geq=lum='p(X,Y)':
        - geq stands for "generic equation", which allows pixel-by-pixel manipulation.
        - lum='p(X,Y)': This part of the command keeps the luminance (brightness) of each pixel (p(X,Y)) intact. It doesn't modify the brightness values; it simply passes the luminance of each pixel to the output.
      a='if(...), 255':
        - This part of the command defines how the alpha (a) or transparency channel is calculated for each pixel.
        - The if expression controls whether a pixel is fully opaque (255) or fully transparent (0), based on its position within the image and whether it lies within the rounded border.
        - hypot(...) computes the Euclidean distance from the pixel to the corner, determining whether it's inside the circular area that defines the rounded corner.
        - If the distance is less than or equal to the borderRadius, the pixel is inside the corner, and the alpha is set to 255 (fully opaque).
        - If the distance is greater than the borderRadius, the pixel is outside the corner, and the alpha is set to 0 (fully transparent).

    */
    `[${scaledImageLabel}] format=yuva420p,geq=lum='p(X,Y)':a='if(gt(abs(W/2-X),W/2-${borderRadius})*gt(abs(H/2-Y),H/2-${borderRadius}),if(lte(hypot(${borderRadius}-(W/2-abs(W/2-X)),${borderRadius}-(H/2-abs(H/2-Y))),${borderRadius}),255,0),255)'[${imageWithRoundedCornersLabel}]`,

    // Overlay the image with rounded corners onto the video at the specified position (x, y)
    `[video][${imageWithRoundedCornersLabel}] overlay=${x}:${y}:enable='between(t,${startTime},${endTime})' [video]`,
  ];

  return filters;
};

const runFfmpegCommand = async (
  ffmpegCommand: ffmpeg.FfmpegCommand,
): Promise<boolean> => {
  return await new Promise((resolve, reject) => {
    ffmpegCommand
      // default is medium, values: [ultrafast, superfast, veryfast, faster, fast, medium]
      // more fast, more file size, same quality
      .outputOptions('-preset', 'ultrafast')
      // default is 23, values [18, 28]
      // 18 is high quality and 28 is low
      .outputOptions('-crf', '28')
      // Frame rate,
      .outputOptions('-r', '24')
      // auto allows FFmpeg to automatically detect and use the maximum number of threads available on the system
      .outputOptions('-threads', 'auto')
      .on('start', (commandLine) => {
        logger.debug('Spawned FFmpeg with command: ' + commandLine);
      })
      .on('end', () => {
        logger.info('runFfmpegCommand success.');
        resolve(true);
      })
      .on('error', (err) => {
        logger.error('Error occurred runFfmpegCommand: ' + err.message);
        reject(err);
      })
      .run();
  });
};

export const generateAudioUsingTemplate = async (
  template: IVideoTemplate,
): Promise<string> => {
  if (!existsSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR)) {
    mkdirSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR, { recursive: true });
  }

  const baseFileName = `${Date.now()}-${template?.video_caption?.replace(
    / /g,
    '_',
  )}`;

  // create single voiceover file
  const voiceoverPromises: Array<Promise<IScriptDuration[]>> = [];
  const voiceoverAudioPaths: string[] = [];
  template.frames.forEach((frame, index) => {
    const voiceoverAudioPath = `${TMP_BANNERBOT_AUDIO_UPLOAD_DIR}/${baseFileName}-${index}.mp3`;
    voiceoverAudioPaths.push(voiceoverAudioPath);
    voiceoverPromises.push(
      convertTextToSpeech([frame.voiceover_text], voiceoverAudioPath, {
        voice: template.voice,
      }),
    );
  });
  await Promise.all(voiceoverPromises);
  const mergedVoiceoverAudioPath = `${TMP_BANNERBOT_AUDIO_UPLOAD_DIR}/${baseFileName}-merged.mp3`;
  const audioDurationsArr = template.frames.map((item) => item.duration);
  const totalAudioDuration = audioDurationsArr.reduce(
    (accumulator, currentValue) => accumulator + currentValue,
    0,
  );
  await mergeAudioFiles(voiceoverAudioPaths, {
    audioDurations: audioDurationsArr,
    outputFile: mergedVoiceoverAudioPath,
  });

  const ffmpegCommand = ffmpeg(mergedVoiceoverAudioPath).input(
    path.join(__dirname, 'audios/a-product-demo-167264.mp3'),
  );

  ffmpegCommand.complexFilter([
    // extends the voiceover indefinitely with silence, so that ducked bg music has whole duration intact
    // also, split it into 2 copies, one for sidechain, other for mixing
    `[0:a]apad,asplit=2[voiceover_copy1][voiceover_copy2]`,

    // Audio Mixing: Use sidechaincompress to duck background music when the voiceover is active
    // here 1:a (bg_music) is main audio and voiceover_copy1 is sidechain audio
    // threshold specifies how loud the main audio has to be before the sidechain compressor alters the music track's volume.
    // ratio determines how much the main audio is reduced. Default value is 2, and min,max is 1,20
    // release=1000: Controls how long it takes for the background music to come back after the voiceover stops.
    // A longer release time keeps the background music quiet during pauses in speech.
    `[1:a][voiceover_copy1]sidechaincompress=threshold=0.03:ratio=15:release=1000[bg_music_ducked]`,

    // Increase the voiceover volume before mixing
    '[voiceover_copy2]volume=2.0[voiceover_loud]', // Increase volume by 2x

    // Mix the ducked background music with the voiceover, thus making voiceover louder
    // amix=inputs=2:weights=1 2: This specifies the weights of the two audio tracks.
    // The background music has a weight of 1, and the voiceover has a weight of 2, making the voiceover louder in the final mix.
    `[bg_music_ducked][voiceover_loud]amix=inputs=2:weights=1 1.5:duration=first:dropout_transition=3[audio]`,
  ]);

  const generatedAudioFilePath = `${TMP_BANNERBOT_AUDIO_UPLOAD_DIR}/${baseFileName}.mp3`;

  ffmpegCommand
    .map('[audio]') // Use the mixed audio stream
    .outputOptions('-shortest') // Ensure the output duration matches the shortest input
    .outputOptions(`-t ${totalAudioDuration}`)
    .output(generatedAudioFilePath);

  await runFfmpegCommand(ffmpegCommand);

  const s3Url = await uploadFileToS3(
    BANNERBOT_S3_PUBLIC_BUCKET_NAME,
    generatedAudioFilePath,
    `${S3_BANNERBOT_VIDEO_MAKER_AUDIO_UPLOAD_DIR}/${baseFileName}.mp3`,
  );
  // clean all files
  try {
    await Promise.all(
      voiceoverAudioPaths.map(async (path) => {
        await fsUnlink(path);
      }),
    );
    await fsUnlink(mergedVoiceoverAudioPath);
    await fsUnlink(generatedAudioFilePath);
  } catch (error) {
    logger.error(error);
  }
  return s3Url;
};

export const generateVideoUsingTemplate = async (
  template: IVideoTemplate,
): Promise<{
  url: string;
  caption: string;
}> => {
  // console.dir(template, { depth: null });

  if (!existsSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR)) {
    mkdirSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR, { recursive: true });
  }
  const baseFileName = `${Date.now()}-${template?.video_caption?.replace(
    / /g,
    '_',
  )}`;

  // create single voiceover file
  const audioUrl = await generateAudioUsingTemplate(template);

  // base video will always be first input and audio 2nd
  const ffmpegCommand = ffmpeg(template.base_video_url).input(audioUrl);

  // ffmpeg order of input files matter so using fallback image when both videos & images are missing
  const fallbackInputFile =
    'https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/empty-white-image-dnd.png';
  // input images / videos for each frame
  template.frames.forEach((frame) => {
    ffmpegCommand.input(frame.mediaProps?.url ?? fallbackInputFile);
  });

  // image/video filters
  const imageOrVideoFilters: string[] = [];

  // 0: base template, 1: audio, image starts from 2
  let imageInputStartSequence = 2;
  let imageStartDuration = 0;
  template.frames.forEach((frame) => {
    if (frame.mediaProps) {
      const filters = overlayImageWithRoundedCorners(imageInputStartSequence, {
        startTime: imageStartDuration,
        endTime: imageStartDuration + frame.duration,
        imageProps: frame.mediaProps,
      });
      imageOrVideoFilters.push(...filters);
      imageInputStartSequence += 1;
    }
    imageStartDuration += frame.duration;
  });

  // text filters
  const textFilters: string[] = [];
  let textStartDuration = 0;
  template.frames.forEach((frame) => {
    const filters = drawText(frame.textProps, {
      startTime: textStartDuration,
      endTime: textStartDuration + frame.duration,
    });
    textFilters.push(...filters);
    textStartDuration += frame.duration;
  });

  // process voiceover
  ffmpegCommand.complexFilter([
    `[0:v] copy [video]`, // Base video
    `[1:a] anull [audio]`, // Process the audio from input 1
    ...imageOrVideoFilters, // Apply image/video filters first
    ...textFilters, // Apply text filters on top of everything
  ]);

  const generatedVideoFilePath = `${TMP_BANNERBOT_AUDIO_UPLOAD_DIR}/${baseFileName}.mp4`;

  ffmpegCommand
    .map('[video]') // Use the processed video stream
    .map('[audio]') // Use the input audio stream
    .outputOptions('-shortest') // Ensure the output duration matches the shortest input
    .output(generatedVideoFilePath);

  await runFfmpegCommand(ffmpegCommand);

  const s3Url = await uploadFileToS3(
    BANNERBOT_S3_PUBLIC_BUCKET_NAME,
    generatedVideoFilePath,
    `${S3_BANNERBOT_VIDEO_MAKER_VIDEO_UPLOAD_DIR}/${baseFileName}.mp4`,
  );

  // clean all files
  try {
    await fsUnlink(generatedVideoFilePath);
  } catch (error) {
    logger.error(error);
  }
  return {
    url: s3Url,
    caption: template.video_caption,
  };
};

export const generateBannerBasedVideoAd = async (
  template: IBannerBasedVideoTemplate,
): Promise<{
  url: string;
  caption: string;
}> => {
  // console.dir(template, { depth: null });

  if (!existsSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR)) {
    mkdirSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR, { recursive: true });
  }
  const baseFileName = `${Date.now()}-${template?.video_caption?.replace(
    / /g,
    '_',
  )}`;

  const audioUrl = template.bg_music_url;

  // base video will always be first input and audio 2nd
  const ffmpegCommand = ffmpeg(template.base_video_url).input(audioUrl);

  // ffmpeg order of input files matter so using fallback image when both videos & images are missing
  const fallbackInputFile =
    'https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/empty-white-image-dnd.png';
  // input images / videos for each frame
  template.frames.forEach((frame) => {
    frame.mediaPropsArr?.forEach((mediaProps) => {
      ffmpegCommand.input(mediaProps?.url || fallbackInputFile);
    });
  });

  const totalVideoDuration = template.frames
    .map((item) => item.duration)
    .reduce((accumulator, currentValue) => accumulator + currentValue, 0);

  // image/video filters
  const imageOrVideoFilters: string[] = [];
  let imageStartDuration = 0;

  // 0: base template, 1: audio, image starts from 2
  let imageInputStartSequence = 2;
  template.frames.forEach((frame) => {
    frame.mediaPropsArr?.forEach((mediaProps) => {
      const filters = overlayImageWithRoundedCorners(imageInputStartSequence, {
        startTime: imageStartDuration,
        endTime: imageStartDuration + frame.duration,
        imageProps: mediaProps,
      });
      imageOrVideoFilters.push(...filters);
      imageInputStartSequence += 1;
    });
    imageStartDuration += frame.duration;
  });

  // text filters
  const textFilters: string[] = [];
  let textStartDuration = 0;
  template.frames.forEach((frame) => {
    frame.textPropsArr?.forEach((textProps, innerIndex) => {
      const filters = drawText(textProps, {
        startTime: textStartDuration,
        endTime: textStartDuration + frame.duration,

        // Animate Call out text & CTA
        animation: [
          undefined,
          TextAnimation.FADE_IN,
          TextAnimation.LEFT_TO_RIGHT,
        ][innerIndex],
      });
      textFilters.push(...filters);
    });
    textStartDuration += frame.duration;
  });

  // Merge everything
  ffmpegCommand.complexFilter([
    `[0:v] copy [video]`, // Base video
    `[1:a] anull [audio]`, // Process the audio from input 1
    ...imageOrVideoFilters, // Apply image/video filters first
    ...textFilters, // Apply text filters on top of everything
  ]);

  const generatedVideoFilePath = `${TMP_BANNERBOT_AUDIO_UPLOAD_DIR}/${baseFileName}.mp4`;

  ffmpegCommand
    .map('[video]') // Use the processed video stream
    .map('[audio]') // Use the input audio stream
    .outputOptions('-shortest') // Ensure the output duration matches the shortest input
    .outputOptions(`-t ${totalVideoDuration}`)
    .output(generatedVideoFilePath);

  await runFfmpegCommand(ffmpegCommand);

  const s3Url = await uploadFileToS3(
    BANNERBOT_S3_PUBLIC_BUCKET_NAME,
    generatedVideoFilePath,
    `${S3_BANNERBOT_VIDEO_MAKER_VIDEO_UPLOAD_DIR}/${baseFileName}.mp4`,
  );

  // clean all files
  try {
    await fsUnlink(generatedVideoFilePath);
  } catch (error) {
    logger.error(error);
  }
  return {
    url: s3Url,
    caption: template.video_caption,
  };
};

export const addBgMusicToAudio = async (params: {
  inputAudioFilePath: string;
  outputAudioFilePath: string;
}): Promise<void> => {
  const ffmpegCommand = ffmpeg(params.inputAudioFilePath).input(
    path.join(__dirname, 'audios/a-product-demo-167264.mp3'),
  );

  ffmpegCommand.complexFilter([
    // Split voiceover into 2 copies (no apad here, so duration remains original)
    `[0:a]asplit=2[voiceover_copy1][voiceover_copy2]`,

    // Pad background music to match voiceover length
    `[1:a]apad[bg_music_padded]`,

    // Sidechain compression (duck bg music when voiceover speaks)
    `[bg_music_padded][voiceover_copy1]sidechaincompress=threshold=0.03:ratio=15:release=1000[bg_music_ducked]`,

    // Boost voiceover volume
    '[voiceover_copy2]volume=2.0[voiceover_loud]',

    // Mix both tracks (voiceover duration determines final length)
    `[bg_music_ducked][voiceover_loud]amix=inputs=2:weights=1 1.5:duration=first:dropout_transition=3[audio]`,
  ]);

  ffmpegCommand
    .map('[audio]') // Use the mixed audio stream
    .output(params.outputAudioFilePath);

  await runFfmpegCommand(ffmpegCommand);
};

export const generateThumbnailFromVideo = async (params: {
  inputVideoUrlOrPath: string;
  outputImageFilePath: string;
}): Promise<void> => {
  const { inputVideoUrlOrPath, outputImageFilePath } = params;

  await new Promise<void>((resolve, reject) => {
    ffmpeg(inputVideoUrlOrPath)
      .on('end', () => { resolve(); })
      .on('error', (err) => { reject(err); })
      .screenshots({
        timestamps: ['10%'], // pick frame at 10% of duration
        filename: outputImageFilePath.split('/').pop(),
        folder: path.dirname(outputImageFilePath),
        // omit size, ffmpeg will use original video resolution
      });
  });
};
