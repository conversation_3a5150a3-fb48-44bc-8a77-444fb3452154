import { type AWSError } from 'aws-sdk';
import SES from 'aws-sdk/clients/ses';
import { type PromiseResult } from 'aws-sdk/lib/request';

// if not passed explicitly, it'll pick from env AWS_ACCESS_KEY_ID etc (belongs to bannerbot)
const ses = new SES({
  accessKeyId: process.env.GROWEASY_AWS_ACCESS_KEY_ID ?? '',
  secretAccessKey: process.env.GROWEASY_AWS_SECRET_ACCESS_KEY ?? '',
  region: process.env.AWS_REGION,
});

const DEFAULT_EMAIL_SOURCE = 'Tej from GrowEasy<<EMAIL>>';
const DEFAULT_CC_ADDRESSES = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

/**
 * @param params -
 * 
 * {
    Destination: {
      ToAddresses: ["<EMAIL>"] // Email address/addresses that you want to send your email
    },
    ConfigurationSetName: <<ConfigurationSetName>>,
    Message: {
      Body: {
        Html: {
          // HTML Format of the email
          Charset: "UTF-8",
          Data:
            "<html><body><h1>Hello  Charith</h1><p style='color:red'>Sample description</p> <p>Time 1517831318946</p></body></html>"
        },
        Text: {
          Charset: "UTF-8",
          Data: "Hello Charith Sample description time 1517831318946"
        }
      },
      Subject: {
        Charset: "UTF-8",
        Data: "Test email"
      }
    },
    Source: "<EMAIL>",
    ReplyToAddresses: ["<EMAIL>"]
* }
 */
export const sendEmail = async (
  params: SES.SendEmailRequest,
): Promise<PromiseResult<SES.SendEmailResponse, AWSError>> => {
  return await ses.sendEmail(params).promise();
};

/**
 * A template would be a json file something like-
 * {
    "Template": {
      "TemplateName": "MyTemplate",
      "SubjectPart": "Greetings, {{name}}!",
      "HtmlPart": "<h1>Hello {{name}},</h1><p>Your favorite animal is {{favoriteanimal}}.</p>",
      "TextPart": "Dear {{name}},\r\nYour favorite animal is {{favoriteanimal}}."
    }
 * }
 * It can be created using: aws ses create-template --cli-input-json <<path to template.json>>
 * 
 * params.Template would be MyTemplate
 * params.TemplateData would be something like-
 * "TemplateData": "{ \"name\":\"Alejandro\", \"favoriteanimal\": \"alligator\" }"
 */
export const sendTemplatedEmail = async (
  params: SES.SendTemplatedEmailRequest,
): Promise<PromiseResult<SES.SendTemplatedEmailResponse, AWSError>> => {
  return await ses.sendTemplatedEmail(params).promise();
};

export const sendSimpleEmail = async (params: {
  to: string[];
  htmlBody: string;
  subject: string;
}): Promise<PromiseResult<SES.SendTemplatedEmailResponse, AWSError>> => {
  const mailOptions = {
    Destination: {
      ToAddresses: params.to,
      CcAddresses: DEFAULT_CC_ADDRESSES,
    },
    Message: {
      Body: {
        Html: {
          Charset: 'UTF-8',
          Data: params.htmlBody,
        },
      },
      Subject: {
        Charset: 'UTF-8',
        Data: params.subject,
      },
    },
    Source: DEFAULT_EMAIL_SOURCE,
  };
  return await sendEmail(mailOptions);
};

export const sendSimpleTemplatedEmail = async (params: {
  to: string[];
  templateName: string;
  templateData: Record<string, unknown>;
}): Promise<PromiseResult<SES.SendTemplatedEmailResponse, AWSError>> => {
  const mailOptions = {
    Destination: {
      ToAddresses: params.to,
      CcAddresses: DEFAULT_CC_ADDRESSES,
    },
    Source: DEFAULT_EMAIL_SOURCE,
    Template: params.templateName,
    TemplateData: JSON.stringify(params.templateData),
  };
  return await sendTemplatedEmail(mailOptions);
};
