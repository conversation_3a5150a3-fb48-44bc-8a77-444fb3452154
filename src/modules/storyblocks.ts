import axios from 'axios';
import crypto from 'crypto';
import {
  type IStoryblocksItem,
  type IStoryblocksSearchResults,
} from '../types/storyblocks';

const STORYBLOCKS_PRIVATE_KEY = process.env.STORYBLOCKS_PRIVATE_KEY;
const STORYBLOCKS_PUBLIC_KEY = process.env.STORYBLOCKS_PUBLIC_KEY;

const BASE_URL = 'https://api.storyblocks.com';

function generateHMAC(expires: string, resource: string): string {
  const key = STORYBLOCKS_PRIVATE_KEY + expires;
  const hmac = crypto.createHmac('sha256', key);
  hmac.update(resource);
  return hmac.digest('hex');
}

export const getVideosFromStoryblocks = async (
  keywords: string[],
): Promise<IStoryblocksItem[]> => {
  if (!STORYBLOCKS_PUBLIC_KEY || !STORYBLOCKS_PRIVATE_KEY)
    throw new Error('missing storkblocks keys');

  const resource = '/api/v2/videos/search';
  const expires = Math.floor(Date.now() / 1000) + 100;

  const hmac = generateHMAC(expires.toString(), resource);

  const url = new URL(BASE_URL + resource);

  url.searchParams.append('APIKEY', STORYBLOCKS_PUBLIC_KEY);
  url.searchParams.append('EXPIRES', expires.toString());
  url.searchParams.append('HMAC', hmac);
  url.searchParams.append('keywords', keywords.join(','));
  url.searchParams.append('user_id', 'rohit-kumar');
  url.searchParams.append('project_id', 'test-1');
  url.searchParams.append('orientation', 'portrait');

  const { data } = await axios.get(url.toString());

  const storyblocksResponse = data as IStoryblocksSearchResults;
  return storyblocksResponse.results;
};
