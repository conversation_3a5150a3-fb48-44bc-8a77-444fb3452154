import axios, { type AxiosError } from 'axios';
import logger from './logger';

const OPEN_AI_API_KEY = process.env.OPEN_AI_API_KEY ?? '';
// const AZURE_GPT_4O_MINI_API_KEY = process.env.AZURE_GPT_4O_MINI_API_KEY ?? '';

// Docs: https://platform.openai.com/docs/api-reference/chat/create
// GPT-3.5 costing: $0.0005 / 1K tokens(input), $0.0015 / 1K tokens (output), 16K context
// GPT-4 costing: 	$0.01 / 1K tokens (input), $0.03 / 1K tokens (output), 128K context
export const getResponseFromOpenAi = async (
  prompt: string,
  params?: {
    // 0 to 1, 0 being deterministic, 1 being random
    temperature?: number;
    // gpt-4.1-mini tends to follow instructions better than 4o-mini but cost 3X
    model?: 'gpt-4o-mini' | 'gpt-4.1-mini';
  },
): Promise<any> => {
  const messages = [
    {
      content: prompt,
      role: 'system',
    },
  ];
  const data = {
    model: params?.model ?? 'gpt-4o-mini',
    messages,
    response_format: {
      // when using JSON mode, you must also instruct the model to produce JSON yourself via a system or user message
      type: 'json_object',
    },
    ...params,
  };
  const url = 'https://api.openai.com/v1/chat/completions';
  try {
    const response = await axios.post(url, data, {
      headers: {
        Authorization: `Bearer ${OPEN_AI_API_KEY}`,
      },
    });
    const content = response.data.choices?.[0]?.message?.content;
    return JSON.parse(content);
  } catch (error) {
    logger.error((error as AxiosError).response?.data);
    throw error;
  }
};

export const getResponseFromAzureOpenAi = async (
  prompt: string,
): Promise<any> => {
  return await getResponseFromOpenAi(prompt);
  /* let openAiResponse = null;
  const messages = [
    {
      content: prompt,
      role: 'system',
    },
  ];
  const data = {
    messages,
    response_format: {
      type: 'json_object',
    },
  };
  const url =
    'https://groweasy-ai-eastus.cognitiveservices.azure.com/openai/deployments/gpt-4o-mini/chat/completions?api-version=2023-03-15-preview';
  const headers = {
    'api-key': AZURE_GPT_4O_MINI_API_KEY,
  };
  try {
    const response = await axios.post(url, data, {
      headers,
      timeout: 30000, // 30 secs timeout
    });
    const content = response.data.choices?.[0]?.message?.content;
    try {
      openAiResponse = JSON.parse(content);
    } catch (error) {
      logger.info(content);
      throw error;
    }
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);

    // Something wrong with Azure deployment / infra, Fallback to OpenAI
    logger.error(
      'Error in getResponseFromAzureOpenAi, falling back to getResponseFromOpenAi',
    );
    return await getResponseFromOpenAi(prompt);
  }
  return openAiResponse; */
};

export interface IOpenAiImageGenResponse {
  created: string;
  data: Array<{
    b64_json: string;
  }>;
}

export const generateAiBannerImage = async (params: {
  prompt: string;
  aspect_ratio?: '1x1' | '9x16' | '16x9';
  quality?: 'high' | 'medium' | 'low';
}): Promise<IOpenAiImageGenResponse> => {
  const url = 'https://api.openai.com/v1/images/generations';

  // Map aspect ratio to size
  const sizeMap: Record<string, string> = {
    '1x1': '1024x1024',
    '16x9': '1536x1024', // landscape
    '9x16': '1024x1536', // portrait
  };
  const size = sizeMap[params.aspect_ratio ?? '1x1'];

  const response = await axios.post<IOpenAiImageGenResponse>(
    url,
    {
      prompt: params.prompt,
      size,
      model: 'gpt-image-1',
      quality: params.quality ?? 'medium',
    },
    {
      headers: {
        Authorization: `Bearer ${OPEN_AI_API_KEY}`,
        'Content-Type': 'application/json',
      },
    },
  );

  return response.data;
};
