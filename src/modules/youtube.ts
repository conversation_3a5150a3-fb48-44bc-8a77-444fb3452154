import { google } from 'googleapis';
import fs, { existsSync, mkdirSync } from 'fs';
import { getGoogleOauthUserAccessToken } from '../controllers/util';
import axios from 'axios';
import { TMP_ADIMAGES_UPLOAD_DIR } from '../constants';
import logger from './logger';
import { type OAuth2Client } from 'google-auth-library';

const getAuthClient = async (): Promise<OAuth2Client> => {
  const oAuth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_ADS_OAUTH_CLIENT_ID,
    process.env.GOOGLE_ADS_OAUTH_CLIENT_SECRET,
    'https://groweasy.ai',
  );

  const accessToken = await getGoogleOauthUserAccessToken();

  // Retrieve the token from your token storage (could be a database)
  oAuth2Client.setCredentials({
    access_token: accessToken,
    refresh_token: process.env.GOOGLE_ADS_REFRESH_TOKEN,
  });

  return oAuth2Client;
};

export const uploadVideoToYouTube = async (params: {
  videoUrl?: string;
  videoPath?: string;
  title: string;
  description: string;
}): Promise<string | null | undefined> => {
  // Sample videoUrls:
  // https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/videos/1730624996380-Transform_your_business_with_unprecedented_funding%21.mp4
  // https://bannerbot-public.s3.ap-south-1.amazonaws.com/groweasy-meta/videos/1728250868612-flabs-hindi.mov
  const { videoUrl, videoPath, title, description } = params;

  if (!videoUrl && !videoPath) {
    throw new Error('Either videoUrl or videoPath must be provided.');
  }

  logger.info('Uploading to YouTube:', videoUrl ?? videoPath);

  const authClient = await getAuthClient();
  const youtube = google.youtube({ version: 'v3', auth: authClient });

  let finalVideoPath = videoPath;

  // If videoUrl is provided, download the video first
  if (videoUrl) {
    if (!existsSync(TMP_ADIMAGES_UPLOAD_DIR)) {
      mkdirSync(TMP_ADIMAGES_UPLOAD_DIR, { recursive: true });
    }

    const fileName = videoUrl.split('/').pop();
    finalVideoPath = `${TMP_ADIMAGES_UPLOAD_DIR}/${fileName}`;

    const getVideoResponse = await axios({
      url: videoUrl,
      method: 'GET',
      responseType: 'stream',
    });

    const writer = fs.createWriteStream(finalVideoPath);
    getVideoResponse.data.pipe(writer);

    // Wait for the download to finish
    await new Promise<void>((resolve, reject) => {
      writer.on('finish', () => {
        resolve();
      });
      writer.on('error', reject);
    });
  }

  if (!finalVideoPath) {
    throw new Error('Failed to determine video file path.');
  }

  // Upload video to YouTube
  const uploadVideoResponse = await youtube.videos.insert({
    part: ['snippet', 'status'],
    requestBody: {
      snippet: {
        title,
        description,
      },
      status: {
        privacyStatus: 'public', // Or "private", has to be public for Google Ads assets
      },
    },
    media: {
      body: fs.createReadStream(finalVideoPath),
    },
  });

  // Clean up temporary file if it was downloaded
  if (videoUrl) {
    fs.unlinkSync(finalVideoPath);
  }

  const youtubeVideoId = uploadVideoResponse.data.id;
  logger.info('YouTube upload success:', youtubeVideoId);

  return youtubeVideoId;
};

export const checkYoutubeVideoStatus = async (
  videoId: string,
): Promise<'uploaded' | 'processed' | null> => {
  try {
    // Initialize the YouTube API client
    const authClient = await getAuthClient();
    const youtube = google.youtube({ version: 'v3', auth: authClient });

    // Call the YouTube API to fetch the video details
    const videoResponse = await youtube.videos.list({
      part: ['status'],
      id: [videoId], // The video ID to check the status of
    });

    // Check if the video exists
    if (!videoResponse.data.items || videoResponse.data.items.length === 0) {
      logger.error(`Video with ID ${videoId} not found.`);
      return null;
    }

    // Extract the video status from the response
    const videoStatus = videoResponse?.data?.items?.[0]?.status
      ?.uploadStatus as 'uploaded' | 'processed';
    logger.info(`Video status for ${videoId}: ${videoStatus}`);

    // Return the status (processing, processed, etc.)
    return videoStatus ?? null;
  } catch (error) {
    logger.error(`Error checking video status for ${videoId}:`, error);
    throw new Error('Failed to check video status');
  }
};

export const waitForVideoProcessing = async (
  videoId: string,
  timeoutMs = 10000,
  intervalMs = 2000,
): Promise<boolean> => {
  const startTime = Date.now();

  while (Date.now() - startTime < timeoutMs) {
    const status = await checkYoutubeVideoStatus(videoId);

    if (status === 'processed') {
      logger.info(`Video ${videoId} is fully processed.`);
      return true;
    }

    // If not processed yet, wait for the interval before trying again
    await new Promise((resolve) => setTimeout(resolve, intervalMs));
  }

  logger.warn(
    `Video ${videoId} did not finish processing within ${
      timeoutMs / 1000
    } seconds.`,
  );
  return false;
};
