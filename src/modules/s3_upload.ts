// TODO: clean
// bannerbot account
// /**
//  * @deprecated import function from 'modules/aws/s3/index.ts'.
//  */
// export const uploadFileToS3 = async (
//   bucketName: string,
//   filePath: string,
//   key: string,
// ): Promise<string> => {
//   return await new Promise((resolve, reject) => {
//     // Read the file from the local filesystem
//     fs.readFile(filePath, (err, data) => {
//       if (err) {
//         reject(err);
//         return;
//       }

//       // Define the S3 upload parameters
//       const params = {
//         Bucket: bucketName,
//         Key: key,
//         Body: data,
//         ContentType: mime.lookup(filePath) || 'application/octet-stream',
//         // ACL: 'public-read' // Set the file to be publicly readable, default policies will be applied when missed
//       };

//       // Upload the file to S3
//       s3.upload(params, (err: Error, data: S3.ManagedUpload.SendData) => {
//         if (err) {
//           reject(err);
//           return;
//         }

//         // Resolve with the URL of the uploaded file
//         resolve(data.Location);
//       });
//     });
//   });
// };
