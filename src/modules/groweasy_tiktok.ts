import axios, { type AxiosError } from 'axios';
import logger from './logger';
import {
  type ITiktokCampaignInsights,
  type ICampaign,
} from '../types/campaign_details';

const BASE_URL = `http://13.212.1.79/api/tiktok`;

const headers = {
  'Content-Type': 'application/json',
  'x-shared-secret': process.env.GROWEASY_TIKTOK_BE_SHARED_SECRET_TOKEN,
};

export const uploadVideoToTiktok = async (
  videoUrl: string,
): Promise<{ video_id: string }> => {
  try {
    const response = await axios.post(
      `${BASE_URL}/video/upload`,
      { video_url: videoUrl },
      { headers },
    );

    return response.data.data; // { video_id: "..." }
  } catch (error) {
    logger.error(
      'Error uploading video:',
      (error as AxiosError)?.response?.data ?? error,
    );
    throw error;
  }
};

export const uploadImageToTikTok = async (
  imageUrl: string,
): Promise<{ image_id: string; height: number; width: number }> => {
  try {
    const response = await axios.post(
      `${BASE_URL}/image/upload`,
      { image_url: imageUrl },
      { headers },
    );

    return response.data.data; // { image_id, height, width }
  } catch (error) {
    logger.error(
      'Error uploading image:',
      (error as AxiosError)?.response?.data ?? error,
    );
    throw error;
  }
};

export const launchCampaignOnTiktok = async (
  campaign: ICampaign,
): Promise<{
  campaign_id?: string;
  advertiser_id?: string;
  ad_ids?: string[];
  adgroup_id?: string;
}> => {
  try {
    const response = await axios.post(
      `${BASE_URL}/campaigns`,
      {
        campaign,
      },
      { headers },
    );

    return response.data.data;
  } catch (error) {
    logger.error(
      'Error launchCampaignOnTiktok:',
      (error as AxiosError)?.response?.data ?? error,
    );
    throw error;
  }
};

export const updateCampaignOnTiktok = async (params: {
  campaign_id: string;
  operation_status?: 'ENABLE' | 'DISABLE';
  campaign_name?: string;
  budget?: number;
}): Promise<void> => {
  try {
    const response = await axios.post(`${BASE_URL}/campaigns/update`, params, {
      headers,
    });

    return response.data.data;
  } catch (error) {
    logger.error(
      'Error updateCampaignOnTiktok:',
      (error as AxiosError)?.response?.data ?? error,
    );
    throw error;
  }
};

export const updateAdGroupOnTiktok = async (params: {
  adgroup_id: string;
  schedule_end_time?: string;
}): Promise<void> => {
  try {
    const response = await axios.post(`${BASE_URL}/adgroup/update`, params, {
      headers,
    });

    return response.data.data;
  } catch (error) {
    logger.error(
      'Error updateAdGroupOnTiktok:',
      (error as AxiosError)?.response?.data ?? error,
    );
    throw error;
  }
};

export const getCampaignInsightsFromTiktok = async (queryParams: {
  campaign_id: string;
}): Promise<ITiktokCampaignInsights> => {
  try {
    const url = new URL(`${BASE_URL}/campaign-insights`);

    Object.entries(queryParams).forEach(([key, value]) => {
      url.searchParams.set(key, value);
    });

    const response = await axios.get(url.href, { headers });

    return response.data.data?.[0];
  } catch (error) {
    logger.error(
      'Error getCampaignInsightsFromTiktok:',
      (error as AxiosError)?.response?.data ?? error,
    );
    throw error;
  }
};
