import { getFunctions } from '@remotion/lambda';
import logger from '../logger'; // Ensure you have a logger utility
import {
  getRenderProgress,
  renderMediaOnLambda,
  renderStillOnLambda,
} from '@remotion/lambda-client';
const REGION = 'ap-south-1'; // Lambda region (Mumbai)
const POLL_INTERVAL = 2000; // 2 seconds delay between progress checks

/**
 * Get Lambda function name dynamically
 */
const getLambdaFunctionName = async (): Promise<string> => {
  try {
    const functions = await getFunctions({
      region: REGION,
      compatibleOnly: true,
    });

    if (functions.length === 0) {
      throw new Error('No compatible Lambda functions found.');
    }

    return functions[0].functionName;
  } catch (error) {
    logger.error('Failed to fetch Lambda functions:', error);
    throw error;
  }
};

/**
 * Continuously check the render status until it completes or fails
 * @param renderId - The unique identifier for the render process.
 * @param bucketName - The name of the S3 bucket where the rendered media is stored.
 * @param functionName - The name of the AWS Lambda function handling the rendering.
 */
const waitForRenderCompletion = async (params: {
  renderId: string;
  bucketName: string;
  functionName: string;
}): Promise<string> => {
  const { renderId, bucketName, functionName } = params;

  logger.info(`Waiting for render completion... (ID: ${renderId})`);

  while (true) {
    await new Promise((resolve) => setTimeout(resolve, POLL_INTERVAL));

    try {
      const progress = await getRenderProgress({
        region: REGION,
        renderId,
        bucketName,
        functionName,
      });

      logger.info(`Progress: ${Math.round(progress.overallProgress * 100)}%`);

      if (progress.done) {
        logger.info(`✅ Render completed: ${progress.outputFile}`);
        logger.info(
          `Estimated billing duration: ${progress.estimatedBillingDurationInMilliseconds} ms`,
        );

        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        return progress.outputFile!;
      }

      if (progress.fatalErrorEncountered) {
        logger.error(`❌ Fatal error encountered:`, progress.errors);
        throw new Error(`Rendering failed: ${JSON.stringify(progress.errors)}`);
      }
    } catch (error) {
      logger.error(`Error checking render status:`, error);
      throw error;
    }
  }
};

/**
 * Generate a video using Remotion Lambda.
 *
 * @param serveUrl - The URL where the Remotion bundle is hosted. This can be an S3 URL or a deployed URL.
 * @param composition - The ID of the composition to render (e.g., "p3", "p4").
 * @param inputProps - The input data required for rendering (e.g., IFpVideoData).
 * @param codec - The video codec to use for rendering. Supported values: 'h264', 'h265', 'vp8', 'vp9'. Default is 'h264'.
 * @param framesPerLambda - The number of frames to process per Lambda invocation. Default is 20.
 */
export const generateVideoUsingRemotionLambda = async (params: {
  serveUrl: string;
  composition: string;
  inputProps: Record<string, unknown>;
  codec?: 'h264' | 'h265' | 'vp8' | 'vp9';
  framesPerLambda?: number;
}): Promise<{ videoUrl: string; renderId: string; bucketName: string }> => {
  const {
    serveUrl,
    composition,
    inputProps,
    codec = 'h264',
    framesPerLambda = 20,
  } = params;

  try {
    const functionName = await getLambdaFunctionName();

    logger.info(
      `Starting video rendering with Lambda function: ${functionName}`,
    );

    const { renderId, bucketName } = await renderMediaOnLambda({
      region: REGION,
      functionName,
      serveUrl,
      composition,
      inputProps,
      codec,
      imageFormat: 'jpeg',
      maxRetries: 1,
      framesPerLambda,
      privacy: 'no-acl',
    });

    logger.info(`Render started: ID = ${renderId}, Bucket = ${bucketName}`);

    // Wait for the rendering to complete
    const videoUrl = await waitForRenderCompletion({
      renderId,
      bucketName,
      functionName,
    });

    return { videoUrl, renderId, bucketName };
  } catch (error) {
    logger.error('Error generating video using Lambda:', error);
    throw error;
  }
};

/**
 * Generate thumbnail using Remotion Lambda
 */
export const generateThumbnailUsingRemotionLambda = async (params: {
  serveUrl: string;
  composition: string;
  inputProps: Record<string, unknown>;
  frame: number;
  imageFormat?: 'png' | 'jpeg';
}): Promise<{
  thumbnailUrl: string;
}> => {
  const {
    serveUrl,
    composition,
    inputProps,
    frame,
    imageFormat = 'png',
  } = params;

  try {
    const functionName = await getLambdaFunctionName();

    logger.info(
      `Starting thumbnail rendering with Lambda function: ${functionName}`,
    );

    const { estimatedPrice, url } = await renderStillOnLambda({
      region: REGION,
      functionName,
      serveUrl,
      composition,
      inputProps,
      imageFormat,
      maxRetries: 1,
      privacy: 'no-acl',
      envVariables: {},
      frame,
    });

    logger.info(`Thumbnail generated: ${url}`);

    logger.info(
      `Estimated billing accrued so far: ${estimatedPrice.accruedSoFar}`,
    );

    return {
      thumbnailUrl: url,
    };
  } catch (error) {
    logger.error('Error generating thumbnail using Lambda:', error);
    throw error;
  }
};
