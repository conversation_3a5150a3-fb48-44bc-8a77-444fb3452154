import axios, { type AxiosError } from 'axios';
import logger from '../logger';
import { type IWaMessageToBeSentPayload } from './types';

const INTERAKT_BASE_URL = 'https://amped-express.interakt.ai/api/v17.0';

// in Autotme Software Meta business
const GROWEASY_WA_SALES_AGENT_WABA_ID = '787927533558877';
const GROWEASY_WA_SALES_AGENT_WHATSAPP_ID = '669165422952915';

const interaktAccessToken = process.env.INTERAKT_API_KEY;

const getInteraktHeaders = (wabaId: string): Record<string, string> => {
  return {
    'x-access-token': interaktAccessToken ?? '',
    'x-waba-id': wabaId,
    'Content-Type': 'application/json',
  };
};

// https://documenter.getpostman.com/view/14760594/2sA2r9X4Kb#6c240fb9-9cc6-4db5-baf3-b5de5fb67417
export const sendWhatsappMessage = async (
  params: {
    whatsappId?: string;
    wabaId?: string;
  },
  data: IWaMessageToBeSentPayload,
): Promise<string> => {
  const {
    whatsappId = GROWEASY_WA_SALES_AGENT_WHATSAPP_ID,
    wabaId = GROWEASY_WA_SALES_AGENT_WABA_ID,
  } = params;

  try {
    const url = `${INTERAKT_BASE_URL}/${whatsappId}/messages`;
    const response = await axios({
      method: 'post',
      url: url.toString(),
      data,
      headers: getInteraktHeaders(wabaId),
    });
    const messageId = response.data?.messages?.[0]?.id;
    logger.info(`Interakt: Message sent to ${data.to} with id ${messageId}`);
    return messageId;
  } catch (error) {
    logger.error((error as AxiosError).response?.data);
    return '';
  }
};
