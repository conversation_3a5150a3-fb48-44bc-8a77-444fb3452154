export enum WaMessageType {
  text = 'text',
  reaction = 'reaction',
  image = 'image',
  location = 'location',
  contacts = 'contacts',
  sticker = 'sticker',
  interactive = 'interactive',
  template = 'template',
  button = 'button',
}

export interface IWaMessageCtaUrlAction {
  name: 'cta_url';
  parameters: {
    display_text: string;
    url: string;
  };
}

export interface IWaMessageButtonsAction {
  buttons: Array<{
    type: 'reply';
    reply: {
      id: string;
      title: string;
    };
  }>;
}

export interface IWaMessageDropdownAction {
  button: string;
  sections: Array<{
    rows: Array<{
      description: string;
      id: string;
      title: string;
    }>;
  }>;
}

export interface IWaMessageToBeSentPayload {
  messaging_product: 'whatsapp';
  type: WaMessageType;
  to?: string;
  text?: {
    body?: string;
  };
  interactive?: {
    type: 'cta_url' | 'button' | 'list';
    header?: {
      type: 'text';
      text: string;
    };
    body: {
      text?: string;
    };
    action?:
      | IWaMessageCtaUrlAction
      | IWaMessageButtonsAction
      | IWaMessageDropdownAction;
  };
  template?: {
    name: string;
    language: {
      code: string;
    };
    components?: Array<{
      type: 'body';
      parameters: Array<{
        type: 'text';
        text: string;
        parameter_name?: string;
      }>;
    }>;
  };
}
