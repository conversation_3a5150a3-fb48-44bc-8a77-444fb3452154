import axios from 'axios';
import logger from './logger';

const BASE_URL = 'https://api.interakt.ai/v1';

const API_KEY = process.env.INTERAKT_API_KEY ?? '';
const SOLUTION_ID = '1029150822664030';

// wrapper of 3 APIs: webhook subscription, phone registration and credit line attachment
export const initiateWabaOnboarding = async (params: {
  waba_id: string;
}): Promise<object> => {
  try {
    logger.info('initiating WABA onboarding', params);
    const response = await axios.post(
      `${BASE_URL}/organizations/tp-signup/`,
      {
        entry: [
          {
            changes: [
              {
                value: {
                  event: 'PARTNER_ADDED',
                  waba_info: {
                    waba_id: params.waba_id,
                    solution_id: SOLUTION_ID,
                  },
                },
              },
            ],
          },
        ],
        object: 'tech_partner',
      },
      {
        headers: {
          Authorization: API_KEY,
          'Content-Type': 'application/json',
        },
      },
    );

    return response.data;
  } catch (error) {
    logger.error('Error initiating WABA onboarding:', error);
    throw error;
  }
};
