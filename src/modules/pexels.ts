import axios, { type AxiosError, type AxiosPromise } from 'axios';
import {
  type IPexelsImageData,
  type IImageData,
  type IPexelsVideoData,
} from '../types/stock_image';
import logger from './logger';

const PEXELS_API_KEY = process.env.PEXELS_API_KEY ?? '';

// API Docs: https://www.pexels.com/api/documentation/
// query is comma separate string e.g. "Smartphone, Personal Assistant, AI Technology"
export const getImagesFromPexels = async (
  query: string,
): Promise<IImageData[]> => {
  const images: IImageData[] = [];
  const url = 'https://api.pexels.com/v1/search';
  // call API for each of the keywords
  const queryKeywordsArr = query.split(',');
  const promises: AxiosPromise[] = [];
  queryKeywordsArr.forEach((keyword) => {
    promises.push(
      axios.get(url, {
        params: {
          page: '1',
          query: keyword,
          per_page: queryKeywordsArr.length > 2 ? '7' : '10',
        },
        headers: {
          Authorization: PEXELS_API_KEY,
        },
      }),
    );
  });
  const responses = await Promise.all(promises);
  const combinedResults: IPexelsImageData[] = [];
  responses.forEach((response) => {
    combinedResults.push(...(response.data?.photos ?? []));
  });
  combinedResults.forEach((item) => {
    const imageUrl = new URL(item.src.original);
    const thumbnailUrl = new URL(item.src.original);
    const authorProfileUrl = new URL(item.photographer_url);
    const imageQueryParams: Record<string, string> = {
      crop: 'edges',
      fit: 'crop',
      h: '1024',
      w: '1024',
    };
    const thumbnailQueryParams: Record<string, string> = {
      crop: 'edges',
      fit: 'crop',
      h: '100',
      w: '100',
    };
    const authorProfileQueryParams: Record<string, string> = {
      utm_source: 'xyz.bannerbot',
      utm_medium: 'referral',
    };
    Object.keys(imageQueryParams).forEach((key) => {
      imageUrl.searchParams.set(key, imageQueryParams[key]);
    });
    Object.keys(thumbnailQueryParams).forEach((key) => {
      thumbnailUrl.searchParams.set(key, thumbnailQueryParams[key]);
    });
    Object.keys(authorProfileQueryParams).forEach((key) => {
      authorProfileUrl.searchParams.set(key, authorProfileQueryParams[key]);
    });
    const imageData: IImageData = {
      id: `${item.id}`,
      url: imageUrl.toString(),
      thumbnail_url: thumbnailUrl.toString(),
      author_name: item.photographer,
      author_profile_url: authorProfileUrl.toString(),
      likes: 0,
      pexels_url:
        'https://www.pexels.com/?utm_source=xyz.bannerbot&utm_medium=referral',
      download_location: '',
      alt_description: item.alt,
      width: item.width,
      height: item.height,
    };
    images.push(imageData);
  });
  return images;
};

export const getVideosFromPexels = async (queryParams: {
  queries: string[]; // comma separated keywords
  orientation?: 'portrait' | 'landscape' | 'square';
  limit?: string;
  page?: number;
}): Promise<IPexelsVideoData[]> => {
  try {
    const url = 'https://api.pexels.com/videos/search';
    // call API for each of the keywords
    const queryKeywordsArr = queryParams.queries;
    const promises: AxiosPromise[] = [];
    queryKeywordsArr.forEach((keyword) => {
      promises.push(
        axios.get(url, {
          params: {
            page: queryParams.page ?? 1,
            query: keyword,
            per_page: queryParams.limit ?? '20',
            orientation: queryParams.orientation ?? 'portrait',
          },
          headers: {
            Authorization: PEXELS_API_KEY,
          },
        }),
      );
    });
    const responses = await Promise.all(promises);
    const combinedResults: IPexelsVideoData[] = [];
    responses.forEach((response) => {
      combinedResults.push(...(response.data?.videos ?? []));
    });
    return combinedResults;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    return [];
  }
};
