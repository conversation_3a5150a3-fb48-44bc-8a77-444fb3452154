import { z } from 'zod';

// FP = Ffmp<PERSON>, Puppeteer, now we have switched to remotion
export enum FpVideoTemplates {
  p1 = 'p1',
  p2 = 'p2',
  p3 = 'p3',
  p4 = 'p4',
  p5 = 'p5',
  p6 = 'p6',
  p7 = 'p7',
  p8 = 'p8',
  p9 = 'p9',
  p10 = 'p10',
  p11 = 'p11',
}

const IBaseVideoDataSchema = z.object({
  video_caption: z.string(),
  fps: z.number().min(1).max(60),
  duration_in_sec: z.number().optional(),
  size: z.enum(['portrait', 'square', 'landscape']),
  width: z.number().positive(),
  height: z.number().positive(),

  branding: z
    .object({
      website: z.string().optional(),
      tagline: z.string().optional(),
      mobile: z.string().optional(),
      brand_name: z.string(),
      logo: z
        .object({
          url: z.string().url(),
          width: z.number().positive(),
          height: z.number().positive(),
        })
        .optional(),
    })
    .optional(),

  base_assets: z
    .array(
      z.object({
        type: z.enum(['image', 'video']),
        url: z.string().url(),
        video_duration: z
          .number()
          .optional()
          .describe('video duration for type=video'),
      }),
    )
    .optional(),
});

export const IFpVideoDataV1Schema = IBaseVideoDataSchema.extend({
  template_id: z.enum(['p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9']),

  base_audio: z.object({
    url: z.string().url(),
  }),

  scenes: z.array(
    z.object({
      scene_id: z.number(),
      assets: z
        .array(
          z.object({
            type: z.enum(['image', 'video']),
            url: z.string().url(),
          }),
        )
        .optional(),
      texts: z
        .array(
          z.object({
            value: z.string(),
          }),
        )
        .optional(),
    }),
  ),
});

// V2 Schema
export const IFpVideoDataV2Schema = IBaseVideoDataSchema.extend({
  template_id: z.enum(['p10', 'p11']),
  base_audio: z.object({
    url: z.string().url(),
    subtitle: z.array(
      z.object({ text: z.string(), start: z.number(), end: z.number() }),
    ),
  }),
  scenes: z
    .array(
      z.object({
        scene_id: z.number(),
        assets: z
          .array(
            z.object({
              type: z.enum(['image', 'video']),
              url: z.string().url(),
            }),
          )
          .optional(),
        texts: z
          .array(
            z.object({
              value: z.string(),
            }),
          )
          .optional(),
        start: z.number(),
        end: z.number(),
      }),
    )
    .optional(),
});

export const IFpVideoDataScheme = z.union([
  IFpVideoDataV1Schema,
  IFpVideoDataV2Schema,
]);

export type IFpVideoData = z.infer<typeof IFpVideoDataScheme>;
export type IFpVideoDataV2 = z.infer<typeof IFpVideoDataV2Schema>;
