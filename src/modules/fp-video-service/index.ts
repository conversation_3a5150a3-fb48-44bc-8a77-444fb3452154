import fs from 'fs';
import path from 'path';
import puppeteer from 'puppeteer';
import logger from '../logger';
import { P1_SAMPLE_DATA } from './templates/p1';
import { P2_SAMPLE_DATA } from './templates/p2';
import { FpVideoTemplates, type IFpVideoData } from './types';
import { stitchFramesToVideo } from '../fc-video-service/utils';
import { P3_SAMPLE_DATA } from './templates/p3';
import { P4_SAMPLE_DATA } from './templates/p4';
import { TEMPLATES_CONFIG } from '../../constants/fp_videos';
import { P5_SAMPLE_DATA } from './templates/p5';
import { P6_SAMPLE_DATA } from './templates/p6';
import { P7_SAMPLE_DATA } from './templates/p7';
import { P8_SAMPLE_DATA } from './templates/p8';
import { P9_SAMPLE_DATA } from './templates/p9';
import { P10_SAMPLE_DATA } from './templates/p10';
import { P11_SAMPLE_DATA } from './templates/p11';

declare global {
  interface Window {
    gsap: any; // GSAP library
    init: (videoData: string) => void; // Function to initialize animation
    pageTimeline: {
      duration: () => number; // Gets the total animation duration
      asyncSeek: (time: number, suppressEvents?: boolean) => Promise<void>; // Seeks to a specific time in the animation
    };
    preloadVideoToTime: (duration: number) => Promise<{
      success: boolean;
      error?: {
        message: string;
        name: string;
      };
    }>;
  }
}

// export const generateVideoUsingRemotionLambda = async (params: {
//   parentTmpDir: string;
//   data: IFpVideoData;
//   videoCaption: string;
// }): Promise<{
//   videoFilePath: string;
//   videoFileName: string;
//   thumbnailFilePath: string;
//   thumbnailFileName: string;
// }> => {
//   const { parentTmpDir, data: videoData, videoCaption } = params;
//   const region = 'ap-south-1';

//   const [{ functionName }] = await getFunctions({
//     region,
//     compatibleOnly: true,
//   });

//   const inputProps = { videoData }; // since this is the type used for inputProps in composition
//   // server url for the remotion renderer
//   const serveUrl = 'https://designeasy.ai/fp-videos-v2/index.html';
//   // const videoTemplateUrl = "http://localhost:3000/fp-videos-v2/index.html";

//   const { renderId, bucketName } = await renderMediaOnLambda({
//     functionName,
//     inputProps,
//     region,
//     serveUrl: serveUrl,
//     codec: 'h264',
//     composition: videoData.template_id,
//   });

//   while (true) {
//     const progress = await getRenderProgress({
//       bucketName,
//       functionName,
//       region,
//       renderId,
//     });

//     if (progress.done) {
//       console.log(progress.outputFile);
//       break;
//     }
//     if (progress.fatalErrorEncountered) {
//       console.log(progress.errors.filter((e) => e.isFatal));
//       break;
//     }
//     console.log(progress.overallProgress);

//     await new Promise((resolve) => {
//       setTimeout(() => resolve, 1000);
//     });
//   }

//   return {
//     thumbnailFileName: '',
//     thumbnailFilePath: '',
//     videoFileName: '',
//     videoFilePath: '',
//   };
// };

// export const generateVideoUsingRemotion = async (params: {
//   parentTmpDir: string;
//   data: IFpVideoData;
//   videoCaption: string;
// }): Promise<{
//   videoFilePath: string;
//   videoFileName: string;
//   thumbnailFilePath: string;
//   thumbnailFileName: string;
// }> => {
//   const { parentTmpDir, data: videoData, videoCaption } = params;

//   // Create temporary directories
//   await fs.promises.mkdir(parentTmpDir, { recursive: true }); // Parent directory
//   const outputDir = path.join(parentTmpDir, 'out'); // Generated video and thumbnail will be saved here
//   await fs.promises.mkdir(outputDir, { recursive: true });

//   // server url for the remotion renderer
//   const serverUrl = 'https://designeasy.ai/fp-videos-v2/index.html';
//   // const videoTemplateUrl = "http://localhost:3000/fp-videos-v2/index.html";

//   const inputProps = { videoData }; // since this is the type used for inputProps in composition

//   // first select the composition for which the video is going to be rendered
//   const composition = await selectComposition({
//     serveUrl: serverUrl,
//     id: videoData.template_id,
//     inputProps,
//   });

//   const videoFileName = `${Date.now()}-${videoCaption.replace(/ /g, '_')}.mp4`;
//   const videoFilePath = path.join(outputDir, videoFileName);
//   const thumbnailFileName = `${Date.now()}-thumbnail-${videoCaption.replace(
//     / /g,
//     '_',
//   )}.jpg`;
//   const thumbnailFilePath = path.join(outputDir, thumbnailFileName);

//   logger.info(`Rendering Video for ${videoData.video_caption}`);
//   logger.info('Video will be saved at:', videoFilePath);

//   await renderMedia({
//     composition,
//     serveUrl: serverUrl,
//     codec: 'h264',
//     outputLocation: videoFilePath,
//     inputProps,
//   });

//   await renderStill({
//     composition,
//     serveUrl: serverUrl,
//     output: thumbnailFilePath,
//     frame:
//       TEMPLATES_CONFIG[videoData.template_id].thumbnailTimeInSec *
//       videoData.fps,
//     inputProps,
//   });

//   logger.info('Video and Thumbnail generated');

//   return {
//     videoFileName,
//     videoFilePath,
//     thumbnailFileName,
//     thumbnailFilePath,
//   };
// };

export const generateVideoUsingPuppeteer = async (params: {
  parentTmpDir: string;
  data: IFpVideoData;
  videoCaption: string;
}): Promise<{
  videoFilePath: string;
  videoFileName: string;
  thumbnailFilePath: string;
  thumbnailFileName: string;
}> => {
  const { parentTmpDir, data: videoData, videoCaption } = params;

  // Create temporary directories
  await fs.promises.mkdir(parentTmpDir, { recursive: true }); // Parent directory
  const outputDir = path.join(parentTmpDir, 'out'); // Generated video and thumbnail will be saved here
  await fs.promises.mkdir(outputDir, { recursive: true });
  const framesDir = path.join(parentTmpDir, 'frames'); // All frames will be stored here
  await fs.promises.mkdir(framesDir, { recursive: true });

  // Load the video template URL in a headless browser using Puppeteer
  const videoTemplateUrl = `https://designeasy.ai/fp-videos/${videoData.template_id}`;
  // const videoTemplateUrl = `http://localhost:3000/fp-videos/${videoData.template_id}`;
  let totalDuration = 0; // will be calculated from animation
  let totalFrames = 0; // will be calculated later

  logger.info(`Launching puppeteer for ${videoData.video_caption}`);
  const browser = await puppeteer.launch({
    headless: true,
    defaultViewport: { width: videoData.width, height: videoData.height },
    args: [
      '--disable-gpu=false', // Enable GPU acceleration
      '--disable-dev-shm-usage', // Prevent Chrome from using /dev/shm
      '--no-sandbox', // Run without sandbox (unsafe, but faster)
      '--disable-software-rasterizer', // Use GPU instead of software rendering
      '--disable-setuid-sandbox', // Disable setuid sandbox for containerized environments
    ],
  });

  const page = await browser.newPage();

  try {
    logger.info(`Navigating to ${videoTemplateUrl}`);
    // Navigate to the video template URL
    await page.goto(videoTemplateUrl, { waitUntil: 'networkidle2' });

    // Ensure animations are loaded
    await page.waitForSelector('#container');

    await page.evaluate(async () => {
      await new Promise<void>((resolve) => {
        setTimeout(() => {
          resolve();
        }, 1000);
      });
    });

    // Start the animation and pause it
    await page.evaluate((videoData: IFpVideoData) => {
      window.gsap.globalTimeline.pause();
      window.init(JSON.stringify(videoData));
      // return await new Promise((resolve) => {
      //   const checkGsap = setInterval(() => {
      //     if (window.gsap?.globalTimeline && window.init) {
      //       clearInterval(checkGsap);
      //       window.init(JSON.stringify(videoData));
      //       window.gsap.globalTimeline.pause();
      //       resolve(true);
      //     }
      //   }, 100);
      // });
    }, videoData);

    // Get the total duration of the animation
    totalDuration = await page.evaluate(() => window.pageTimeline.duration());

    // Capture frames
    totalFrames = videoData.fps * totalDuration; // Total frames = FPS * duration
    const interval = totalDuration / totalFrames;

    const hasPreloadFunction = await page.evaluate(() => {
      return typeof window.preloadVideoToTime === 'function';
    });

    if (hasPreloadFunction) {
      const result = await page.evaluate(async (totalDuration) => {
        return await window.preloadVideoToTime(totalDuration);
      }, totalDuration);

      if (!result.success) {
        throw new Error(
          result.error?.message ?? 'Error in preloading the video',
        );
      }
    }

    logger.info('Starting frame capture...');

    for (let i = 0; i < totalFrames; i++) {
      const time = i * interval;

      // Logging only 100th frames, to avoid too many logs
      if (i % 100 === 0) {
        logger.info('Capturing frame at time:', time);
      }

      // Seek to the specific point in the animation
      await page.evaluate(async (t: number) => {
        await window.pageTimeline.asyncSeek(t, false);
      }, time);

      // Capture a screenshot of the current frame
      await page.screenshot({
        path: path.join(
          framesDir,
          `frame-${String(i).padStart(4, '0')}.jpeg`,
        ) as `${string}.jpeg`,
        type: 'jpeg',
        quality: 80,
        optimizeForSpeed: true,
        fullPage: true,
      });

      // Wait for the next frame
      // await new Promise((resolve) => setTimeout(resolve, interval * 1000));
    }

    logger.info(`Frame capture completed for ${videoData.video_caption}`);
  } catch (error) {
    console.error('Error during frame capture:', error);
    throw error;
  } finally {
    await browser.close();
  }

  // Generate the video file name and path
  const videoFileName = `${Date.now()}-${videoCaption.replace(/ /g, '_')}.mp4`;
  const videoFilePath = path.join(outputDir, videoFileName);
  const thumbnailFileName = `${Date.now()}-thumbnail-${videoCaption.replace(
    / /g,
    '_',
  )}.jpg`;
  const thumbnailFilePath = path.join(
    framesDir,
    `frame-${String(
      TEMPLATES_CONFIG[videoData.template_id].thumbnailTimeInSec *
        videoData.fps,
    ).padStart(4, '0')}.jpg`,
  );
  logger.info('Video will be saved at:', videoFilePath);

  // Stitch frames into a video using FFmpeg
  const baseAudioUrl = videoData.base_audio?.url;

  await stitchFramesToVideo({
    framesFilepath: `${framesDir}/frame-%04d.jpg`,
    soundtrackUrl: baseAudioUrl,
    outputFilepath: videoFilePath,
    duration: totalDuration,
    frameRate: videoData.fps,
  });

  logger.info(`Stitched ${totalFrames} frames to video.`);

  return {
    videoFileName,
    videoFilePath,
    thumbnailFileName,
    thumbnailFilePath,
  };
};

export const getSampleVideoData = (
  templateId: FpVideoTemplates,
): IFpVideoData | null => {
  switch (templateId) {
    case FpVideoTemplates.p1: {
      return P1_SAMPLE_DATA;
    }
    case FpVideoTemplates.p2: {
      return P2_SAMPLE_DATA;
    }
    case FpVideoTemplates.p3: {
      return P3_SAMPLE_DATA;
    }
    case FpVideoTemplates.p4: {
      return P4_SAMPLE_DATA;
    }
    case FpVideoTemplates.p5: {
      return P5_SAMPLE_DATA;
    }
    case FpVideoTemplates.p6: {
      return P6_SAMPLE_DATA;
    }
    case FpVideoTemplates.p7: {
      return P7_SAMPLE_DATA;
    }
    case FpVideoTemplates.p8: {
      return P8_SAMPLE_DATA;
    }
    case FpVideoTemplates.p9: {
      return P9_SAMPLE_DATA;
    }
    case FpVideoTemplates.p10: {
      return P10_SAMPLE_DATA;
    }
    case FpVideoTemplates.p11: {
      return P11_SAMPLE_DATA;
    }
    default: {
      // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
      const _exhaustiveCheck: never = templateId;
      return null;
    }
  }
};

// TODO: clean, use the new technique of remotion
/*
  Temp files paths: 
    - Prefix: /tmp/groweasy-be/fp-videos/<uid-timestamp>
    - Output directory: /out
    - Frames directory: /frames (frame-0000.png, frame-0001.png etc)
*/
// export const createVideo = async (params: {
//   uid: string; // to create unique tmp folders
//   templateData: IFpVideoData;
// }): Promise<{
//   s3_url: string;
//   thumbnail_s3_url: string;
// }> => {
//   const { uid, templateData } = params;
//   const videoCaption = templateData.video_caption;

//   logger.info(`createVideo for ${uid} with caption ${videoCaption}`);

//   // create parent tmp folder
//   const parentTmpDir = `${TMP_FP_VIDEOS_DIR}/${uid}-${Date.now()}`;

//   let videoFilePath = '';
//   const videoFileName = '';
//   let thumbnailFilePath = '';
//   const thumbnailFileName = '';
//   let s3Url = '';
//   let thumbnailS3Url = '';

//   try {
//     ({ videoFilePath, thumbnailFilePath } =
//       // await generateVideoUsingRemotion({
//       //   parentTmpDir,
//       //   data: templateData,
//       //   videoCaption,
//       // }));
//       await generateVideoUsingPuppeteer({
//         parentTmpDir,
//         data: templateData,
//         videoCaption,
//       }));

//     if (videoFilePath) {
//       s3Url = await uploadFileToS3(
//         GROWEASY_S3_PUBLIC_BUCKET_NAME,
//         videoFilePath,
//         `${S3_FP_VIDEOS_DIR}/${videoFileName}`,
//       );
//     }

//     if (thumbnailFilePath) {
//       thumbnailS3Url = await uploadFileToS3(
//         GROWEASY_S3_PUBLIC_BUCKET_NAME,
//         thumbnailFilePath,
//         `${S3_FP_VIDEOS_THUMBNAIL_DIR}/${thumbnailFileName}`,
//       );
//     }

//     // clean tmp directory
//     await fs.promises.rm(parentTmpDir, { recursive: true });

//     return {
//       s3_url: s3Url,
//       thumbnail_s3_url: thumbnailS3Url,
//     };
//   } catch (error) {
//     logger.error(error);
//     // clean tmp directory
//     await fs.promises.rm(parentTmpDir, { recursive: true });

//     throw error;
//   }
// };
