import axios, { type AxiosError } from 'axios';
import { type IImageData } from '../types/stock_image';
import logger from './logger';
import { GROWEASY_CLOUDFLARE_URL } from '../constants';
import { getUpdatedUnsplashImageUrl } from '../utils';

const FREEPIK_API_KEY = process.env.FREEPIK_API_KEY ?? '';

interface IFreepikImageData {
  id: number;
  title: string;
  url: string;
  filename: string;
  image: {
    type: string;
    orientation: string;
    source: {
      key: string;
      url: string;
      size: string;
    };
  };
  author: {
    id: number;
    name: string;
    avatar: string;
    slug: string;
  };
  active: boolean;
  stats: {
    downloads: number;
    likes: number;
  };
}

// API Docs: https://docs.freepik.com/
// query is image description
export const getImagesFromFreepik = async (
  query: string,
  limit = '15',
  orientation: 'square' | 'portrait' | 'landscape' = 'square',
): Promise<IImageData[]> => {
  const images: IImageData[] = [];
  const url = 'https://api.freepik.com/v1/resources';
  try {
    const response = await axios.get(url, {
      headers: {
        'Accept-Language': 'en-GB',
        Accept: 'application/json',
        'Content-Type': 'application/json',
        'X-Freepik-API-Key': FREEPIK_API_KEY,
      },
      params: {
        locale: 'en-GB',
        page: '1',
        limit,
        order: 'relevance',
        term: query,
        // 'filters[orientation][landscape]': '1',
        // 'filters[orientation][portrait]': '1',
        // 'filters[orientation][square]': '1',
        [`filters[orientation][${orientation}]`]: '1',
        // 'filters[content_type][vector]': '0',
        // 'filters[people][number]': '1',
        // 'filters[people][include]': '1',
        // Filters results to only include resources of type photo. Set to 1 to enable, 0 to disable.
        'filters[content_type][photo]': '1',
        'filters[ai-generated][excluded]': '1',
      },
    });
    if (response.data?.data) {
      const data = response.data.data;
      data.forEach((item: IFreepikImageData) => {
        // Freepik transformations add watermark hence using our own
        const imageCfTransformations = `quality=100,fit=cover`;
        const imageUrl = `${GROWEASY_CLOUDFLARE_URL}/${imageCfTransformations}/${item.image?.source?.url}`;

        const imageSizeArr = item.image?.source?.size?.split('x');
        let imageWidth = 0;
        let imageHeight = 0;
        if (imageSizeArr?.length === 2) {
          imageWidth = parseInt(imageSizeArr[0]);
          imageHeight = parseInt(imageSizeArr[1]);
        }

        const thumbnailUrl = getUpdatedUnsplashImageUrl(
          item.image?.source?.url,
          {
            width: 100,
            height: 100,
          },
        );
        images.push({
          id: item.id.toString(),
          url: imageUrl,
          thumbnail_url: thumbnailUrl,
          author_name: item.author.name,
          author_profile_url: item.author.avatar,
          alt_description: item.title,
          unsplash_url: '',
          download_location: '',
          likes: item.stats.likes,
          width: imageWidth,
          height: imageHeight,
        });
      });
    }
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
  }
  return images;
};
