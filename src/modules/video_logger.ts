import { DEV } from '../constants';

import bunyan from 'bunyan';

const streams: bunyan.Stream[] = [
  {
    level: DEV ? bunyan.DEBUG : bunyan.INFO,
    stream: process.stdout,
  },
];

if (DEV) {
  streams.push({
    level: bunyan.INFO,
    path: './video_process.log',
  });
}

/**
 * Log levels- fatal(60), error(50), warn(40), info(30), debug(20), trace(10)
 * Setting a logger instance (or one of its streams) to a particular level
 * implies that all log records at that level and above are logged.
 * E.g. a logger set to level "info" will log records at level info
 * and above (warn, error, fatal).
 */
const videoLogger = bunyan.createLogger({
  name: 'groweasy-videos-be',
  streams,
  serializers: bunyan.stdSerializers,
  src: true,
  fields: {
    category: 'video',
  },
});

export default videoLogger;
