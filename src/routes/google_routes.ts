import express, { type Request, type Response } from 'express';
import { getGenericErrorResponse } from '../utils';
import { type AxiosError } from 'axios';
import {
  generateTextAssets,
  getCampaignInsights,
  getLocationSearch,
  generateAdAssets,
  uploadMediaAssets,
  getMediaAssetsDetails,
  getCampaignLeads,
  populateLeadFormContent,
  populateSearchKeywordsSuggestions,
  getCampaignInsightsV2,
  generateKeywordIdeas,
  createCustomConversionAction,
  uploadVideoToYoutubeFromUrl,
} from '../controllers/google/google_controller';
import { type IGoogleAdsError, type IApiRequest } from '../types';
import ValidationError from '../utils/validation_error';
import uploadBanner from '../modules/multer_upload_banner';
import logger from '../modules/logger';
import { uploadAssetToS3 } from '../controllers/util';
import { type ICampaign } from '../types/campaign_details';
import {
  createPMaxCampaign,
  uploadAdVideo,
} from '../controllers/google/pmax_controller';

const router = express.Router();

router.post('/media-assets', (req: IApiRequest, res: Response) => {
  const data = req.body;
  if (!data.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }
  uploadMediaAssets(data, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        ((error as AxiosError)?.response?.data as { error: IGoogleAdsError })
          ?.error?.details?.[0]?.errors?.[0]?.message ?? error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.post('/text-assets', (req: IApiRequest, res: Response) => {
  if (!req.query?.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing in query param',
      },
    });
  }
  generateTextAssets(req.query as { campaign_id: string }, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        ((error as AxiosError)?.response?.data as { error: IGoogleAdsError })
          ?.error?.details?.[0]?.errors?.[0]?.message ?? error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.post('/launch', (req: IApiRequest, res: Response) => {
  const data = req.body;
  if (!data.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }
  createPMaxCampaign(data, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        ((error as AxiosError)?.response?.data as { error: IGoogleAdsError })
          ?.error?.details?.[0]?.errors?.[0]?.message ?? error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.get('/insights', (req: IApiRequest, res: Response) => {
  const queryParams = req.query;

  const { campaign_id: campaignId } = queryParams;
  if (!campaignId) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }

  getCampaignInsights(
    {
      campaignId: campaignId as string,
    },
    req.user,
  )
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        (
          (error as AxiosError)?.response?.data as Array<{
            error: IGoogleAdsError;
          }>
        )?.[0]?.error?.details?.[0]?.errors?.[0]?.message ?? error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.get('/v2/insights', (req: IApiRequest, res: Response) => {
  const queryParams = req.query;

  const { campaign_id: campaignId } = queryParams;
  if (!campaignId) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }

  getCampaignInsightsV2(
    {
      campaignId: campaignId as string,
    },
    req.user,
  )
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        (
          (error as AxiosError)?.response?.data as Array<{
            error: IGoogleAdsError;
          }>
        )?.[0]?.error?.details?.[0]?.errors?.[0]?.message ?? error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.get('/location-search', (req: IApiRequest, res: Response) => {
  const queryParams = req.query as Record<string, string>;

  getLocationSearch(queryParams)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        (
          (error as AxiosError)?.response?.data as Array<{
            error: IGoogleAdsError;
          }>
        )?.[0]?.error?.details?.[0]?.errors?.[0]?.message ?? error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.post(
  '/s3-asset-url',
  uploadBanner as any,
  (req: Request, res: Response) => {
    if (!req.file) {
      return res.status(400).json({
        error: {
          message: 'File is missing',
        },
      });
    }
    uploadAssetToS3(req.file /* req.query */)
      .then((data) => {
        res.json({
          data,
        });
      })
      .catch((error: AxiosError) => {
        logger.error(error);
        const statusCode = error.response?.status ?? 500;
        res.status(statusCode);
        res.json(error.response?.data ?? getGenericErrorResponse());
      });
  },
);

router.post('/ad-assets', (req: IApiRequest, res: Response) => {
  const data = req.body as Partial<ICampaign>;
  if (!data.id) {
    return res.status(400).json({
      error: {
        message: 'Campaign data is missing',
      },
    });
  }
  generateAdAssets(data, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        ((error as AxiosError)?.response?.data as { error: IGoogleAdsError })
          ?.error?.details?.[0]?.errors?.[0]?.message ?? error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.get('/media-assets', (req: IApiRequest, res: Response) => {
  const assetResourceNames: string[] = (
    req.query.asset_resource_names as string
  ).split(',');
  if (!assetResourceNames?.length) {
    return res.status(400).json({
      error: {
        message: 'Please pass values in asset_resource_names query param',
      },
    });
  }
  getMediaAssetsDetails(assetResourceNames, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        ((error as AxiosError)?.response?.data as { error: IGoogleAdsError })
          ?.error?.details?.[0]?.errors?.[0]?.message ?? error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.get('/leads', (req: IApiRequest, res: Response) => {
  const campaignId = req.query.campaign_id as string;
  if (!campaignId) {
    return res.status(400).json({
      error: {
        message: 'Missing campaign_id in query param',
      },
    });
  }
  getCampaignLeads(campaignId, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post(
  '/populate-lead-form-content',
  (req: IApiRequest, res: Response) => {
    const campaignId = req.query.campaign_id as string;

    if (!campaignId) {
      return res.status(400).json({
        error: {
          message: 'campaign_id is missing',
        },
      });
    }

    populateLeadFormContent(campaignId, req.user)
      .then((data) => {
        res.json({
          data,
        });
      })
      .catch((error: Error) => {
        const errorResponse = getGenericErrorResponse();
        errorResponse.error.message = error.message;
        if (error instanceof ValidationError) {
          res.status(error.statusCode);
        } else {
          res.status(500);
        }
        res.json(errorResponse);
      });
  },
);

router.post(
  '/populate-search-keywords-suggestions',
  (req: IApiRequest, res: Response) => {
    const campaignId = req.query.campaign_id as string;

    if (!campaignId) {
      return res.status(400).json({
        error: {
          message: 'campaign_id is missing',
        },
      });
    }

    populateSearchKeywordsSuggestions(campaignId, req.user)
      .then((data) => {
        res.json({
          data,
        });
      })
      .catch((error: Error) => {
        const errorResponse = getGenericErrorResponse();
        errorResponse.error.message = error.message;
        if (error instanceof ValidationError) {
          res.status(error.statusCode);
        } else {
          res.status(500);
        }
        res.json(errorResponse);
      });
  },
);

router.post('/keyword-ideas', (req: IApiRequest, res: Response) => {
  const data = req.body;
  if (!data.seed_keywords) {
    return res.status(400).json({
      error: {
        message: 'seed_keywords is missing',
      },
    });
  }
  generateKeywordIdeas(data)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        ((error as AxiosError)?.response?.data as { error: IGoogleAdsError })
          ?.error?.details?.[0]?.errors?.[0]?.message ?? error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.post(
  '/advideos',
  uploadBanner as any,
  (req: IApiRequest, res: Response) => {
    if (!req.file) {
      return res.status(400).json({
        error: {
          message: 'File is missing',
        },
      });
    }
    if (!req.query.campaign_id) {
      return res.status(400).json({
        error: {
          message: 'campaign_id is missing in query params',
        },
      });
    }
    uploadAdVideo(req.file, req.query, req.user)
      .then((data) => {
        res.json({
          data,
        });
      })
      .catch((error: AxiosError) => {
        logger.error(error);
        const statusCode = error.response?.status ?? 500;
        res.status(statusCode);
        res.json(error.response?.data ?? getGenericErrorResponse());
      });
  },
);

router.post('/custom-conversion-action', (req: IApiRequest, res: Response) => {
  const campaignId = req.query.campaign_id as string;

  if (!campaignId) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }

  createCustomConversionAction(campaignId, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        ((error as AxiosError)?.response?.data as { error: IGoogleAdsError })
          ?.error?.details?.[0]?.errors?.[0]?.message ?? error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.post('/advideos-v2', (req: IApiRequest, res: Response) => {
  const data = req.body;
  if (!data.file_url) {
    return res.status(400).json({
      error: {
        message: 'file_url is missing',
      },
    });
  }
  if (!req.query?.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing in query params',
      },
    });
  }
  uploadVideoToYoutubeFromUrl(
    {
      ...data,
      ...req.query,
    },
    req.user,
  )
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

export default router;
