import express, { type Request, type Response } from 'express';
import crypto from 'crypto';
import type Strip<PERSON> from 'stripe';
import { getGenericErrorResponse } from '../utils';
import {
  storeLeadsFromWebhook,
  triggerNotificationsForNewLeads,
} from '../controllers/db_controller';
import logger from '../modules/logger';
import {
  type IGoogleWebhookLeadEventBody,
  type IMetaWebhookLeadEventBody,
} from '../types/campaign_details';
import { compareSignatures } from '../controllers/util';
import {
  GrowEasyPaymentSource,
  type IXenditWebhookEventBody,
  type IRazorpayWebhookEventBody,
} from '../types/payments_invoices';
import {
  verifyRazorpayPayment,
  verifyStripePayment,
  verifyXenditPayment,
} from '../controllers/payment_controller';
import ValidationError from '../utils/validation_error';
import { processWhatsappPayloadFromWebhook } from '../controllers/whatsapp/whatsapp_controller';
import { type IMetaWebhookWhatsappEventBody } from '../types/whatsapp';
import { getCtwaLeadDetails } from '../controllers/ctwa_controller';
import { constructStripeEvent } from '../modules/stripe';
import { handleCheckoutSessionCompleted } from '../controllers/bannerbot/bannerbot_controller';

const router = express.Router();

const appSecret = process.env.GROWEASY_APP_SECRET ?? '';
const leadsWebhookVerificationToken =
  process.env.LEADS_WEBHOOK_VERIFICATION_TOKEN;
const xenditWebhookVerificationToken =
  process.env.XENDIT_WEBHOOK_VERIFICATION_TOKEN;

router.get('/ping', (req: Request, res: Response) => {
  res.json({
    succes: true,
    message: 'pong',
  });
});

// https://developers.facebook.com/docs/graph-api/webhooks/getting-started#configure-webhooks-product
router.get('/meta/leads-webhook', (req: Request, res: Response) => {
  const queryParams = req.query;
  if (
    queryParams['hub.mode'] === 'subscribe' &&
    queryParams['hub.verify_token'] === leadsWebhookVerificationToken
  ) {
    return res.send(queryParams['hub.challenge']);
  }
  res.status(400).json({
    error: {
      message: 'Bad params',
    },
  });
});

// https://developers.facebook.com/docs/graph-api/webhooks/getting-started/webhooks-for-leadgen
// Generate a SHA256 signature using the payload and your app's App Secret.
// Compare your signature to the signature in the X-Hub-Signature-256 header (everything after sha256=)
router.post('/meta/leads-webhook', (req: Request, res: Response) => {
  const parsedBody: IMetaWebhookLeadEventBody = req.body;
  logger.info(JSON.stringify(parsedBody));
  const headers = req.headers;
  const signatureFromMeta = (headers['x-hub-signature-256'] as string)?.split(
    'sha256=',
  )?.[1];
  const hmac = crypto.createHmac('sha256', appSecret);
  hmac.update(JSON.stringify(req.rawBody));
  const generatedSignature = hmac.digest('hex');
  // todo debug signature mismatch
  console.log(
    generatedSignature,
    signatureFromMeta,
    compareSignatures(generatedSignature, signatureFromMeta),
  ); // todo
  const signatureMatched = true; // compareSignatures(generatedSignature, signatureFromMeta)
  if (signatureMatched) {
    /**
     * Sample payload-
     * 
     * {
      "entry":[
        {
          "id":"0",
          "time":1701840811,
          "changes":[
            {
              "field":"leadgen",
              "value":{
                "ad_id":"444444444",
                "form_id":"321454037347582",
                "leadgen_id":"921555476026727",
                "created_time":1701840811,
                "page_id":"444444444444",
                "adgroup_id":"44444444444"
              }
            }
          ]
        }
      ],
      "object":"page",
}
     */
    // check in Firestore if this leadgen_id is already available
    // if not, hit https://graph.facebook.com/v18.0/{lead-id}/ and save response
    storeLeadsFromWebhook(parsedBody)
      .then((response) => {
        res.json(response);

        // trigger notifications in non-blocking way
        void triggerNotificationsForNewLeads(parsedBody, response);
      })
      .catch((error: Error) => {
        logger.error(error);
        const errorResponse = getGenericErrorResponse();
        errorResponse.error.message = error.message;
        res.status(500).json(errorResponse);
      });
  } else {
    res.status(400).json({
      error: {
        message: 'Could not verify signature',
      },
    });
  }
});

router.get('/meta/ads-rules-engine-webhook', (req: Request, res: Response) => {
  const queryParams = req.query;
  if (
    queryParams['hub.mode'] === 'subscribe' &&
    queryParams['hub.verify_token'] === leadsWebhookVerificationToken
  ) {
    return res.send(queryParams['hub.challenge']);
  }
  res.status(400).json({
    error: {
      message: 'Bad params',
    },
  });
});

// https://developers.facebook.com/docs/marketing-api/ad-rules/guides/trigger-based-rules
router.post('/meta/ads-rules-engine-webhook', (req: Request, res: Response) => {
  const body: IMetaWebhookLeadEventBody = req.body;
  logger.info(JSON.stringify(body));

  /**
     * Sample payload-
     * 
     * {
        object: 'application',
        entry: [{
          id: '<APPLICATION_ID>',
          time: 1468938744,
          changes: [{
            field: 'ads_rules_engine',
            value: {
              'rule_id': 120203875000070187,
              'object_id': 5678,
              'object_type': 'ADSET',
              'trigger_type': 'STATS_CHANGE',
              'trigger_field': 'COST_PER_LINK_CLICK',
              'current_value': '15.8',
            }
          }],
        }],
      }
     */
  // check in Firestore if this leadgen_id is already available
  // if not, hit https://graph.facebook.com/v18.0/{lead-id}/ and save response
  res.json({
    success: true,
  });
});

// https://developers.google.com/google-ads/webhook/docs/implementation
router.post('/google/leads-webhook', (req: Request, res: Response) => {
  const body: IGoogleWebhookLeadEventBody = req.body;
  logger.info(JSON.stringify(body));

  /**
     * Sample payload-
     * 
     * {
      "lead_id":"Cj0KCQjwit_8BRCoARIsAIx3Rj7g-AeL6z35IWb6VYiZUygtTfwD3hDlgSGmY-XTTlK3lfV1wcuIwIAaAmMxEALw_wcB",
      "campaign_id":123456,
      "adgroup_id":0,
      "creative_id":0,
      "gcl_id":"Cj0KCQjwit_8BRCoARIsAIx3Rj7g-AeL6z35IWb6VYiZUygtTfwD3hDlgSGmY-XTTlK3lfV1wcuIwIAaAmMxEALw_wcB",
      "user_column_data": [
        {
          "column_name": "Full Name",
          "string_value":"FirstName LastName",
          "column_id": "FULL_NAME"
        },
        {
          "column_name":"User Phone",
          "string_value":"**************",
          "column_id":"PHONE_NUMBER"
        },
        {
          "column_name":"User Email",
          "string_value":"<EMAIL>",
          "column_id":"EMAIL"
        }],
      "api_version":"1.0",
      "form_id":123456789,
      "google_key":"testkey",
      "is_test":true
    }
     */
  // todo
  res.json({
    success: true,
  });
});

router.get('/meta/whatsapp-webhook', (req: Request, res: Response) => {
  const queryParams = req.query;
  if (
    queryParams['hub.mode'] === 'subscribe' &&
    queryParams['hub.verify_token'] === leadsWebhookVerificationToken
  ) {
    return res.send(queryParams['hub.challenge']);
  }
  res.status(400).json({
    error: {
      message: 'Bad params',
    },
  });
});

// https://developers.facebook.com/docs/whatsapp/cloud-api/guides/set-up-webhooks
// https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks/payload-examples/
router.post('/meta/whatsapp-webhook', (req: Request, res: Response) => {
  const body: IMetaWebhookWhatsappEventBody = req.body;
  logger.info(JSON.stringify(body));

  /**
     * Sample payload-
     * 
     * {
      "object": "whatsapp_business_account",
      "entry": [{
          "id": "WHATSAPP_BUSINESS_ACCOUNT_ID",
          "changes": [{
              "value": {
                  "messaging_product": "whatsapp",
                  "metadata": {
                      "display_phone_number": "PHONE_NUMBER",
                      "phone_number_id": "PHONE_NUMBER_ID"
                  },
                  # specific Webhooks payload            
              },
              "field": "messages"
            }]
        }]
    }
     */
  processWhatsappPayloadFromWebhook(body)
    .then((response) => {
      res.json(response);
    })
    .catch((error: Error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      res.status(500).json(errorResponse);
    });
});

// https://razorpay.com/docs/webhooks/validate-test/
router.post('/razorpay/webhook', (req: Request, res: Response) => {
  const body: IRazorpayWebhookEventBody = req.body;
  const headers = req.headers;
  logger.info(JSON.stringify(body));
  logger.info(JSON.stringify(headers));
  // check if this payment was initiated from GrowEasy and not via other sources (e.g. by sharing link)
  // todo: remove uid check after a month, it's redundant and meant only for temporary backward compatibility
  if (
    [
      GrowEasyPaymentSource.GROWEASY_AI,
      GrowEasyPaymentSource.GROWEASY_VIDEOS,
      GrowEasyPaymentSource.GROWEASY_MASTER_CLASS,
    ].includes(body?.payload?.payment?.entity?.notes?.source) ||
    body?.payload?.payment?.entity?.notes?.uid
  ) {
    // process this webhook
  } else {
    // return 200 and do not process
    logger.info(
      'Not processing this webhook since not initiated from GrowEasy App/Web',
    );
    return res.json({});
  }
  const payload = {
    razorpay_payment_id: body?.payload?.payment?.entity?.id,
    razorpay_order_id: body?.payload?.payment?.entity?.order_id,
    razorpay_signature: headers['x-razorpay-signature'] as string,
    razorpay_webhook_body: JSON.stringify(body),
    payment_source: body?.payload?.payment?.entity?.notes?.source,
  };
  verifyRazorpayPayment(payload)
    .then((data) => {
      logger.info('/razorpay/webhook success:', data);
      res.json({
        data,
      });
    })
    .catch((error) => {
      logger.error(error);
      if (error instanceof ValidationError) {
        const errorResponse = getGenericErrorResponse();
        errorResponse.error.message = error.message;
        res.status(error.statusCode);
        res.json(errorResponse);
      } else {
        const statusCode = error.response?.status ?? 500;
        res.status(statusCode);
        res.json(error.response?.data ?? getGenericErrorResponse());
      }
    });
});

router.get('/ctwa/lead-details', (req: Request, res: Response) => {
  const queryParams = req.query;

  if (!queryParams.lead_id) {
    return res.status(400).json({
      error: {
        message: 'lead_id is missing',
      },
    });
  }

  getCtwaLeadDetails(queryParams)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: ValidationError) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post(
  '/stripe/webhook',
  express.raw({ type: 'application/json' }),
  (req: Request, res: Response) => {
    const rawBody = req.rawBody as string;
    let event: Stripe.Event | null = null;

    const signature = req.headers['stripe-signature'];
    try {
      event = constructStripeEvent(rawBody, signature as string);
      logger.info(event);
    } catch (error) {
      logger.error(error);
      return res.sendStatus(400);
    }
    const paymentIntent = event.data.object as Stripe.PaymentIntent;

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        void verifyStripePayment({
          stripe_payment_intent_id: paymentIntent.id,
        });
        // Then define and call a method to handle the successful payment intent.
        // handlePaymentIntentSucceeded(paymentIntent);
        break;
      case 'payment_intent.payment_failed':
        // todo log event?
        logger.info(`Payment failed`, paymentIntent);
        break;
      case 'checkout.session.completed':
        if (
          event.data.object?.metadata &&
          'source' in event.data.object.metadata
        ) {
          if (event.data.object?.metadata.source === 'DESIGNEASY') {
            void handleCheckoutSessionCompleted(event.data.object);
          }
        }
        break;

      default:
        // Unexpected event type
        logger.info(`Unhandled event type ${event.type}.`);
    }

    // Return a 200 response to acknowledge receipt of the event
    return res.json({});
  },
);

router.post('/xendit/webhook', (req: Request, res: Response) => {
  const body: IXenditWebhookEventBody = req.body;
  const headers = req.headers;
  logger.info(JSON.stringify(body));
  logger.info(JSON.stringify(headers));
  const verificationToken = req.headers['x-callback-token'];
  if (verificationToken !== xenditWebhookVerificationToken) {
    return res.status(401).json({
      error: {
        message: 'Unauthorized webhook call',
      },
    });
  }
  void verifyXenditPayment(body);
  return res.json({});
});

router.get('/interakt/whatsapp-webhook', (req: Request, res: Response) => {
  const queryParams = req.query;
  return res.send(queryParams['hub.challenge']);
});

/**
 * {
    "event": "WABA_ONBOARDING_FAILED",
    "isv_name_token": "",
    "waba_id": "***************",
    "phone_number_id": "***************",
    "error": {
        "error": {
            "message": "(#133005) Two step verification PIN Mismatch",
            "type": "OAuthException",
            "code": 133005,
            "error_data": {
                "messaging_product": "whatsapp",
                "details": "Security PIN mismatch: Wrong PIN used. Make sure that you are using the correct PIN and try again."
            },
            "fbtrace_id": "AHXpwmx0S4nU9WVtnVgWHpA"
        }
    }
}

{
    "event": "WABA_ONBOARDED",
    "isv_name_token": "",
    "waba_id": "***************",
    "phone_number_id": "***************"
}

This hook also receives all messaging related events, same as /meta/whatsapp-webhook
but only for WABAs onboarded via Interakt, example-

{
    "object": "whatsapp_business_account",
    "entry": [
        {
          ...  
        }
    ]
}
 */
router.post('/interakt/whatsapp-webhook', (req: Request, res: Response) => {
  const body: IMetaWebhookWhatsappEventBody = req.body;
  logger.info(JSON.stringify(body));

  processWhatsappPayloadFromWebhook(body, 'INTERAKT')
    .then((response) => {
      res.json(response);
    })
    .catch((error: Error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      res.status(500).json(errorResponse);
    });
});

export default router;
