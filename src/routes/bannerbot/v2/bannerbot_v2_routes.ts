import express, { type Request, type Response } from 'express';
import { type IApiRequest } from '../../../types';
import { getGenericErrorResponse } from '../../../utils';
import ValidationError from '../../../utils/validation_error';
import {
  cancelUserSubscription,
  createOrUpdateProject,
  createOrUpdateUserProfile,
  createStripeCheckoutSession,
  generateAiAdBannerAndSaveToProject,
  getAdBanners,
  getProjects,
  getUserSubscription,
  populateKeyBenefits,
  reactivateUserSubscription,
  verifyStripeCheckout,
} from '../../../controllers/bannerbot/bannerbot_controller';
import uploadBanner from '../../../modules/multer_upload_banner';
import { uploadAssetToS3 } from '../../../controllers/util';
import { type AxiosError } from 'axios';
import logger from '../../../modules/logger';
import { getBusinessDetailsFromWebsite } from '../../../controllers/openai_controller';
import { sendWelcomeEmail } from '../../../controllers/bannerbot/email_controller';
import bannerbotAdminRoute from '../admin/bannerbot_admin_routes';
import bannerbotVideoRoutes from './bannerbot_video_routes';
import { getVideosFromPexels } from '../../../modules/pexels';
import { getImagesFromFreepik } from '../../../modules/freepik';
import { getAllSubscriptionPlans } from '../../../controllers/bannerbot/subscription_controller';
import { redeemCoupon } from '../../../controllers/bannerbot/coupon_controller';

const router = express.Router();

router.use('/admin', bannerbotAdminRoute);

router.get('/projects', (req: IApiRequest, res: Response) => {
  getProjects(req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/projects/:id', (req: IApiRequest, res: Response) => {
  const params = req.params;
  getProjects(req.user, params.id)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

// create or update projects API
router.post('/projects', (req: IApiRequest, res: Response) => {
  const body = req.body;
  createOrUpdateProject(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/populate-key-benefits', (req: IApiRequest, res: Response) => {
  const body = req.body;
  populateKeyBenefits(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post(
  '/s3-asset-url',
  uploadBanner as any,
  (req: Request, res: Response) => {
    if (!req.file) {
      return res.status(400).json({
        error: {
          message: 'File is missing',
        },
      });
    }
    uploadAssetToS3(req.file /* req.query */)
      .then((data) => {
        res.json({
          data,
        });
      })
      .catch((error: AxiosError) => {
        logger.error(error);
        const statusCode = error.response?.status ?? 500;
        res.status(statusCode);
        res.json(error.response?.data ?? getGenericErrorResponse());
      });
  },
);

router.post('/ad-banners', (req: IApiRequest, res: Response) => {
  const body = req.body;
  getAdBanners(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get(
  '/business-details-from-website',
  (req: IApiRequest, res: Response) => {
    const website = req.query.website as string;
    if (!website) {
      return res.status(400).json({
        error: {
          message: 'Missing website',
        },
      });
    }

    getBusinessDetailsFromWebsite({
      website,
    })
      .then((data) => {
        res.json({
          data,
        });
      })
      .catch((error: Error) => {
        logger.error(error);
        res.status(500);
        res.json({
          error: {
            message: error.message,
          },
        });
      });
  },
);

router.post('/send-welcome-email', (req: IApiRequest, res: Response) => {
  sendWelcomeEmail(req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/profile', (req: IApiRequest, res: Response) => {
  createOrUpdateUserProfile(req.body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/search/videos', (req: IApiRequest, res: Response) => {
  const { query, orientation } = req.query;
  if (!query) {
    return res.status(400).json({
      error: {
        message: 'Missing query',
      },
    });
  }
  if (
    orientation &&
    !['portrait', 'landscape', 'square'].includes(orientation as string)
  ) {
    return res.status(400).json({
      error: {
        message:
          'Possible values of orientation are: portrait, landscape, square',
      },
    });
  }

  getVideosFromPexels({
    queries: [query as string],
    orientation: orientation as 'portrait' | 'landscape' | 'square',
    limit: '50',
  })
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/search/images', (req: IApiRequest, res: Response) => {
  const { query } = req.query;
  if (!query) {
    return res.status(400).json({
      error: {
        message: 'Missing query',
      },
    });
  }

  getImagesFromFreepik(query as string, '50')
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/create-checkout-session', (req: IApiRequest, res: Response) => {
  const body: { price_id: string; success_url: string; cancel_url: string } =
    req.body;

  if (!body?.price_id || !body?.success_url || !body?.cancel_url) {
    return res.status(400).json({
      error: {
        message: 'Missing priceId or successUrl or cancelUrl',
      },
    });
  }

  if (!req.user) {
    return res.status(400).json({
      error: {
        message: 'Missing uid',
      },
    });
  }

  createStripeCheckoutSession(req.user, body)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/verify-checkout', (req: IApiRequest, res: Response) => {
  const body: { session_id: string } = req.body;
  if (!body?.session_id) {
    return res.status(400).json({
      error: {
        message: 'Missing sessionId',
      },
    });
  }

  verifyStripeCheckout(body.session_id)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/user-subscription', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    return res.status(400).json({
      error: {
        message: 'Missing uid',
      },
    });
  }

  getUserSubscription(req.user)
    .then((data) => {
      res.json({ data });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/cancel-user-subscription', (req, res) => {
  const body: { subscription_id: string } = req.body;

  if (!body.subscription_id) {
    return res.status(400).json({
      error: {
        message: 'Missing subscriptionId',
      },
    });
  }

  cancelUserSubscription(body.subscription_id)
    .then(() => {
      res.json({
        data: {
          message: 'subscription successfully cancelled',
        },
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/reactivate-user-subscription', (req, res) => {
  const body: { subscription_id: string } = req.body;

  if (!body.subscription_id) {
    return res.status(400).json({
      error: {
        message: 'Missing subscriptionId',
      },
    });
  }

  reactivateUserSubscription(body.subscription_id)
    .then(() => {
      res.json({
        data: {
          message: 'subscription successfully reactivated',
        },
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/subscription-plan', (req, res) => {
  const active = req.query.active as string;

  getAllSubscriptionPlans({ active })
    .then((data) => res.json({ data }))
    .catch((error) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

router.post('/generate-ai-ad-banner', (req: IApiRequest, res) => {
  const body: { project_id: string; size: 'square' | 'portrait' } = req.body;

  if (!body.project_id || !body.size) {
    return res.status(400).json({
      error: {
        message: 'Missing project_id or size',
      },
    });
  }

  if (!req.user) {
    return res.status(400).json({
      error: {
        message: 'Missing user',
      },
    });
  }

  generateAiAdBannerAndSaveToProject(body, req.user)
    .then((data) => {
      res.json({ data });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/coupons/redeem', (req: IApiRequest, res) => {
  const body: { coupon_code: string } = req.body;

  if (!body.coupon_code) {
    return res.status(400).json({
      error: {
        message: 'Missing coupon_code',
      },
    });
  }

  if (!req.user) {
    return res.status(400).json({
      error: {
        message: 'Missing user',
      },
    });
  }

  redeemCoupon(body, req.user)
    .then((data) => {
      res.json({ data });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.use(bannerbotVideoRoutes);

export default router;
