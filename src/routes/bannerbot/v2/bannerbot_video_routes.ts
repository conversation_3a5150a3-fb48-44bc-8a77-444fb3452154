import express, { type Response } from 'express';
import { type IApiRequest } from '../../../types';
import { getGenericErrorResponse } from '../../../utils';
import ValidationError from '../../../utils/validation_error';
import {
  generateFpVideo,
  getVideoData,
} from '../../../controllers/bannerbot/video/bannerbot_video_controller';
import { getVideoData as getVideoDataV2 } from '../../../controllers/bannerbot/video/bannerbot_video_v2_controller';
import {
  FpVideoTemplates,
  IFpVideoDataScheme,
} from '../../../modules/fp-video-service/types';
import logger from '../../../modules/logger';

const router = express.Router();

router.get('/video-data', (req: IApiRequest, res: Response) => {
  const { template_id: templateId, project_id: projectId } = req.query;
  if (!templateId || !projectId) {
    return res.status(400).json({
      error: {
        message: 'Missing project_id or template_id',
      },
    });
  }
  if (
    !Object.values(FpVideoTemplates).includes(templateId as FpVideoTemplates)
  ) {
    return res.status(400).json({
      error: {
        message: 'Unsupported template_id',
      },
    });
  }

  const getVideoDataFunction = ['p10', 'p11'].includes(templateId as string)
    ? getVideoDataV2
    : getVideoData;

  getVideoDataFunction(
    {
      templateId: templateId as FpVideoTemplates,
      projectId: projectId as string,
    },
    req.user,
  )
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/fp-video', (req: IApiRequest, res: Response) => {
  const { project_id: projectId, template_data: templateData } = req.body;

  if (!projectId || !templateData) {
    return res.status(400).json({
      error: {
        message: 'Missing project_id or template_data',
      },
    });
  }

  // make sure template_data follows the schema
  const templateDataParsingResult = IFpVideoDataScheme.safeParse(templateData);

  if (!templateDataParsingResult.success) {
    return res.status(400).json({
      error: {
        message: 'Invalid template_data',
      },
      issues: templateDataParsingResult.error.format(),
    });
  }

  if (!req.user?.uid) {
    return res.status(400).json({
      error: {
        message: 'Missing uid',
      },
    });
  }

  generateFpVideo(
    {
      projectId,
      templateData,
    },
    req.user,
  )
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

export default router;
