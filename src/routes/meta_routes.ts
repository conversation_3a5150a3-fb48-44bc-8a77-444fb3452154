import { type AxiosError } from 'axios';
import express, { type Request, type Response } from 'express';

import uploadBanner from '../modules/multer_upload_banner';
import {
  createPostOnFbPage,
  generatePreviews,
  getAdImages,
  getAdLeads,
  getAdPreview,
  getAdVideosDetails,
  getAudienceSearch,
  getCampaignInsights,
  getAdCreativeInsights,
  getDeliveryEstimate,
  getTargetingSearch,
  grantFbPageAccess,
  processFbLoginResult,
  uploadAdImage,
  uploadAdImagesFromSquareTemplate,
  uploadAdImageV2,
  uploadAdVideo,
  uploadAdVideoFromUrl,
  processWabaOnboardingResultAsSolutionPartner,
} from '../controllers/meta_controller';
import logger from '../modules/logger';
import { getGenericErrorResponse } from '../utils';
import {
  type IFbLoginStatusResult,
  type IApiRequest,
  type IAuthUser,
} from '../types';
import ValidationError from '../utils/validation_error';
import { type IBannerTemplate } from '../types/banner_template';

const router = express.Router();

router.get('/search', (req: Request, res: Response) => {
  const queryParams = req.query;

  getTargetingSearch(queryParams)
    .then((response) => {
      const statusCode = response.status;
      res.status(statusCode);
      // delete pagination since it exposes access_token, todo: find a better way to handle pagination
      if (response?.data?.paging) {
        delete response.data.paging;
      }
      res.json(response.data);
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

router.post('/adimages', uploadBanner as any, (req: Request, res: Response) => {
  if (!req.file) {
    return res.status(400).json({
      error: {
        message: 'File is missing',
      },
    });
  }
  uploadAdImage(req.file, req.query)
    .then((response) => {
      const statusCode = response.status;
      res.status(statusCode);
      res.json(response.data);
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

router.get('/adimages', (req: Request, res: Response) => {
  const queryParams = req.query;

  getAdImages(queryParams)
    .then((response) => {
      const statusCode = response.status;
      res.status(statusCode);
      res.json(response.data);
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

// proxy API to meta
router.get('/generatepreviews', (req: Request, res: Response) => {
  const queryParams = req.query;

  generatePreviews(queryParams)
    .then((response) => {
      const statusCode = response.status;
      res.status(statusCode);
      res.json(response.data);
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

// pass `campaign_id` in query param along with other params like `fields`
router.get('/insights', (req: Request, res: Response) => {
  const queryParams = req.query;

  if (!queryParams.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }

  getCampaignInsights(queryParams)
    .then((response) => {
      const statusCode = response.status;
      res.status(statusCode);
      res.json(response.data);
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

// pass `leadgen_form_id` in query param along with other params like `fields`
router.get('/leads', (req: IApiRequest, res: Response) => {
  const queryParams = req.query;

  if (!queryParams.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }

  getAdLeads(queryParams, req.user as IAuthUser)
    .then((response) => {
      res.json(response);
    })
    .catch((error: AxiosError | ValidationError) => {
      logger.error(error);
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      if (error instanceof ValidationError) {
        fallbackErrorResponse.error.message = error.message;
      }
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/ad-preview', (req: IApiRequest, res: Response) => {
  const campaignId = req.query.campaign_id as string;
  const adFormat = req.query.ad_format as string;

  if (!campaignId || !adFormat) {
    return res.status(400).json({
      error: {
        message: 'campaign_id or ad_format are missing',
      },
    });
  }

  getAdPreview(req.query, req.user as IAuthUser)
    .then((preview) => {
      res.json(preview);
    })
    .catch((error: AxiosError | ValidationError) => {
      logger.error(error);
      if (error instanceof ValidationError) {
        const errorResponse = getGenericErrorResponse();
        errorResponse.error.message = error.message;
        res.status(error.statusCode);
        res.json(errorResponse);
      } else {
        const statusCode = error.response?.status ?? 500;
        res.status(statusCode);
        res.json(error.response?.data ?? getGenericErrorResponse());
      }
    });
});

router.get('/targetingsearch', (req: Request, res: Response) => {
  const queryParams = req.query;

  getAudienceSearch(queryParams)
    .then((response) => {
      const statusCode = response.status;
      res.status(statusCode);
      res.json(response.data);
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

router.post('/v2/adimages', (req: IApiRequest, res: Response) => {
  const body: {
    template: IBannerTemplate;
  } = req.body;
  if (!body?.template) {
    return res.status(400).json({
      error: {
        message: 'Missing template data',
      },
    });
  }
  uploadAdImageV2(body.template, req.user, req.query)
    .then((response) => {
      const statusCode = response.status;
      res.status(statusCode);
      res.json(response.data);
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

router.post('/fb-login', (req: IApiRequest, res: Response) => {
  const body: {
    result: IFbLoginStatusResult;
  } = req.body;
  if (!body?.result) {
    return res.status(400).json({
      error: {
        message: 'Missing login result data',
      },
    });
  }
  processFbLoginResult(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      if (error instanceof ValidationError) {
        fallbackErrorResponse.error.message = error.message;
      }
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/fb-page-access', (req: IApiRequest, res: Response) => {
  const body: {
    fb_page_id: string;
  } = req.body;
  if (!body?.fb_page_id) {
    return res.status(400).json({
      error: {
        message: 'Missing fb_page_id',
      },
    });
  }
  grantFbPageAccess(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      if (error instanceof ValidationError) {
        fallbackErrorResponse.error.message = error.message;
      }
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/fb-page-create-post', (req: IApiRequest, res: Response) => {
  const body: {
    fb_page_id: string;
    url: string;
    message: string;
  } = req.body;
  if (!body?.fb_page_id || !body?.url) {
    return res.status(400).json({
      error: {
        message: 'Missing fb_page_id or url',
      },
    });
  }
  createPostOnFbPage(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      if (error instanceof ValidationError) {
        fallbackErrorResponse.error.message = error.message;
      }
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/advideos', uploadBanner as any, (req: Request, res: Response) => {
  if (!req.file) {
    return res.status(400).json({
      error: {
        message: 'File is missing',
      },
    });
  }
  uploadAdVideo(req.file, req.query)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

router.get('/advideos/details', (req: Request, res: Response) => {
  const queryParams = req.query;

  if (!queryParams.ids) {
    return res.status(400).json({
      error: {
        message: 'Missing fields: ids',
      },
    });
  }

  getAdVideosDetails(queryParams)
    .then((response) => {
      const statusCode = response.status;
      res.status(statusCode);
      res.json({
        data: response.data,
      });
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

router.post('/advideos-v2', (req: Request, res: Response) => {
  const data = req.body;
  if (!data.file_url) {
    return res.status(400).json({
      error: {
        message: 'file_url is missing',
      },
    });
  }
  uploadAdVideoFromUrl(data, req.query)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

router.get('/delivery-estimate', (req: Request, res: Response) => {
  const queryParams = req.query;

  getDeliveryEstimate(queryParams)
    .then((response) => {
      const statusCode = response.status;
      res.status(statusCode);
      res.json({
        data: response.data,
      });
    })
    .catch((error: AxiosError) => {
      const statusCode = error.response?.status ?? 500;
      if (statusCode !== 400) {
        logger.error(error);
      }
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

router.post('/v3/adimages', (req: IApiRequest, res: Response) => {
  const body: {
    template: IBannerTemplate;
  } = req.body;
  if (!body?.template) {
    return res.status(400).json({
      error: {
        message: 'Missing template data',
      },
    });
  }
  uploadAdImagesFromSquareTemplate(body.template, req.user, req.query)
    .then((responses) => {
      const statusCode = responses[0].status;
      res.status(statusCode);
      res.json(responses.map((response) => response.data));
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

// pass `meta_id` & `meta_ad_ids` (comma separated) in query param
router.get('/creative-insights', (req: Request, res: Response) => {
  const queryParams = req.query;

  if (!queryParams.meta_id || !queryParams.meta_ad_ids) {
    return res.status(400).json({
      error: {
        message: 'meta_id or meta_ad_ids is missing',
      },
    });
  }

  getAdCreativeInsights(queryParams)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError) => {
      logger.error(error);
      const statusCode = error.response?.status ?? 500;
      res.status(statusCode);
      res.json(error.response?.data ?? getGenericErrorResponse());
    });
});

router.post('/waba-onboarding', (req: IApiRequest, res: Response) => {
  const body: {
    code: string;
  } = req.body;
  if (!body?.code) {
    return res.status(400).json({
      error: {
        message: 'Missing authorization code',
      },
    });
  }
  processWabaOnboardingResultAsSolutionPartner(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      if (error instanceof ValidationError) {
        fallbackErrorResponse.error.message = error.message;
      }
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

export default router;
