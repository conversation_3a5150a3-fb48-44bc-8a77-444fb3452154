import express, { type Response } from 'express';
import { type IApiRequest } from '../types';
import { getGenericErrorResponse } from '../utils';
import ValidationError from '../utils/validation_error';
import {
  getAgentEasyChatMessages,
  getAgentEasyConversationDetailsForAuthUser,
} from '../controllers/agenteasy';

const router = express.Router();

router.get('/chat-messages', (req: IApiRequest, res: Response) => {
  if (!req.query?.chat_doc_id) {
    return res.status(400).json({
      error: {
        message: 'chat_doc_id is missing in query params',
      },
    });
  }

  getAgentEasyChatMessages(req.query, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/conversation-details', (req: IApiRequest, res: Response) => {
  if (!req.query?.chat_doc_id) {
    return res.status(400).json({
      error: {
        message: 'chat_doc_id is missing in query params',
      },
    });
  }

  getAgentEasyConversationDetailsForAuthUser(req.query, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

export default router;
