import express, { type Response } from 'express';
import { type IApiRequest } from '../types';
import {
  generateBannerBasedVideo,
  generateFcVideo,
  generateRemotionVideo,
  getBannerBasedVideoTemplate,
  getBannerBasedVideoTemplateV2,
  getRemotionVideoData,
  getRemotionVideoDataV2,
  getRemotionVideoDataV3,
} from '../controllers/video_controller';
import logger from '../modules/logger';
import { getGenericErrorResponse } from '../utils';
import ValidationError from '../utils/validation_error';
import { IP1CarouselVideoDataSchema } from '../modules/fc-video-service/templates/P1_CAROUSEL';
import { IP2OverlayVideoDataSchema } from '../modules/fc-video-service/templates/P2_OVERLAY_PROMO';
import { FcVideoTemplates } from '../modules/fc-video-service/types';
import { getVideosFromPexels } from '../modules/pexels';
import { IFpVideoDataScheme } from '../modules/fp-video-service/types';

const router = express.Router();

router.get('/banner-video-template', (req: IApiRequest, res: Response) => {
  const queryParams = req.query as Record<string, string>;
  if (!queryParams?.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'Missing campaign_id',
      },
    });
  }

  const controllerFun =
    queryParams.version === 'v2'
      ? getBannerBasedVideoTemplateV2
      : getBannerBasedVideoTemplate;

  controllerFun(
    {
      campaignId: queryParams.campaign_id,
    },
    req.user,
  )
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      res.status(500);
      res.json({
        error: {
          message: error.message,
        },
      });
    });
});

router.post('/banner-video', (req: IApiRequest, res: Response) => {
  const body = req.body;
  if (!body?.template) {
    return res.status(400).json({
      error: {
        message: 'Missing template',
      },
    });
  }

  generateBannerBasedVideo({
    template: body.template,
  })
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      res.status(500);
      res.json({
        error: {
          message: error.message,
        },
      });
    });
});

router.post('/fc-video', (req: IApiRequest, res: Response) => {
  const body = req.body;
  if (!body?.template_id || !body?.template_data || !body?.video_caption) {
    return res.status(400).json({
      error: {
        message: 'Missing template_id or template_data or video_caption',
      },
    });
  }

  // make sure template_data follows the schema
  let templateDataParsingResult;

  if (body.template_id === FcVideoTemplates.P1_CAROUSEL) {
    templateDataParsingResult = IP1CarouselVideoDataSchema.safeParse(
      body.template_data,
    );
  } else if (body.template_id === FcVideoTemplates.P2_OVERLAY_PROMO) {
    templateDataParsingResult = IP2OverlayVideoDataSchema.safeParse(
      body.template_data,
    );
  }

  if (!templateDataParsingResult) {
    return res.status(400).json({
      error: {
        message: 'This template_id is not supported',
      },
    });
  }

  if (!templateDataParsingResult.success) {
    return res.status(400).json({
      error: {
        message: 'Invalid template_data',
      },
      issues: templateDataParsingResult.error.format(),
    });
  }

  generateFcVideo(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/remotion-video-data', (req: IApiRequest, res: Response) => {
  const queryParams = req.query as Record<string, string>;
  if (!queryParams?.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'Missing campaign_id',
      },
    });
  }

  const useAiVideoGeneration = queryParams.template_id === 'p12';
  const actualTemplateId = useAiVideoGeneration
    ? 'p12'
    : queryParams.template_id;

  let videoDataPromise;

  if (actualTemplateId === 'p12') {
    videoDataPromise = getRemotionVideoDataV3(
      {
        campaignId: queryParams.campaign_id,
        templateId: 'p10',
        useStoryblocks: !!queryParams.use_story_blocks,
        useAiVideoGeneration,
      },
      req.user,
      1,
    );
  } else if (['p10', 'p11'].includes(actualTemplateId)) {
    videoDataPromise = getRemotionVideoDataV2(
      {
        campaignId: queryParams.campaign_id,
        templateId: actualTemplateId,
        useStoryblocks: !!queryParams.use_story_blocks,
        useAiVideoGeneration,
      },
      req.user,
      1,
    );
  } else {
    videoDataPromise = getRemotionVideoData(
      {
        campaignId: queryParams.campaign_id,
        templateId: actualTemplateId,
      },
      req.user,
    );
  }

  videoDataPromise
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      res.status(500);
      res.json({
        error: {
          message: error.message,
        },
      });
    });
});

router.get('/search/videos', (req: IApiRequest, res: Response) => {
  const { query, orientation } = req.query;
  if (!query) {
    return res.status(400).json({
      error: {
        message: 'Missing query',
      },
    });
  }
  if (
    orientation &&
    !['portrait', 'landscape', 'square'].includes(orientation as string)
  ) {
    return res.status(400).json({
      error: {
        message:
          'Possible values of orientation are: portrait, landscape, square',
      },
    });
  }

  getVideosFromPexels({
    queries: [query as string],
    orientation: orientation as 'portrait' | 'landscape' | 'square',
    limit: '50',
  })
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/remotion-video', (req: IApiRequest, res: Response) => {
  const { campaign_id: campaignId, template_data: templateData } = req.body;

  if (!campaignId || !templateData) {
    return res.status(400).json({
      error: {
        message: 'Missing campaign_id or template_data',
      },
    });
  }

  // make sure template_data follows the schema
  const templateDataParsingResult = IFpVideoDataScheme.safeParse(templateData);

  if (!templateDataParsingResult.success) {
    return res.status(400).json({
      error: {
        message: 'Invalid template_data',
      },
      issues: templateDataParsingResult.error.format(),
    });
  }

  // adding retry count as 1
  generateRemotionVideo(
    {
      campaignId,
      templateData,
    },
    req.user,
    1,
  )
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

export default router;
