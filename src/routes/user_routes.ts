import express, { type Response } from 'express';
import {
  contactUsEmail,
  createOrUpdateUserProfile,
  deleteAccount,
  getUserProfile,
  updateProfileInCustomClaims,
} from '../controllers/user_controller';
import logger from '../modules/logger';
import { type IApiRequest } from '../types';
import { getGenericErrorResponse } from '../utils';
import ValidationError from '../utils/validation_error';

const router = express.Router();

router.post('/profile', (req: IApiRequest, res: Response) => {
  updateProfileInCustomClaims(req.body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/v2/profile', (req: IApiRequest, res: Response) => {
  getUserProfile(req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/v2/profile', (req: IApiRequest, res: Response) => {
  createOrUpdateUserProfile(req.body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/delete-account', (req: IApiRequest, res: Response) => {
  deleteAccount(req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/contact-us', (req: IApiRequest, res: Response) => {
  contactUsEmail(req.body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

export default router;
