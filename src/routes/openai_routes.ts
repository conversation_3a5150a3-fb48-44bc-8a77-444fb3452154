import express, { type Request, type Response } from 'express';
import {
  populateDetailedTargeting,
  getAdBanners,
  getAdCopies,
  getLeadgenQuestions,
  generateLeadgenForm,
  populateAdCopies,
  populateSpecialAdCategories,
  getVideoDataV2,
  generateVideo,
  getBusinessDetailsFromWebsite,
  generateAiAdBanner,
  getAdLanguageSuggestions,
  getUspAndBannerElementDetails,
  getIdealCustomers,
} from '../controllers/openai_controller';

import logger from '../modules/logger';
import { type IApiRequest } from '../types';
import {
  type AdLanguage,
  type IBusinessDetails,
  type ITargeting,
} from '../types/campaign_details';
import { getGenericErrorResponse } from '../utils';
import ValidationError from '../utils/validation_error';
import { type IVideoTemplate } from '../types/video_template';

const router = express.Router();

router.post('/ad-banners', (req: Request, res: Response) => {
  const body: {
    business_details: IBusinessDetails;
    targeting: ITargeting;
    ad_language?: AdLanguage;
  } = req.body;
  if (!body?.business_details || !body?.targeting) {
    return res.status(400).json({
      error: {
        message: 'Missing business_details or targeting',
      },
    });
  }

  getAdBanners(body)
    .then((banners) => {
      res.json({
        data: banners,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      res.status(500);
      res.json({
        error: {
          message: error.message,
        },
      });
    });
});

router.post('/ad-copies', (req: Request, res: Response) => {
  const body: {
    business_details: IBusinessDetails;
    targeting: ITargeting;
  } = req.body;
  if (!body?.business_details || !body?.targeting) {
    return res.status(400).json({
      error: {
        message: 'Missing business_details or targeting',
      },
    });
  }

  getAdCopies(body)
    .then((banners) => {
      res.json({
        data: banners,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      res.status(500);
      res.json({
        error: {
          message: error.message,
        },
      });
    });
});

router.post(
  '/populate-detailed-targeting',
  (req: IApiRequest, res: Response) => {
    const campaignId = req.query.campaign_id as string;

    if (!campaignId) {
      return res.status(400).json({
        error: {
          message: 'campaign_id is missing',
        },
      });
    }

    populateDetailedTargeting(campaignId, req.user)
      .then((targeting) => {
        res.json({
          data: targeting,
        });
      })
      .catch((error: Error) => {
        const errorResponse = getGenericErrorResponse();
        errorResponse.error.message = error.message;
        if (error instanceof ValidationError) {
          res.status(error.statusCode);
        } else {
          res.status(500);
        }
        res.json(errorResponse);
      });
  },
);

// generate questions and return to user for review
router.post('/leadgen-questions', (req: Request, res: Response) => {
  const body: {
    business_details: IBusinessDetails;
    targeting: ITargeting;
    ad_language?: AdLanguage;
  } = req.body;
  if (!body?.business_details || !body?.targeting) {
    return res.status(400).json({
      error: {
        message: 'Missing business_details or targeting',
      },
    });
  }

  getLeadgenQuestions(body)
    .then((questions) => {
      res.json({
        data: questions,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      res.status(500);
      res.json({
        error: {
          message: error.message,
        },
      });
    });
});

// generate form along with questions in db
router.post('/generate-leadgen-form', (req: IApiRequest, res: Response) => {
  const campaignId = req.query.campaign_id as string;

  if (!campaignId) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }

  generateLeadgenForm(campaignId, req.user)
    .then((questions) => {
      res.json({
        data: questions,
      });
    })
    .catch((error: Error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/populate-ad-copies', (req: IApiRequest, res: Response) => {
  const campaignId = req.query.campaign_id as string;
  const version = (req.query.version ?? 'v1') as 'v1' | 'v2';

  if (!campaignId) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }

  populateAdCopies({ campaignId, version }, req.user)
    .then((targeting) => {
      res.json({
        data: targeting,
      });
    })
    .catch((error: Error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post(
  '/populate-special-ad-categories',
  (req: IApiRequest, res: Response) => {
    const campaignId = req.query.campaign_id as string;

    if (!campaignId) {
      return res.status(400).json({
        error: {
          message: 'campaign_id is missing',
        },
      });
    }

    populateSpecialAdCategories(campaignId, req.user)
      .then((targeting) => {
        res.json({
          data: targeting,
        });
      })
      .catch((error: Error) => {
        const errorResponse = getGenericErrorResponse();
        errorResponse.error.message = error.message;
        if (error instanceof ValidationError) {
          res.status(error.statusCode);
        } else {
          res.status(500);
        }
        res.json(errorResponse);
      });
  },
);

router.post('/video-data', (req: IApiRequest, res: Response) => {
  const body: {
    business_details: IBusinessDetails;
    targeting: ITargeting;
  } = req.body;
  if (!body?.business_details || !body?.targeting) {
    return res.status(400).json({
      error: {
        message: 'Missing business_details or targeting',
      },
    });
  }

  getVideoDataV2({
    ...body,
    google_tts: true,
    // frame_type: 'video'
  })
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      res.status(500);
      res.json({
        error: {
          message: error.message,
        },
      });
    });
});

router.post('/generate-video', (req: IApiRequest, res: Response) => {
  const body: {
    template: IVideoTemplate;
  } = req.body;
  const { template } = body;
  if (!template?.video_caption || !template?.frames?.length) {
    return res.status(400).json({
      error: {
        message: 'Invalid template file',
      },
    });
  }

  generateVideo({
    template,
  })
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      logger.error(error);
      res.status(500);
      res.json({
        error: {
          message: error.message,
        },
      });
    });
});

router.get(
  '/business-details-from-website',
  (req: IApiRequest, res: Response) => {
    const website = req.query.website as string;
    if (!website) {
      return res.status(400).json({
        error: {
          message: 'Missing website',
        },
      });
    }

    getBusinessDetailsFromWebsite({
      website,
    })
      .then((data) => {
        res.json({
          data,
        });
      })
      .catch((error: Error) => {
        logger.error(error);
        res.status(500);
        res.json({
          error: {
            message: error.message,
          },
        });
      });
  },
);

router.post('/generate-ai-ad-banner', (req: IApiRequest, res: Response) => {
  const body = req.body;

  if (!body?.campaign_id || !body?.banner_details || !body?.size) {
    return res.status(400).json({
      error: {
        message: 'Missing details campaign_id or banner_details or size',
      },
    });
  }

  generateAiAdBanner(body, req.user)
    .then((targeting) => {
      res.json({
        data: targeting,
      });
    })
    .catch((error: Error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/ad-language-suggestions', (req: IApiRequest, res: Response) => {
  const campaignId = req.query.campaign_id as string;

  if (!campaignId) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }

  getAdLanguageSuggestions(campaignId, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/usps-and-banner-elements', (req: IApiRequest, res: Response) => {
  const queryParams = req.query as Record<string, string>;
  const campaignId = queryParams.campaign_id;
  const flowType = queryParams.flow_type;

  if (!campaignId) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }
  if (!['usps', 'banners'].includes(flowType)) {
    return res.status(400).json({
      error: {
        message: 'fow_type is missing, possible values: usps/banners',
      },
    });
  }

  getUspAndBannerElementDetails(queryParams, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/ideal-customers', (req: IApiRequest, res: Response) => {
  const queryParams = req.query as Record<string, string>;
  const campaignId = queryParams.campaign_id;
  if (!campaignId) {
    return res.status(400).json({
      error: {
        message: 'campaign_id is missing',
      },
    });
  }

  getIdealCustomers(queryParams, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: Error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

export default router;
