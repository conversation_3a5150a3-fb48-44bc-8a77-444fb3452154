import express, { type NextFunction, type Response } from 'express';
import {
  createOrUpdateSelfAdAccountConfigs,
  getAdminCampaigns,
  getAdminInvoices,
  getUserProfileDetails,
  syncMetaAdsetFromCampaign,
  syncPaidOrdersToSheet,
  updateUserCampaign,
  updateUserCustomClaims,
} from '../controllers/admin_controller';
import { generateInvoices } from '../controllers/invoice_controller';
import { isAdGlobalAiAdmin, isGrowEasyAdmin } from '../controllers/util';
import { type ISelfAdAccountConfigs, type IApiRequest } from '../types';
import { getGenericErrorResponse } from '../utils';
import ValidationError from '../utils/validation_error';
import { generateAiAdBannerForAdCampaign } from '../controllers/openai_controller';
import {
  initAgentEasyChatToLead,
  storeAgentEasyDataForCampaignId,
} from '../controllers/agenteasy';
import { type IMetaLead } from '../types/leads';

const router = express.Router();

router.use((req: IApiRequest, res: Response, next: NextFunction) => {
  // make sure to further restrict access to adglobal ai admins based on campaign partner
  if (isGrowEasyAdmin(req.user) || isAdGlobalAiAdmin(req.user)) {
    next();
  } else {
    return res.status(403).json({
      error: {
        message: 'Not an Admin',
      },
    });
  }
});

router.get('/campaigns', (req: IApiRequest, res: Response) => {
  const queryParams = req.query;
  getAdminCampaigns(queryParams, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/user-profile', (req: IApiRequest, res: Response) => {
  const queryParams = req.query;
  if (!queryParams.email && !queryParams.uid) {
    return res.status(400).json({
      error: {
        message: 'email or uid is required',
      },
    });
  }
  getUserProfileDetails(queryParams)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/user-campaign', (req: IApiRequest, res: Response) => {
  const queryParams = req.query;
  const campaignId = queryParams.campaign_id as string;
  if (!campaignId) {
    return res.status(400).json({
      error: {
        message: 'Missing campaign_id',
      },
    });
  }
  updateUserCampaign(campaignId, req.body)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

/** GrowEasy admins access only, for access to AdGlobalAI admins too, put routes above */
router.use((req: IApiRequest, res: Response, next: NextFunction) => {
  if (isGrowEasyAdmin(req.user)) {
    next();
  } else {
    return res.status(403).json({
      error: {
        message: 'Not a GrowEasy Admin',
      },
    });
  }
});

router.post('/invoices', (req: IApiRequest, res: Response) => {
  const body = req.body;
  generateInvoices(body)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/invoices', (req: IApiRequest, res: Response) => {
  getAdminInvoices(req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

// if body is empty, i.e. {}, it can be used to inspect current claims
router.post('/user-custom-claims/:uid', (req: IApiRequest, res: Response) => {
  const uid = req.params.uid;
  const body = req.body;
  updateUserCustomClaims(uid, body)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/sync-paid-orders-to-sheet', (req: IApiRequest, res: Response) => {
  syncPaidOrdersToSheet()
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/self-ad-account-configs', (req: IApiRequest, res: Response) => {
  const payload = req.body as {
    configs: ISelfAdAccountConfigs;
    uid: string;
  };

  if (!payload?.uid || !payload?.configs) {
    return res.status(400).json({
      error: {
        message: 'Missing uid or configs',
      },
    });
  }

  createOrUpdateSelfAdAccountConfigs(payload)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/ai-media', (req: IApiRequest, res: Response) => {
  const payload = req.body as {
    campaign_id: string;
    media_type: 'image' | 'video'; // this is not being used for now maybe in the future
  };

  if (!payload?.campaign_id || !payload?.media_type) {
    return res.status(400).json({
      error: {
        message: 'Missing campaign_id or media_type',
      },
    });
  }

  generateAiAdBannerForAdCampaign(payload, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

// mostly for testing
router.post('/init-agenteasy-chat', (req: IApiRequest, res: Response) => {
  const payload = req.body as Partial<IMetaLead>;

  if (!payload?.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'Missing campaign_id',
      },
    });
  }

  initAgentEasyChatToLead(payload as IMetaLead)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

// for testing
router.post('/agenteasy-campaign-data', (req: IApiRequest, res: Response) => {
  const payload = req.body as Partial<IMetaLead>;

  if (!payload?.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'Missing campaign_id',
      },
    });
  }

  storeAgentEasyDataForCampaignId(payload.campaign_id)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post(
  '/campaigns/:campaign_id/adset/sync',
  (req: IApiRequest, res: Response) => {
    const campaignId = req.params.campaign_id;
    syncMetaAdsetFromCampaign(campaignId)
      .then((data) => {
        res.json({
          data,
        });
      })
      .catch((error) => {
        const errorResponse = getGenericErrorResponse();
        errorResponse.error.message = error.message;
        if (error instanceof ValidationError) {
          res.status(error.statusCode);
        } else {
          res.status(500);
        }
        res.json(errorResponse);
      });
  },
);

/** Only put GrowEasy admin routes above, for GrowEasy + AdGlobalAI, put in beginning */

export default router;
