import express, { type Response } from 'express';
import {
  getCampaigns,
  createOrUpdateCampaign,
  updateCampaignStatus,
  getCampaignDetails,
  getAllFormLeads,
  getBusinessCategories,
  getOrders,
  createOrUpdateBillingDetails,
  getBillingDetails,
  createOrderForExtendingCampaign,
  updateCampaignPostLaunch,
  getV2Campaigns,
  getFbAssignedPages,
  getAdCreditTransactions,
  getAdCreditBalance,
} from '../controllers/db_controller';
import { sendWelcomeEmail } from '../controllers/email_controller';
import {
  getInvoiceDetails,
  getInvoices,
} from '../controllers/invoice_controller';
import {
  verifyRazorpayPayment,
  verifyStripePayment,
} from '../controllers/payment_controller';
import { getOrderDetails } from '../controllers/util';
import logger from '../modules/logger';
import { type IApiRequest } from '../types';
import { getGenericErrorResponse } from '../utils';
import ValidationError from '../utils/validation_error';

const router = express.Router();

router.get('/campaigns', (req: IApiRequest, res: Response) => {
  const queryParams = req.query;
  getCampaigns(queryParams, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/v2/campaigns', (req: IApiRequest, res: Response) => {
  const queryParams = req.query;
  getV2Campaigns(queryParams, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/campaigns/:campaign_id', (req: IApiRequest, res: Response) => {
  const campaignId = req.params.campaign_id;
  getCampaignDetails(campaignId, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

// create or update campaigns API
router.post('/campaigns', (req: IApiRequest, res: Response) => {
  const body = req.body;
  createOrUpdateCampaign(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

// body: { id: WK2ct3AeKMMIKuFU2z9jREolRxr2_1699899525354, status: ACTIVE/PAUSED }
router.post('/campaigns/update-status', (req: IApiRequest, res: Response) => {
  const body = req.body;
  updateCampaignStatus(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

// /all-leads?limit=25&start_after=1702129010 where start_after is in seconds
router.get('/all-leads', (req: IApiRequest, res: Response) => {
  const queryParams = req.query;
  getAllFormLeads(queryParams, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/send-welcome-email', (req: IApiRequest, res: Response) => {
  sendWelcomeEmail(req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/business-categories', (req: IApiRequest, res: Response) => {
  getBusinessCategories()
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/verify-payment', (req: IApiRequest, res: Response) => {
  let promise = null;
  if (req.body.stripe_payment_intent_id) {
    promise = verifyStripePayment(req.body, req.user);
  } else {
    promise = verifyRazorpayPayment(req.body, req.user);
  }
  promise
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      logger.error(error);
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/orders/:order_id', (req: IApiRequest, res: Response) => {
  const orderId = req.params.order_id;
  getOrderDetails(orderId)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/orders', (req: IApiRequest, res: Response) => {
  const queryParams = req.query as Record<string, string>;
  getOrders(queryParams, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/billing-details', (req: IApiRequest, res: Response) => {
  getBillingDetails(req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/billing-details', (req: IApiRequest, res: Response) => {
  const body = req.body;
  createOrUpdateBillingDetails(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/invoices', (req: IApiRequest, res: Response) => {
  getInvoices(req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/invoices/:invoice_id', (req: IApiRequest, res: Response) => {
  const invoiceId = req.params.invoice_id;
  getInvoiceDetails(invoiceId, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/campaigns/extend', (req: IApiRequest, res: Response) => {
  const body = req.body;
  createOrderForExtendingCampaign(body, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post(
  '/campaigns/edit-post-launch',
  (req: IApiRequest, res: Response) => {
    const body = req.body;
    updateCampaignPostLaunch(body, req.user)
      .then((data) => {
        res.json({
          data,
        });
      })
      .catch((error) => {
        const errorResponse = getGenericErrorResponse();
        errorResponse.error.message = error.message;
        if (error instanceof ValidationError) {
          res.status(error.statusCode);
        } else {
          res.status(500);
        }
        res.json(errorResponse);
      });
  },
);

router.get('/fb-assigned-pages', (req: IApiRequest, res: Response) => {
  const queryParams = req.query;
  getFbAssignedPages(queryParams, req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/ad-credit/transactions', (req: IApiRequest, res: Response) => {
  getAdCreditTransactions(req.user)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.get('/ad-credit/balance', (req: IApiRequest, res: Response) => {
  getAdCreditBalance(req.user?.uid)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

export default router;
