import { type AxiosError } from 'axios';
import express, { type Request, type Response } from 'express';
import { type ITiktokApiError, type IApiRequest } from '../types';
import type ValidationError from '../utils/validation_error';
import { getGenericErrorResponse } from '../utils';
import {
  getLocationSearch,
  uploadAdVideo,
} from '../controllers/tiktok/tiktok_controller';
import uploadFile from '../modules/multer_upload_banner';
import { getCampaignInsightsFromTiktok } from '../modules/groweasy_tiktok';

const router = express.Router();

router.get('/location-search', (req: IApiRequest, res: Response) => {
  const queryParams = req.query as Record<string, string>;

  getLocationSearch(queryParams)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        ((error as AxiosError)?.response?.data as ITiktokApiError)?.message ??
        error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

router.post(
  '/video/upload',
  uploadFile as any,
  (req: Request, res: Response) => {
    if (!req.file && !req.body?.file_url) {
      return res.status(400).json({
        error: {
          message: 'Missing file or file_url',
        },
      });
    }
    uploadAdVideo({
      file: req.file,
      queryParams: req.query,
      body: req.body,
    })
      .then((data) => {
        res.json({
          data,
        });
      })
      .catch((error: AxiosError) => {
        const statusCode = error.response?.status ?? 500;
        res.status(statusCode);
        res.json(error.response?.data ?? getGenericErrorResponse());
      });
  },
);

router.get('/insights', (req: IApiRequest, res: Response) => {
  const queryParams = req.query as Record<string, string>;
  if (!queryParams.campaign_id) {
    return res.status(400).json({
      error: {
        message: 'Missing campaign_id',
      },
    });
  }
  getCampaignInsightsFromTiktok({
    campaign_id: queryParams.campaign_id,
  })
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error: AxiosError | ValidationError) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message =
        ((error as AxiosError)?.response?.data as ITiktokApiError)?.message ??
        error.message;
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as ValidationError).statusCode ??
        500;
      res.status(statusCode);
      res.json(errorResponse);
    });
});

export default router;
