import express, { type Response } from 'express';
import { type IApiRequest } from '../../types';
import { getGenericErrorResponse } from '../../utils';
import ValidationError from '../../utils/validation_error';
import { handleAgentRequest as handleLeadCalculatorAgentRequest } from '../../controllers/ai-tools/lead-calculator';
import { handleAgentRequest as handleAdCopiesGeneratorAgentRequest } from '../../controllers/ai-tools/ad-copies-generator';
const router = express.Router();

router.post('/lead-calculator', (req: IApiRequest, res: Response) => {
  const payload = req.body;

  if (!payload.session_id || !payload.message || !payload.email) {
    return res.status(400).json({
      error: {
        message: 'Missing session_id or email or message',
      },
    });
  }
  handleLeadCalculatorAgentRequest(payload)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

router.post('/ad-copies-generator', (req: IApiRequest, res: Response) => {
  const payload = req.body;

  if (!payload.session_id || !payload.message || !payload.email) {
    return res.status(400).json({
      error: {
        message: 'Missing session_id or email or message',
      },
    });
  }
  handleAdCopiesGeneratorAgentRequest(payload)
    .then((data) => {
      res.json({
        data,
      });
    })
    .catch((error) => {
      const errorResponse = getGenericErrorResponse();
      errorResponse.error.message = error.message;
      if (error instanceof ValidationError) {
        res.status(error.statusCode);
      } else {
        res.status(500);
      }
      res.json(errorResponse);
    });
});

export default router;
