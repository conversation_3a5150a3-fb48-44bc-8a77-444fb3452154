import { DEV } from '../constants';
// import { checkAndSendFinishedCampaignsReport } from '../controllers/cron_controller';
/* import {
  sendNewCtwaLeadWhatsappNoti,
  sendWaMessage as sendWaMessageToBcn,
} from '../controllers/whatsapp/bcn_wa_controller'; */
import logger from '../modules/logger';
// import { type ICtwaLead } from '../types/leads';

/* const testSendNewCtwaLeadWhatsappNoti = (): void => {
  const ctwaLead: ICtwaLead = {
    created_time: 1717321068,
    id: '9***********_120208234880220187',
    meta_id: '120208234880220187',
    campaign_id: 'czHu6QyeBzXnt0ntyyq0XcJcUJr2_1717257128841',
    wa_id: '***********',
    field_data: [
      {
        label: 'Full Name',
        name: 'question1',
        values: ['vinay cloth stores'],
      },
      {
        label: 'Phone',
        name: 'question3',
        values: ['+9***********"'],
      },
    ],
    client_uid: 'czHu6QyeBzXnt0ntyyq0XcJcUJr2',
    business_details: {
      business_category: 'Home Improvement and Renovation',
      consumer_type: 'All',
      ideal_customers: 'New home buyers,builders,real estate',
      mobile: '+91 72064 79844', // '+91 93405 54643',
      product_or_service_description: `Interior designing service
      Vastu
      Land scape
      Architecture `,
      product_or_service_offers_or_usp:
        'Home interior Office interior Flat interior On time delivery Transparent pricingExperience designers',
      website: '',
    },
  };
  void sendNewCtwaLeadWhatsappNoti(ctwaLead);
};

const testNewLeadNotiV2Template = (): void => {
  const payload = {
    name: 'groweasy-be',
    hostname: 'ip-10-0-4-144.ap-south-1.compute.internal',
    pid: 29,
    level: 30,
    messaging_product: 'whatsapp',
    to: '************', // "************",
    type: 'template',
    template: {
      name: 'new_lead_notification_v2',
      language: {
        code: 'en',
      },
      components: [
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: 'Ratul Lahiri',
            },
            {
              type: 'text',
              text: 'Healthcare and Medical Devices',
            },
            {
              type: 'text',
              text: 'Breast Cancer Surgery ',
            },
            {
              type: 'text',
              text: 'Y',
            },
            {
              type: 'text',
              text: 'NA',
            },
            {
              type: 'text',
              text: '**********',
            },
            {
              type: 'text',
              text: 'Which of the following best describes your current situation?',
            },
            {
              type: 'text',
              text: 'Seeking second opinion on breast cancer surgery',
            },
            {
              type: 'text',
              text: 'What is your preferred method of communication for receiving information or updates?',
            },
            {
              type: 'text',
              text: 'Phone call',
            },
          ],
        },
      ],
    },
  };
  void sendWaMessageToBcn(payload);
}; */

// eslint-disable-next-line
const runLocalTestsOnStart = (): void => {
  // testSendNewCtwaLeadWhatsappNoti();
  // testNewLeadNotiV2Template();
  // void checkAndSendFinishedCampaignsReport()
};

const init = (): void => {
  if (DEV) {
    logger.info(`tests.init executing runLocalTestsOnStart`);
    runLocalTestsOnStart();
  }
};

export default {
  init,
};
