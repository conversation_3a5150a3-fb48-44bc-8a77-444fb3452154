const { customAlphabet } = require('nanoid');
const fs = require('fs');

const TOTAL_NUMBER_OF_CODES = 1500;

// Code generation
const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);
const codePrefix = 'AS-';
const totalCodes = TOTAL_NUMBER_OF_CODES;
const codes = new Set();

// Generate unique codes
while (codes.size < totalCodes) {
  codes.add(`${codePrefix}${nanoid()}`);
}

const codeArray = [...codes];

// Create CSV for AppSumo submission
const csvContent = codeArray.join('\n');
fs.writeFileSync('appsumo_codes.csv', csvContent);

const firebaseAdmin = require('firebase-admin');
const { config } = require('dotenv');

config();

const BANERBOT_CERT =
  process?.env?.BANNERBOT_FIREBASE_SERVICE_ACC_PRIVATE_KEY?.replace(
    /\\n/g,
    '\n',
  );

const BANNERBOT_SERVICE_ACCOUNT = {
  type: 'service_account',
  project_id: 'banner-bot-675a8',
  private_key_id: 'e1986e9c1a1f67c2dc06a67d70b3a7aa555a1412',
  private_key: BANERBOT_CERT,
  client_email:
    '<EMAIL>',
  client_id: '105571820079715830735',
  auth_uri: 'https://accounts.google.com/o/oauth2/auth',
  token_uri: 'https://oauth2.googleapis.com/token',
  auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
  client_x509_cert_url:
    'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-jqjno%40banner-bot-675a8.iam.gserviceaccount.com',
  universe_domain: 'googleapis.com',
};

const app = firebaseAdmin.initializeApp(
  {
    credential: firebaseAdmin.credential.cert(BANNERBOT_SERVICE_ACCOUNT),
  },
  'bannerbot.xyz',
);

const db = app.firestore();

async function createCouponsInFirestore() {
  const batch = db.batch();
  const couponCollection = db.collection('couponCodes');

  const validUntil = new Date();
  validUntil.setFullYear(validUntil.getFullYear() + 1);

  codeArray.forEach((code) => {
    const docRef = couponCollection.doc();
    const couponData = {
      id: docRef.id,
      created_at: firebaseAdmin.firestore.FieldValue.serverTimestamp(),
      redeemed_at: null,
      redeemed_by: null,
      code: code,
      is_active: true,
      valid_until: firebaseAdmin.firestore.Timestamp.fromDate(validUntil),
      discount_type: 'flat', // can be percent as well
      discount_value: 99,
      metadata: {
        source: 'APP_SUMO',
      },
      description: 'Lifetime free usage to AppSumo users in just $99',
    };

    batch.set(docRef, couponData);
  });

  try {
    await batch.commit();
    console.log(`Successfully created ${totalCodes} coupon codes in Firestore`);
  } catch (error) {
    console.error('Error creating coupons:', error);
  }
}

createCouponsInFirestore().then(() => {
  console.log('successfully created coupons in firestore');
});
