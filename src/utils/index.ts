import crypto from 'crypto';
import { type Express } from 'express';
import fs from 'fs';
import util from 'util';
import path from 'path';
import mime from 'mime-types';
import { Readable } from 'stream';

import { type IGenericErrorResponse } from '../types';
import { GROWEASY_CLOUDFLARE_URL } from '../constants';
import logger from '../modules/logger';
import axios, { type AxiosError } from 'axios';
import {
  Currency,
  type ICampaignInsightDetails,
} from '../types/campaign_details';
import { type IPexelsVideoData } from '../types/stock_image';
import { type Timestamp } from 'firebase-admin/firestore';

export const deepCopy = (object: object): object => {
  return JSON.parse(JSON.stringify(object));
};

export const getGenericErrorResponse = (): IGenericErrorResponse => {
  return {
    error: {
      message: 'Something went wrong',
    },
  };
};

export const generateSha256Hash = (data: string): string => {
  // Create a SHA-256 hash instance
  const hash = crypto.createHash('sha256');

  // Update the hash instance with the data
  hash.update(data);

  // Finalize the hash and get the result in 'hex' encoding
  return hash.digest('hex');
};

export const getDateDifferenceInDays = (
  startDate: Date,
  endDate: Date,
): number => {
  // in ms
  const timeDifference = endDate.getTime() - startDate.getTime();
  // Convert the time difference from milliseconds to days
  const daysDifference = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));

  return daysDifference;
};

export const createMulterFile = async (
  filePath: string,
): Promise<Express.Multer.File> => {
  const readFile = util.promisify(fs.readFile);
  const fsStat = util.promisify(fs.stat);
  const fileContent = await readFile(filePath);
  const originalname = path.basename(filePath);
  const mimetype = mime.lookup(filePath) || 'application/octet-stream';
  const size = (await fsStat(filePath)).size;

  const multerFile: Express.Multer.File = {
    fieldname: 'file',
    originalname,
    encoding: '7bit',
    mimetype,
    size,
    buffer: fileContent,
    destination: '',
    filename: originalname,
    path: filePath,
    stream: Readable.from(fileContent),
  };

  return multerFile;
};

export const getAbsoluteUrl = (url: string, baseUrl: string): string => {
  if (!url) return '';
  try {
    return new URL(url, baseUrl).href;
  } catch (e) {
    return url;
  }
};

/**
 * Updates or adds width and height parameters in a Cloudflare image URL while preserving other parameters.
 * If width or height exist, they are replaced; otherwise, they are appended.
 */
export const getUpdatedCloudflareImageUrl = (
  url: string,
  params: {
    width: number | string;
    height: number | string;
  },
): string => {
  const { width, height } = params;

  // Check if URL contains cdn-cgi/image
  if (!url.includes('cdn-cgi/image/')) {
    return url; // Return as is if it's not a Cloudflare image URL
  }

  // Extract existing parameters
  const regex = /(cdn-cgi\/image\/)([^/]+)(\/https:\/\/)/;
  const match = url.match(regex);

  let newParams = `width=${width},height=${height}`;

  if (match) {
    const existingParams = match[2];

    // Remove existing width and height
    const updatedParams = existingParams
      .split(',')
      .filter(
        (param) => !param.startsWith('width=') && !param.startsWith('height='),
      )
      .join(',');

    // Append new width & height
    newParams = updatedParams ? `${newParams},${updatedParams}` : newParams;

    return url.replace(regex, `$1${newParams}$3`);
  }

  return url; // Fallback if no match
};

export const getUpdatedUnsplashImageUrl = (
  url: string,
  params: {
    width: number | string;
    height: number | string;
  },
): string => {
  const { width, height } = params;

  const urlObj = new URL(url);
  urlObj.searchParams.set('crop', 'edges');
  urlObj.searchParams.set('fit', 'crop');
  urlObj.searchParams.set('w', width.toString());
  urlObj.searchParams.set('h', height.toString());

  return urlObj.toString();
};

/**
 * Calculates the maximum possible target width and height for an image while maintaining
 * the given aspect ratio and ensuring dimensions do not exceed the original image size.
 */
export const getImageTransformationParams = ({
  imageWidth,
  imageHeight,
  targetAspectRatio,
}: {
  imageWidth: number;
  imageHeight: number;
  targetAspectRatio: number;
}): { targetWidth: number; targetHeight: number } => {
  let targetWidth = imageWidth;
  let targetHeight = Math.round(targetWidth / targetAspectRatio);

  if (targetHeight > imageHeight) {
    targetHeight = imageHeight;
    targetWidth = Math.round(targetHeight * targetAspectRatio);
  }

  return { targetWidth, targetHeight };
};

/**
 * Converts any URL (S3, Pexels, Unsplash, CF) to Cloudflare URL or dynamic crop supported URL
 */
export const getTransformedImageUrl = (
  url: string,
  params: {
    width: number | string;
    height: number | string;
  },
): string => {
  const { width, height } = params;

  if (
    url.startsWith('https://bannerbot-public.s3.ap-south-1.amazonaws.com') ||
    url.startsWith('https://groweasy-public.s3.ap-south-1.amazonaws.com')
  ) {
    // S3 URL → Convert to Cloudflare
    return `${GROWEASY_CLOUDFLARE_URL}/width=${width},height=${height},quality=100,fit=cover/${url}`;
  } else if (url.includes('/cdn-cgi/image')) {
    // Already a Cloudflare URL → Update dimensions
    return getUpdatedCloudflareImageUrl(url, { width, height });
  } else if (url.includes('unsplash.com') || url.includes('pexels.com')) {
    // Unsplash or Pexels → Apply dimensions using their URL params
    return getUpdatedUnsplashImageUrl(url, { width, height });
  } else {
    // Fallback: Return original URL if no matching condition
    logger.warn(`Unknown URL format: ${url}`);
    return url;
  }
};

export const saveFileToDisk = async ({
  fileName,
  url,
  directory,
}: {
  fileName: string;
  url: string; // either a remote URL or base64 data
  directory: string;
}): Promise<string> => {
  try {
    if (!fileName || !url) {
      throw new Error('Missing required parameters: fileName and url');
    }

    // Ensure directory exists
    if (!fs.existsSync(directory)) {
      fs.mkdirSync(directory, { recursive: true });
    }

    const filePath = path.join(directory, fileName);

    // Check if input is base64
    const isBase64 =
      url.startsWith('data:image') || /^[A-Za-z0-9+/=]+$/.test(url);

    if (isBase64) {
      const base64Data = url.replace(/^data:image\/\w+;base64,/, '');
      const buffer = Buffer.from(base64Data, 'base64');
      fs.writeFileSync(filePath, buffer);
    } else {
      // Download file from URL
      const response = await axios({
        method: 'get',
        url,
        responseType: 'stream',
        timeout: 30000,
      });

      const writer = fs.createWriteStream(filePath);
      response.data.pipe(writer);

      await new Promise<void>((resolve, reject) => {
        writer.on('finish', () => {
          resolve();
        });
        writer.on('error', reject);
      });
    }

    logger.info(`File saved successfully: ${filePath}`);
    return filePath;
  } catch (error) {
    const err = error as AxiosError | Error;
    logger.error(`Failed to save file: ${err.message}`, {
      fileName,
      url: url?.slice?.(0, 100), // log only first 100 chars
      error: err.stack,
    });
    throw new Error(`File save failed: ${err.message}`);
  }
};

export const getCurrencySymbol = (currency: string): string => {
  switch (currency.toUpperCase()) {
    case 'USD':
      return '$';
    case 'INR':
      return '₹';
    case 'IDR':
      return 'Rp';
    case 'PHP':
      return '₱';
    case 'THB':
      return '฿';
    case 'VND':
      return '₫';
    case 'MYR':
      return 'RM';
    default:
      return '';
  }
};

const CURRENCY_TO_LOCALE_MAP: Record<Currency, string> = {
  [Currency.INR]: 'en-IN', // English (India)
  [Currency.USD]: 'en-US', // English (United States)
  [Currency.IDR]: 'id-ID', // Indonesian (Indonesia)
  [Currency.PHP]: 'en-PH', // English (Philippines)
  [Currency.THB]: 'th-TH', // Thai (Thailand)
  [Currency.VND]: 'vi-VN', // Vietnamese (Vietnam)
  [Currency.MYR]: 'ms-MY', // Malay (Malaysia)
};

export const formatCurrencyAmount = (
  amount: number,
  currency: Currency,
  locale?: string,
): string => {
  const isZeroDecimal = [Currency.IDR, Currency.VND].includes(currency);

  // fallback to en-IN if somehow currency is not in the map
  const determinedLocale =
    locale ?? CURRENCY_TO_LOCALE_MAP[currency] ?? 'en-IN';

  return new Intl.NumberFormat(determinedLocale, {
    style: 'currency',
    currency,
    minimumFractionDigits: isZeroDecimal ? 0 : 2,
    maximumFractionDigits: isZeroDecimal ? 0 : 2,
  }).format(amount);
};

// Meta sales can have multiple conversion events, this function decides it
export const getCpcoNodeForMetaSales = (
  insights: ICampaignInsightDetails,
): { action_type: string; value: string } | undefined => {
  const cpcoCandidates: Array<{ action_type: string; value: string }> = [];

  // 1. Custom conversion actions
  const customConversionNodes = insights?.cost_per_action_type?.filter((item) =>
    item.action_type.startsWith('offsite_conversion.custom.'),
  );
  if (customConversionNodes) cpcoCandidates.push(...customConversionNodes);

  // 2. Standard purchase
  const purchaseNode = insights?.cost_per_action_type?.find(
    (item) => item.action_type === 'purchase',
  );
  if (purchaseNode) cpcoCandidates.push(purchaseNode);

  // 3. Fallback to generic pixel custom
  const pixelCustomNode = insights?.cost_per_action_type?.find(
    (item) => item.action_type === 'offsite_conversion.fb_pixel_custom',
  );
  if (pixelCustomNode) cpcoCandidates.push(pixelCustomNode);

  // 4. Return the node with the lowest value (converted to number)
  const actualCplNode = cpcoCandidates.reduce((min, curr) => {
    const minVal = parseFloat(min.value);
    const currVal = parseFloat(curr.value);
    return currVal < minVal ? curr : min;
  }, cpcoCandidates[0]);

  return actualCplNode;
};

export const getHdVideoUrlsFromPexelsData = (
  videos: IPexelsVideoData[],
): Array<{
  url: string;
  duration: number;
}> => {
  const hdVideoUrls: Array<{ url: string; duration: number }> = [];

  for (const video of videos) {
    const sortedVideoFiles = video.video_files
      .filter((videoFile) => videoFile.quality === 'hd')
      .sort((a, b) => a.width - b.width); // sorting on the basis of width
    const hdVideoUrl = sortedVideoFiles?.[0]?.link;
    if (hdVideoUrl) {
      hdVideoUrls.push({ url: hdVideoUrl, duration: video.duration });
    }
  }

  if (hdVideoUrls.length) return hdVideoUrls;

  // if for some reason the hdVideoUrls doesn't contain value going with the width 720 or above
  for (const video of videos) {
    const filteredVideoFiles = video.video_files
      .filter((item) => item.width >= 720)
      .sort((a, b) => a.width - b.width);
    const hdVideoUrl = filteredVideoFiles?.[0]?.link;
    if (hdVideoUrl) {
      hdVideoUrls.push({ url: hdVideoUrl, duration: video.duration });
    }
  }

  if (hdVideoUrls.length) return hdVideoUrls;

  // final fallback
  for (const video of videos) {
    // taking the first link can be of any size
    const filteredVideoFiles = video.video_files;
    const hdVideoUrl = filteredVideoFiles?.[0]?.link;
    if (hdVideoUrl) {
      hdVideoUrls.push({ url: hdVideoUrl, duration: video.duration });
    }
  }

  return hdVideoUrls;
};

export const isFirestoreTimePast24Hours = (
  firestoreTimestamp?: Timestamp,
): boolean => {
  if (!firestoreTimestamp) return false; // or handle null
  const date = firestoreTimestamp.toDate();
  const now = Date.now(); // milliseconds
  const diffMs = now - date.getTime(); // milliseconds difference
  const diffHours = diffMs / (1000 * 60 * 60); // convert ms → hours
  return diffHours > 24;
};
