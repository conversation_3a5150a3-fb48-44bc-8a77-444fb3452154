{"name": "groweasy-be", "version": "1.0.0", "description": "Backend in Express with Typescript for GrowEasy", "main": "dist", "scripts": {"start": "node dist", "lint": "eslint 'src/**/*.ts'", "dev": "cross-env NODE_OPTIONS=--max-old-space-size=4096 NODE_ENV=development concurrently \"tsc --watch\" \"nodemon dist\"", "build": "NODE_OPTIONS=--max-old-space-size=4096 tsc -p tsconfig.json && npm run copy:static", "test": "echo \"Error: no test specified\" && exit 1", "format": "prettier --write .", "prepare": "husky install", "copy:static": "npm run copy:fonts && npm run copy:audios", "copy:fonts": "cp -r src/modules/canvas/fonts dist/modules/canvas/fonts && cp -r src/modules/ffmpeg/fonts dist/modules/ffmpeg/fonts", "copy:audios": "cp -r src/modules/ffmpeg/audios dist/modules/ffmpeg/audios"}, "keywords": [], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.740.0", "@aws-sdk/client-ses": "^3.744.0", "@aws-sdk/lib-storage": "^3.741.0", "@elevenlabs/elevenlabs-js": "^2.13.0", "@google-cloud/text-to-speech": "^5.3.0", "@langchain/community": "^0.3.47", "@langchain/core": "^0.3.61", "@langchain/openai": "^0.5.16", "@remotion/lambda": "4.0.280", "@remotion/renderer": "4.0.280", "aws-sdk": "^2.1515.0", "axios": "^1.9.0", "axios-retry": "^4.5.0", "bunyan": "^1.8.15", "canvas": "^3.1.2", "cheerio": "^1.0.0", "compression": "^1.7.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^11.11.0", "fluent-ffmpeg": "^2.1.3", "googleapis": "^137.1.0", "libphonenumber-js": "^1.11.3", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "newrelic": "^11.22.0", "puppeteer": "^24.3.0", "razorpay": "^2.9.2", "react": "^19.0.0", "react-dom": "^19.0.0", "sharp": "^0.33.5", "stripe": "^15.11.0", "xendit-node": "^6.3.0", "zod": "^3.24.1"}, "devDependencies": {"@types/bunyan": "^1.8.11", "@types/compression": "^1.7.5", "@types/cors": "^2.8.16", "@types/express": "^4.17.20", "@types/fluent-ffmpeg": "^2.1.26", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.10", "@types/node": "^20.8.10", "@typescript-eslint/eslint-plugin": "^6.4.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^8.0.1", "eslint-config-prettier": "^9.0.0", "eslint-config-standard-with-typescript": "^39.1.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-promise": "^6.0.0", "husky": "^8.0.3", "lint-staged": "14.0.1", "prettier": "^3.0.3", "ts-node": "^10.9.1", "typescript": "*"}, "lint-staged": {"*.{js,ts}": "eslint --cache --fix", "*.{ts,js,css,md}": "prettier --write"}}