# Gemini Imagen 4 API Integration

This module provides integration with Google's Gemini Imagen 4 API for high-quality image generation.

## Features

- ✨ **Imagen 4 Support**: Latest Google Imagen 4 models including Standard, Ultra, and Fast variants
- 🎨 **Multiple Aspect Ratios**: Support for 1:1, 9:16, 16:9, 3:4, and 4:3 aspect ratios
- 🖼️ **High Quality**: Support for 1K and 2K image generation
- 👥 **Person Generation Control**: Configure whether to allow person generation in images
- 🔄 **OpenAI Compatible**: Returns data in OpenAI-compatible format for easy migration
- 📝 **Comprehensive Logging**: Detailed logging for debugging and monitoring
- ⚡ **Multiple Models**: Support for standard, ultra, and fast generation models

## Setup

### 1. Environment Variables

Set your Gemini API key:

```bash
export GEMINI_API_KEY="your-api-key-here"
```

### 2. API Access

1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Create a new API key
3. Ensure billing is set up for your Google Cloud project
4. Enable the Generative AI API

## Usage

### Basic Usage (OpenAI Compatible)

```typescript
import { generateAiBannerImage } from './modules/gemini';

const result = await generateAiBannerImage({
  prompt: 'A beautiful sunset over mountains, digital art style',
  aspect_ratio: '16x9',
  quality: 'high',
  numberOfImages: 1,
  model: 'imagen-4.0-generate-001',
});

// Save the image
const buffer = Buffer.from(result.data[0].b64_json, 'base64');
fs.writeFileSync('generated-image.jpeg', buffer);
```

### Advanced Usage

```typescript
import {
  generateImageWithGeminiImagen4,
  IGeminiImageGenParams,
} from './modules/gemini';

const params: IGeminiImageGenParams = {
  prompt: 'A professional business portrait, modern office background',
  aspect_ratio: '3:4',
  numberOfImages: 2,
  imageSize: '2K',
  personGeneration: 'ALLOW_ADULT',
  outputMimeType: 'image/jpeg',
  model: 'imagen-4.0-ultra-generate-001',
};

const result = await generateImageWithGeminiImagen4(params);

// Process multiple images
result.data.forEach((image, index) => {
  const buffer = Buffer.from(image.b64_json, 'base64');
  fs.writeFileSync(`image-${index + 1}.jpeg`, buffer);
});
```

## API Reference

### generateAiBannerImage

Legacy function that maintains OpenAI compatibility.

**Parameters:**

- `prompt` (string): The text description of the image to generate
- `aspect_ratio` (optional): '1x1' | '9x16' | '16x9' | '3x4' | '4x3' (default: '1x1')
- `quality` (optional): 'high' | 'medium' | 'low' (default: 'medium')
- `numberOfImages` (optional): Number of images to generate (1-4, default: 1)
- `model` (optional): Imagen model to use (default: 'imagen-4.0-generate-001')

### generateImageWithGeminiImagen4

Advanced function with full Imagen 4 parameter support.

**Parameters:**

- `prompt` (string): The text description of the image to generate
- `aspect_ratio` (optional): '1:1' | '9:16' | '16:9' | '3:4' | '4:3' (default: '1:1')
- `numberOfImages` (optional): Number of images to generate (1-4, default: 1)
- `imageSize` (optional): '1K' | '2K' (default: '1K')
- `personGeneration` (optional): 'DONT_ALLOW' | 'ALLOW_ADULT' | 'ALLOW_ALL' (default: 'ALLOW_ADULT')
- `outputMimeType` (optional): 'image/jpeg' | 'image/png' (default: 'image/jpeg')
- `model` (optional): Imagen model variant (default: 'imagen-4.0-generate-001')

## Available Models

- **imagen-4.0-generate-001**: Standard Imagen 4 model (recommended)
- **imagen-4.0-ultra-generate-001**: Ultra-high quality model (slower, more expensive)
- **imagen-4.0-fast-generate-001**: Fast generation model (lower quality, faster)

## Aspect Ratios

- **1:1**: Square images (1024x1024)
- **9:16**: Vertical/Portrait (suitable for mobile, stories)
- **16:9**: Horizontal/Landscape (suitable for banners, headers)
- **3:4**: Portrait format (suitable for prints)
- **4:3**: Landscape format (traditional photo)

## Error Handling

The functions provide detailed error handling:

```typescript
try {
  const result = await generateImageWithGeminiImagen4(params);
  // Process result
} catch (error) {
  if (error.message.includes('Invalid or missing GEMINI_API_KEY')) {
    // Handle API key issues
  } else if (error.message.includes('Rate limit exceeded')) {
    // Handle rate limiting
  } else if (error.message.includes('Access denied')) {
    // Handle permission/billing issues
  }
}
```

## Testing

Run the test script to verify your setup:

```bash
npx ts-node src/scripts/test-gemini-imagen.ts
```

This will:

1. Test basic image generation
2. Test advanced parameters
3. Test different aspect ratios
4. Save generated images to `test-images/` directory

## Prompt Engineering Tips

### For Best Results:

1. **Be Descriptive**: Use detailed adjectives and specific descriptions
2. **Specify Style**: Include style keywords like "digital art", "photography", "watercolor"
3. **Set Context**: Describe the environment, lighting, and mood
4. **Quality Modifiers**: Use terms like "high quality", "professional", "detailed"

### Example Prompts:

```typescript
// For photography
'Professional portrait of a business person, studio lighting, shallow depth of field, high quality';

// For art
'Abstract geometric pattern in blue and gold, minimalist style, clean composition';

// For logos
'Modern tech company logo, simple geometric design, blue and white colors, professional';

// For illustrations
"Cartoon illustration of a happy robot, friendly expression, colorful, children's book style";
```

### Text in Images:

Imagen 4 can generate text within images:

```typescript
await generateImageWithGeminiImagen4({
  prompt:
    'A poster with the text "SUMMER SALE" in bold red letters, modern design',
  aspect_ratio: '9:16',
  imageSize: '2K',
});
```

**Text Guidelines:**

- Keep text under 25 characters for best results
- Use clear font style descriptions
- Specify text placement when needed
- Multiple short phrases work better than long sentences

## Rate Limits and Costs

- **Free Tier**: Limited requests per day
- **Paid Tier**: Higher rate limits, check [Google AI Pricing](https://ai.google.dev/pricing)
- **Rate Limits**: Varies by model and tier
- **Image Sizes**: 2K images cost more than 1K images

## Troubleshooting

### Common Issues:

1. **API Key Error**: Ensure `GEMINI_API_KEY` is set correctly
2. **Billing Error**: Verify billing is enabled in Google Cloud Console
3. **Rate Limit**: Implement exponential backoff for retries
4. **Invalid Prompt**: Ensure prompts meet content policy guidelines
5. **Region Restrictions**: Some features may not be available in all regions

### Debug Logging:

The module includes comprehensive logging. Check your logs for detailed error information.

## Migration from OpenAI

The `generateAiBannerImage` function provides OpenAI DALL-E compatible interface:

```typescript
// Before (OpenAI)
const openaiResult = await openai.images.generate({
  prompt: 'A sunset',
  size: '1024x1024',
  n: 1,
});

// After (Gemini Imagen 4)
const geminiResult = await generateAiBannerImage({
  prompt: 'A sunset',
  aspect_ratio: '1x1',
  numberOfImages: 1,
});

// Same response format
const base64Image = geminiResult.data[0].b64_json;
```

## License

This code is part of the GroweEasy backend system. See the main project license for details.
