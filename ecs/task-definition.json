{"taskDefinitionArn": "arn:aws:ecs:ap-south-1:************:task-definition/groweasy-be:1", "containerDefinitions": [{"name": "groweasy-be", "image": "************.dkr.ecr.ap-south-1.amazonaws.com/groweasy-be:latest", "cpu": 0, "portMappings": [{"name": "groweasy-be-4000-tcp", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "PORT", "value": "3000"}, {"name": "NODE_ENV", "value": "production"}, {"name": "GROWEASY_PAGE_ID", "value": "***************"}, {"name": "BANNERBOT_PAGE_ID", "value": "***************"}, {"name": "AD_GLOBAL_AI_PAGE_ID", "value": "***************"}, {"name": "GROWEASY_APP_ID", "value": "***************"}, {"name": "GROWEASY_SAMPLE_LEADGEN_FORM_ID", "value": "****************"}, {"name": "BANNERBOT_SAMPLE_LEADGEN_FORM_ID", "value": "****************"}, {"name": "GROWEASY_AD_ACCOUNT_ID", "value": "***************"}, {"name": "GROWEASY_AD_GLOBAL_AI_AD_ACCOUNT_ID", "value": "****************"}, {"name": "GROWEASY_INSTAGRAM_ID", "value": "*****************"}, {"name": "BANNERBOT_INSTAGRAM_ID", "value": "****************"}, {"name": "LEADS_WEBHOOK_VERIFICATION_TOKEN", "value": "bannerbotToGrowEasyTejAndVarun"}, {"name": "RAZORPAY_TEST_KEY_ID", "value": "rzp_test_eeEFoCcDL585gb"}, {"name": "RAZORPAY_PROD_KEY_ID", "value": "rzp_live_gHyM5yLiqAKZC5"}, {"name": "AWS_LAMBDA_CRON_TOKEN", "value": "aws1Lambda2Cr0n3T0ken4For5Gr0w3asy"}, {"name": "RAZORPAY_WEBHOOK_VERIFICATION_TOKEN", "value": "raz0rP1yWebH00k$3234%^&*^"}, {"name": "BCN_WHATSAPP_PH_NO", "value": "************"}, {"name": "GOOGLE_ADS_MANAGER_ACCOUNT_ID", "value": "**********"}, {"name": "GOOGLE_ADS_OAUTH_CLIENT_ID", "value": "************-cuvcfdp2u2c6fgre2j66om6qvgpk8e7o.apps.googleusercontent.com"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/groweasy-be", "awslogs-region": "ap-south-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "secrets": [{"name": "GROWEASY_LONG_LIVED_ACESS_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:GROWEASY_LONG_LIVED_ACESS_TOKEN::"}, {"name": "GROWEASY_APP_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:GROWEASY_APP_SECRET::"}, {"name": "OPEN_AI_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:OPEN_AI_API_KEY::"}, {"name": "UNSPLASH_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:UNSPLASH_API_KEY::"}, {"name": "GROWEASY_FIREBASE_SERVICE_ACC_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:GROWEASY_FIREBASE_SERVICE_ACC_PRIVATE_KEY::"}, {"name": "AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:AWS_ACCESS_KEY_ID::"}, {"name": "AWS_REGION", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:AWS_REGION::"}, {"name": "AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:AWS_SECRET_ACCESS_KEY::"}, {"name": "RAZORPAY_TEST_KEY_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:RAZORPAY_TEST_KEY_SECRET::"}, {"name": "RAZORPAY_PROD_KEY_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:RAZORPAY_PROD_KEY_SECRET::"}, {"name": "STRIPE_PROD_KEY_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:STRIPE_PROD_KEY_SECRET::"}, {"name": "STRIPE_WEBHOOK_ENDPOINT_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:STRIPE_WEBHOOK_ENDPOINT_SECRET::"}, {"name": "PEXELS_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:PEXELS_API_KEY::"}, {"name": "NEW_RELIC_LICENSE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:NEW_RELIC_LICENSE_KEY::"}, {"name": "GOOGLE_ADS_REFRESH_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:GOOGLE_ADS_REFRESH_TOKEN::"}, {"name": "AZURE_GPT_4O_MINI_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:AZURE_GPT_4O_MINI_API_KEY::"}, {"name": "ZENDOT_RAZORPAY_PROD_KEY_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:ZENDOT_RAZORPAY_PROD_KEY_SECRET::"}, {"name": "GOOGLE_ADS_DEVELOPER_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:GOOGLE_ADS_DEVELOPER_TOKEN::"}, {"name": "GOOGLE_ADS_OAUTH_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:GOOGLE_ADS_OAUTH_CLIENT_SECRET::"}, {"name": "BANNERBOT_FIREBASE_SERVICE_ACC_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:BANNERBOT_FIREBASE_SERVICE_ACC_PRIVATE_KEY::"}, {"name": "GROWEASY_AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:GROWEASY_AWS_ACCESS_KEY_ID::"}, {"name": "GROWEASY_AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:GROWEASY_AWS_SECRET_ACCESS_KEY::"}, {"name": "FREEPIK_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:FREEPIK_API_KEY::"}, {"name": "REMOTION_AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:REMOTION_AWS_ACCESS_KEY_ID::"}, {"name": "REMOTION_AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:REMOTION_AWS_SECRET_ACCESS_KEY::"}, {"name": "IDEOGRAM_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:IDEOGRAM_API_KEY::"}, {"name": "XENDIT_PROD_SECRET_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:XENDIT_PROD_SECRET_API_KEY::"}, {"name": "XENDIT_WEBHOOK_VERIFICATION_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:XENDIT_WEBHOOK_VERIFICATION_TOKEN::"}, {"name": "INTERAKT_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:INTERAKT_API_KEY::"}, {"name": "ELEVENLABS_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:ELEVENLABS_API_KEY::"}, {"name": "STORYBLOCKS_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:STORYBLOCKS_PRIVATE_KEY::"}, {"name": "STORYBLOCKS_PUBLIC_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:STORYBLOCKS_PUBLIC_KEY::"}, {"name": "TIKTOK_LONG_LIVED_ACCESS_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:TIKTOK_LONG_LIVED_ACCESS_TOKEN::"}, {"name": "GROWEASY_TIKTOK_BE_SHARED_SECRET_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:GROWEASY_TIKTOK_BE_SHARED_SECRET_TOKEN::"}, {"name": "GEMINI_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-south-1:************:secret:groweasy/prod/groweasy-be-o3tct8:GEMINI_API_KEY::"}]}], "family": "groweasy-be", "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2023-10-31T18:41:43.604Z", "registeredBy": "arn:aws:iam::************:root", "tags": []}